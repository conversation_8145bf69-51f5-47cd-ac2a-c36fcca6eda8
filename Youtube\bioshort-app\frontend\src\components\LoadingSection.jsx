import React from 'react'

const LoadingSection = ({ progress, text }) => {
  return (
    <section className="glass-card p-8 mb-8 text-center fade-in">
      <div className="max-w-md mx-auto">
        {/* Loading Spinner */}
        <div className="loading-spinner mx-auto mb-6 w-16 h-16"></div>
        
        {/* Title */}
        <h3 className="text-white text-2xl font-bold mb-4">
          Creating Your Video...
        </h3>
        
        {/* Progress Text */}
        <p className="text-blue-200 text-lg mb-6">
          {text}
        </p>
        
        {/* Progress Bar */}
        <div className="w-full bg-white/20 rounded-full h-3 mb-4">
          <div 
            className="bg-gradient-to-r from-blue-500 to-purple-600 h-3 rounded-full transition-all duration-500 ease-out"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
        
        {/* Progress Percentage */}
        <p className="text-blue-300 text-sm">
          {progress}% Complete
        </p>
        
        {/* Process Steps */}
        <div className="mt-8 grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
          <div className={`p-3 rounded-lg border ${progress >= 25 ? 'bg-green-500/20 border-green-400 text-green-200' : 'bg-white/10 border-white/20 text-blue-300'}`}>
            <div className="text-lg mb-1">📝</div>
            <div>Story</div>
          </div>
          <div className={`p-3 rounded-lg border ${progress >= 50 ? 'bg-green-500/20 border-green-400 text-green-200' : 'bg-white/10 border-white/20 text-blue-300'}`}>
            <div className="text-lg mb-1">🎨</div>
            <div>Images</div>
          </div>
          <div className={`p-3 rounded-lg border ${progress >= 75 ? 'bg-green-500/20 border-green-400 text-green-200' : 'bg-white/10 border-white/20 text-blue-300'}`}>
            <div className="text-lg mb-1">🎤</div>
            <div>Voice</div>
          </div>
          <div className={`p-3 rounded-lg border ${progress >= 100 ? 'bg-green-500/20 border-green-400 text-green-200' : 'bg-white/10 border-white/20 text-blue-300'}`}>
            <div className="text-lg mb-1">🎬</div>
            <div>Video</div>
          </div>
        </div>
        
        {/* Estimated Time */}
        <div className="mt-6 p-3 bg-blue-500/20 rounded-lg border border-blue-400/30">
          <p className="text-blue-200 text-sm">
            ⏱️ Estimated time: 30-60 seconds
          </p>
        </div>
      </div>
    </section>
  )
}

export default LoadingSection
