[2025-06-17T17:45:12.368+0000] {processor.py:157} INFO - Started process (PID=199) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:45:12.371+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-17T17:45:12.373+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:45:12.373+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:45:12.454+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:45:12.506+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:45:12.506+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:45:12.620+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.260 seconds
[2025-06-17T17:45:43.071+0000] {processor.py:157} INFO - Started process (PID=219) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:45:43.073+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-17T17:45:43.075+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:45:43.074+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:45:43.113+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:45:43.159+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:45:43.159+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:45:43.225+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.162 seconds
[2025-06-17T17:46:13.972+0000] {processor.py:157} INFO - Started process (PID=242) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:46:13.974+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-17T17:46:13.975+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:46:13.975+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:46:14.018+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:46:14.066+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:46:14.066+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:46:14.160+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.193 seconds
[2025-06-17T17:46:44.624+0000] {processor.py:157} INFO - Started process (PID=265) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:46:44.627+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-17T17:46:44.631+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:46:44.631+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:46:44.706+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:46:44.765+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:46:44.765+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:46:45.012+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.393 seconds
[2025-06-17T17:47:15.485+0000] {processor.py:157} INFO - Started process (PID=288) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:47:15.487+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-17T17:47:15.491+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:47:15.490+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:47:15.539+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:47:15.602+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:47:15.602+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:47:15.691+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.212 seconds
[2025-06-17T17:47:45.961+0000] {processor.py:157} INFO - Started process (PID=311) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:47:45.963+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-17T17:47:45.966+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:47:45.965+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:47:46.040+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:47:46.086+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:47:46.086+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:47:46.166+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.212 seconds
[2025-06-17T17:48:16.349+0000] {processor.py:157} INFO - Started process (PID=334) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:48:16.351+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-17T17:48:16.353+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:48:16.353+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:48:16.409+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:48:16.461+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:48:16.460+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:48:16.543+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.201 seconds
[2025-06-17T17:48:47.165+0000] {processor.py:157} INFO - Started process (PID=357) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:48:47.167+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-17T17:48:47.169+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:48:47.169+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:48:47.215+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:48:47.263+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:48:47.263+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:48:47.358+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.203 seconds
[2025-06-17T17:49:18.033+0000] {processor.py:157} INFO - Started process (PID=380) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:49:18.037+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-17T17:49:18.040+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:49:18.039+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:49:18.089+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:49:18.143+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:49:18.143+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:49:18.228+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.203 seconds
[2025-06-17T17:49:48.825+0000] {processor.py:157} INFO - Started process (PID=403) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:49:48.828+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-17T17:49:48.830+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:49:48.830+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:49:48.883+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:49:48.925+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:49:48.924+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:49:48.986+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.169 seconds
[2025-06-17T17:50:19.803+0000] {processor.py:157} INFO - Started process (PID=426) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:50:19.804+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-17T17:50:19.806+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:50:19.805+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:50:19.840+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:50:19.888+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:50:19.887+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:50:19.958+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.161 seconds
[2025-06-17T17:50:50.755+0000] {processor.py:157} INFO - Started process (PID=451) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:50:50.758+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-17T17:50:50.761+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:50:50.760+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:50:50.826+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:50:50.881+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:50:50.881+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:50:50.930+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:50:50.929+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-17T09:00:00+00:00, run_after=2025-06-18T09:00:00+00:00
[2025-06-17T17:50:51.008+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.265 seconds
[2025-06-17T17:51:21.278+0000] {processor.py:157} INFO - Started process (PID=474) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:51:21.280+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-17T17:51:21.282+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:51:21.282+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:51:21.328+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:51:21.388+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:51:21.388+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:51:21.431+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:51:21.431+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-17T09:00:00+00:00, run_after=2025-06-18T09:00:00+00:00
[2025-06-17T17:51:21.476+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.205 seconds
[2025-06-17T17:51:52.387+0000] {processor.py:157} INFO - Started process (PID=489) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:51:52.388+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-17T17:51:52.390+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:51:52.390+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:51:52.437+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:51:52.568+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:51:52.568+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:51:52.692+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:51:52.692+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-17T09:00:00+00:00, run_after=2025-06-18T09:00:00+00:00
[2025-06-17T17:51:52.867+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.486 seconds
[2025-06-17T17:52:23.768+0000] {processor.py:157} INFO - Started process (PID=512) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:52:23.771+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-17T17:52:23.775+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:52:23.774+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:52:23.816+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:52:23.860+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:52:23.860+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:52:23.894+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:52:23.894+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-17T09:00:00+00:00, run_after=2025-06-18T09:00:00+00:00
[2025-06-17T17:52:23.939+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.178 seconds
[2025-06-17T17:52:54.734+0000] {processor.py:157} INFO - Started process (PID=535) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:52:54.737+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-17T17:52:54.742+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:52:54.741+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:52:54.825+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:52:54.914+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:52:54.914+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:52:54.968+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:52:54.968+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-17T09:00:00+00:00, run_after=2025-06-18T09:00:00+00:00
[2025-06-17T17:52:55.044+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.320 seconds
[2025-06-17T17:53:25.761+0000] {processor.py:157} INFO - Started process (PID=558) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:53:25.764+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-17T17:53:25.766+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:53:25.766+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:53:25.820+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:53:25.878+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:53:25.878+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:53:25.931+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:53:25.930+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-17T09:00:00+00:00, run_after=2025-06-18T09:00:00+00:00
[2025-06-17T17:53:26.018+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.265 seconds
[2025-06-17T17:53:57.028+0000] {processor.py:157} INFO - Started process (PID=582) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:53:57.030+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-17T17:53:57.033+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:53:57.032+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:53:57.078+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:53:57.134+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:53:57.134+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:53:57.182+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:53:57.181+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-17T09:00:00+00:00, run_after=2025-06-18T09:00:00+00:00
[2025-06-17T17:53:57.237+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.214 seconds
[2025-06-17T17:54:27.415+0000] {processor.py:157} INFO - Started process (PID=605) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:54:27.418+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-17T17:54:27.419+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:54:27.419+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:54:27.495+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-17T17:54:27.566+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:54:27.565+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:54:27.632+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:54:27.631+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-17T09:00:00+00:00, run_after=2025-06-18T09:00:00+00:00
[2025-06-17T17:54:27.676+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.269 seconds
