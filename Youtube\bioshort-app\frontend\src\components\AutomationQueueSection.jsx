import React, { useState, useEffect } from 'react'



const AutomationQueueSection = () => {
  const [queue, setQueue] = useState([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)
  const [showAddForm, setShowAddForm] = useState(false)
  const [newPerson, setNewPerson] = useState({
    name: '',
    title_language: 'english',
    description_language: 'english',
    audio_language: 'hindi',
    voice_type: 'hindi_male'
  })


  useEffect(() => {
    fetchQueue()
  }, [])

  const fetchQueue = async () => {
    try {
      setIsLoading(true)
      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL
      const response = await fetch(`${apiBaseUrl}/queue`)
      const result = await response.json()

      if (result.success) {
        setQueue(result.queue)
      } else {
        setError(result.error)
      }
    } catch (error) {
      console.error('Error fetching queue:', error)
      setError(error.message)
    } finally {
      setIsLoading(false)
    }
  }



  const addToQueue = async () => {
    if (!newPerson.name.trim()) {
      setError('Person name is required')
      return
    }

    try {
      setIsLoading(true)
      setError(null)

      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL
      const response = await fetch(`${apiBaseUrl}/queue/add`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newPerson)
      })

      const result = await response.json()

      if (result.success) {
        setNewPerson({
          name: '',
          title_language: 'english',
          description_language: 'english',
          audio_language: 'hindi',
          voice_type: 'hindi_male'
        })
        setShowAddForm(false)
        fetchQueue()
      } else {
        setError(result.error)
      }
    } catch (error) {
      console.error('Error adding to queue:', error)
      setError(error.message)
    } finally {
      setIsLoading(false)
    }
  }

  const removeFromQueue = async (personId) => {
    try {
      setIsLoading(true)
      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL
      const response = await fetch(`${apiBaseUrl}/queue/remove/${personId}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (result.success) {
        fetchQueue()
      } else {
        setError(result.error)
      }
    } catch (error) {
      console.error('Error removing from queue:', error)
      setError(error.message)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'text-yellow-400 bg-yellow-400/20'
      case 'processing': return 'text-blue-400 bg-blue-400/20'
      case 'completed': return 'text-green-400 bg-green-400/20'
      case 'failed': return 'text-red-400 bg-red-400/20'
      default: return 'text-gray-400 bg-gray-400/20'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending': return '⏳'
      case 'processing': return '🔄'
      case 'completed': return '✅'
      case 'failed': return '❌'
      default: return '❓'
    }
  }

  const pendingCount = queue.filter(item => item.status === 'pending').length
  const processingCount = queue.filter(item => item.status === 'processing').length
  const completedCount = queue.filter(item => item.status === 'completed').length

  return (
    <div className="bg-slate-800/90 backdrop-blur-xl rounded-3xl shadow-2xl border border-slate-700/50 p-8 fade-in relative overflow-hidden">
      {/* Background effect */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-blue-500/5"></div>
      
      <div className="relative z-10">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-white text-2xl font-bold flex items-center gap-2">
            🤖 Automation Queue
            <span className="text-sm font-normal text-purple-300">
              (Daily Auto-Upload)
            </span>
          </h3>
          
          <div className="flex items-center gap-3">
            <button
              onClick={() => setShowAddForm(!showAddForm)}
              className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2"
            >
              <span>➕</span>
              Add Person
            </button>
            <button
              onClick={fetchQueue}
              disabled={isLoading}
              className={`p-2 rounded-full transition-all duration-200 ${
                isLoading 
                  ? 'bg-purple-500/30 text-purple-300 animate-spin' 
                  : 'bg-purple-500/20 text-purple-300 hover:bg-purple-500/40 hover:text-white'
              }`}
              title="Refresh queue"
            >
              🔄
            </button>
          </div>
        </div>

        {/* Queue Stats */}
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-yellow-400">{pendingCount}</div>
            <div className="text-yellow-300 text-sm">Pending</div>
          </div>
          <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-blue-400">{processingCount}</div>
            <div className="text-blue-300 text-sm">Processing</div>
          </div>
          <div className="bg-green-500/10 border border-green-500/30 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-green-400">{completedCount}</div>
            <div className="text-green-300 text-sm">Completed</div>
          </div>
        </div>

        {/* Add Person Form */}
        {showAddForm && (
          <div className="bg-slate-700/30 border border-slate-600/50 rounded-lg p-6 mb-6">
            <h4 className="text-white font-medium mb-4">Add Person to Automation Queue</h4>
            
            <div className="space-y-4">
              <div>
                <label className="block text-slate-300 font-medium mb-2">Person Name *</label>
                <input
                  type="text"
                  value={newPerson.name}
                  onChange={(e) => setNewPerson(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-purple-400 focus:outline-none transition-colors"
                  placeholder="Enter person name..."
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-slate-300 font-medium mb-2">Title Language</label>
                  <select
                    value={newPerson.title_language}
                    onChange={(e) => setNewPerson(prev => ({ ...prev, title_language: e.target.value }))}
                    className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-4 py-3 text-white focus:border-purple-400 focus:outline-none transition-colors"
                  >
                    <option value="english">🇺🇸 English</option>
                    <option value="hindi">🇮🇳 Hindi</option>
                  </select>
                </div>

                <div>
                  <label className="block text-slate-300 font-medium mb-2">Description Language</label>
                  <select
                    value={newPerson.description_language}
                    onChange={(e) => setNewPerson(prev => ({ ...prev, description_language: e.target.value }))}
                    className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-4 py-3 text-white focus:border-purple-400 focus:outline-none transition-colors"
                  >
                    <option value="english">🇺🇸 English</option>
                    <option value="hindi">🇮🇳 Hindi</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-slate-300 font-medium mb-2">Audio Language</label>
                  <select
                    value={newPerson.audio_language}
                    onChange={(e) => {
                      const audioLang = e.target.value
                      setNewPerson(prev => ({
                        ...prev,
                        audio_language: audioLang,
                        voice_type: audioLang === 'hindi' ? 'hindi_male' : 'english_male'
                      }))
                    }}
                    className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-4 py-3 text-white focus:border-purple-400 focus:outline-none transition-colors"
                  >
                    <option value="hindi">🇮🇳 Hindi</option>
                    <option value="english">🇺🇸 English</option>
                  </select>
                </div>

                <div>
                  <label className="block text-slate-300 font-medium mb-2">Voice Type</label>
                  <select
                    value={newPerson.voice_type}
                    onChange={(e) => setNewPerson(prev => ({ ...prev, voice_type: e.target.value }))}
                    className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-4 py-3 text-white focus:border-purple-400 focus:outline-none transition-colors"
                  >
                    {newPerson.audio_language === 'hindi' ? (
                      <>
                        <option value="hindi_male">👨 Hindi Male</option>
                        <option value="hindi_female">👩 Hindi Female</option>
                      </>
                    ) : (
                      <>
                        <option value="english_male">👨 English Male</option>
                        <option value="english_female">👩 English Female</option>
                      </>
                    )}
                  </select>
                </div>
              </div>

              <div className="flex gap-3">
                <button
                  onClick={addToQueue}
                  disabled={isLoading || !newPerson.name.trim()}
                  className="bg-purple-600 hover:bg-purple-700 disabled:bg-slate-600 disabled:cursor-not-allowed text-white px-6 py-3 rounded-lg font-medium transition-colors"
                >
                  {isLoading ? 'Adding...' : 'Add to Queue'}
                </button>
                <button
                  onClick={() => setShowAddForm(false)}
                  className="bg-slate-700 hover:bg-slate-600 text-white px-6 py-3 rounded-lg font-medium transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-900/30 border border-red-500/50 rounded-xl p-4 mb-6">
            <div className="flex items-center gap-2 text-red-300">
              <span>❌</span>
              <span className="font-medium">Error:</span>
            </div>
            <p className="text-red-200 mt-1">{error}</p>
          </div>
        )}

        {/* Queue List */}
        <div className="space-y-4">
          {isLoading && queue.length === 0 ? (
            <div className="text-center py-8">
              <div className="loading-spinner w-8 h-8 mx-auto mb-4"></div>
              <p className="text-slate-400">Loading queue...</p>
            </div>
          ) : queue.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-6xl mb-4">📭</div>
              <p className="text-slate-400 text-lg">Queue is empty</p>
              <p className="text-slate-500 text-sm">Add people to start automated video generation</p>
            </div>
          ) : (
            queue.map((person, index) => (
              <div key={person.id} className="bg-slate-700/30 border border-slate-600/50 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="text-2xl font-bold text-slate-400">#{index + 1}</div>
                    <div>
                      <h4 className="text-white font-medium">{person.name}</h4>
                      <div className="flex items-center gap-4 text-sm text-slate-400">
                        <span>Title: {person.title_language === 'english' ? '🇺🇸 English' : '🇮🇳 Hindi'}</span>
                        <span>Desc: {person.description_language === 'english' ? '🇺🇸 English' : '🇮🇳 Hindi'}</span>
                        <span>Audio: {person.audio_language === 'english' ? '🇺🇸 English' : '🇮🇳 Hindi'} {person.voice_type && person.voice_type.includes('male') ? '👨' : '👩'}</span>
                        <span>Added: {new Date(person.created_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium flex items-center gap-1 ${getStatusColor(person.status)}`}>
                      <span>{getStatusIcon(person.status)}</span>
                      {person.status}
                    </span>
                    
                    {person.video_url && (
                      <a
                        href={person.video_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded-lg text-sm transition-colors"
                      >
                        📺 YouTube
                      </a>
                    )}
                    
                    {person.status === 'pending' && (
                      <button
                        onClick={() => removeFromQueue(person.id)}
                        className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded-lg text-sm transition-colors"
                      >
                        Remove
                      </button>
                    )}
                  </div>
                </div>
                
                {person.error_message && (
                  <div className="mt-3 text-red-300 text-sm bg-red-900/20 rounded p-2">
                    Error: {person.error_message}
                  </div>
                )}
              </div>
            ))
          )}
        </div>



        {/* Info Box */}
        <div className="mt-6 bg-blue-900/20 border border-blue-500/30 rounded-lg p-4">
          <h4 className="text-blue-300 font-medium mb-2">🤖 How Automation Works</h4>
          <ul className="text-blue-200 text-sm space-y-1">
            <li>• Airflow runs daily at 9 AM to process one person from the queue</li>
            <li>• Videos are automatically uploaded to YouTube as private</li>
            <li>• You can manually make them public from your YouTube dashboard</li>
            <li>• Each person can have different title and description languages</li>
            <li>• Processing stops when the queue is empty</li>
          </ul>
        </div>
      </div>
    </div>
  )
}

export default AutomationQueueSection
