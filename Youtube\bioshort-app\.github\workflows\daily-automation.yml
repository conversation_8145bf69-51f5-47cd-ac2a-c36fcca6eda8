name: Daily Video Generation

on:
  schedule:
    # Run daily at 9:00 AM UTC (adjust timezone as needed)
    - cron: '0 9 * * *'
  workflow_dispatch: # Allow manual triggering

jobs:
  trigger-automation:
    runs-on: ubuntu-latest
    steps:
      - name: Trigger Queue Processing
        run: |
          # Replace with your actual Render URL when deployed
          # This will trigger the automation to process one person from the queue
          curl -X POST https://your-app.onrender.com/api/process-queue || echo "No people in queue or service unavailable"
          
      - name: Check Queue Status
        run: |
          # Check current queue status
          curl https://your-app.onrender.com/api/queue || echo "Could not check queue status"
