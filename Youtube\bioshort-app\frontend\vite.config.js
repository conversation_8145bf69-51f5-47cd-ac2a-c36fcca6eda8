import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react({
    // Enable fast refresh
    fastRefresh: true,
  })],
  server: {
    host: '0.0.0.0',
    port: 5173,
    // Enhanced hot reload settings
    hmr: {
      overlay: true,
      port: 5173,
    },
    // Watch for changes
    watch: {
      usePolling: true,
      interval: 100,
    },
    proxy: {
      '/api': {
        target: 'http://localhost:5000',
        changeOrigin: true,
        secure: false,
      }
    }
  },
  // Optimize dependencies for faster reload
  optimizeDeps: {
    include: ['react', 'react-dom', 'react-router-dom'],
  },
})
