import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vite.dev/config/
export default defineConfig(({ command, mode }) => {
  // Load env file from root directory (parent of frontend)
  const rootDir = path.resolve(__dirname, '..')
  const env = loadEnv(mode, rootDir, '')

  return {
  plugins: [react({
    // Enable fast refresh
    fastRefresh: true,
  })],
  server: {
    host: '0.0.0.0',
    port: 5173,
    // Enhanced hot reload settings
    hmr: {
      overlay: true,
      port: 5173,
    },
    // Watch for changes
    watch: {
      usePolling: true,
      interval: 100,
    },
    proxy: {
      '/api': {
        target: 'http://localhost:5000',
        changeOrigin: true,
        secure: false,
      }
    }
  },
  // Optimize dependencies for faster reload
  optimizeDeps: {
    include: ['react', 'react-dom', 'react-router-dom'],
  },

  // Configure environment variables
  envDir: rootDir, // Load .env files from root directory
  envPrefix: ['VITE_', 'FRONTEND_'], // Allow both VITE_ and FRONTEND_ prefixed variables
  }
})
