import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig(({ command, mode }) => {
  // Load env file based on `mode` in the current working directory.
  // Set the third parameter to '' to load all env regardless of the `VITE_` prefix.
  const env = loadEnv(mode, process.cwd(), '')

  return {
  plugins: [react({
    // Enable fast refresh
    fastRefresh: true,
  })],
  server: {
    host: '0.0.0.0',
    port: 5173,
    // Enhanced hot reload settings
    hmr: {
      overlay: true,
      port: 5173,
    },
    // Watch for changes
    watch: {
      usePolling: true,
      interval: 100,
    },
    proxy: {
      '/api': {
        target: 'http://localhost:5000',
        changeOrigin: true,
        secure: false,
      }
    }
  },
  // Optimize dependencies for faster reload
  optimizeDeps: {
    include: ['react', 'react-dom', 'react-router-dom'],
  },

  // Environment variables configuration
  define: {
    // Make environment variables available at build time
    __VITE_API_BASE_URL__: JSON.stringify(env.VITE_API_BASE_URL || 'http://localhost:5000/api'),
  },

  // Ensure environment variables are properly loaded
  envPrefix: ['VITE_', 'NODE_'],
  }
})
