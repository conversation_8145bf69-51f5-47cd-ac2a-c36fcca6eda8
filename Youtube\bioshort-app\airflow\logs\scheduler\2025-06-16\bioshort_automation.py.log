[2025-06-16T19:12:32.554+0000] {processor.py:157} INFO - Started process (PID=191) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:12:32.556+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:12:32.558+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:12:32.558+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:12:32.622+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:12:32.644+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:12:32.644+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:12:32.714+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.165 seconds
[2025-06-16T19:13:03.508+0000] {processor.py:157} INFO - Started process (PID=219) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:13:03.510+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:13:03.512+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:13:03.512+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:13:03.569+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:13:03.612+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:13:03.612+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:13:03.686+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.186 seconds
[2025-06-16T19:13:34.559+0000] {processor.py:157} INFO - Started process (PID=242) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:13:34.561+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:13:34.564+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:13:34.564+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:13:34.627+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:13:34.680+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:13:34.680+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:13:34.749+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.197 seconds
[2025-06-16T19:14:05.749+0000] {processor.py:157} INFO - Started process (PID=265) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:14:05.754+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:14:05.758+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:14:05.757+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:14:05.826+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:14:06.049+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:14:06.049+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:14:06.114+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.378 seconds
[2025-06-16T19:14:36.262+0000] {processor.py:157} INFO - Started process (PID=287) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:14:36.264+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:14:36.266+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:14:36.265+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:14:36.325+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:14:36.379+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:14:36.379+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:14:36.447+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.191 seconds
[2025-06-16T19:15:07.409+0000] {processor.py:157} INFO - Started process (PID=312) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:15:07.412+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:15:07.414+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:15:07.414+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:15:07.511+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:15:07.555+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:15:07.555+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:15:07.616+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.215 seconds
[2025-06-16T19:15:38.424+0000] {processor.py:157} INFO - Started process (PID=335) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:15:38.428+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:15:38.432+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:15:38.430+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:15:38.532+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:15:38.612+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:15:38.612+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:15:38.723+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.311 seconds
[2025-06-16T19:16:08.891+0000] {processor.py:157} INFO - Started process (PID=356) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:16:08.894+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:16:08.899+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:16:08.898+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:16:08.986+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:16:09.047+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:16:09.046+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:16:09.136+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.254 seconds
[2025-06-16T19:16:40.052+0000] {processor.py:157} INFO - Started process (PID=381) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:16:40.055+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:16:40.058+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:16:40.057+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:16:40.170+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:16:40.222+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:16:40.221+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:16:40.336+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.291 seconds
[2025-06-16T19:17:10.979+0000] {processor.py:157} INFO - Started process (PID=404) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:17:10.981+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:17:10.983+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:17:10.983+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:17:11.055+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:17:11.101+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:17:11.100+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:17:11.169+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.197 seconds
[2025-06-16T19:17:42.138+0000] {processor.py:157} INFO - Started process (PID=429) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:17:42.141+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:17:42.144+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:17:42.143+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:17:42.208+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:17:42.250+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:17:42.250+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:17:42.282+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:17:42.282+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:17:42.317+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.185 seconds
[2025-06-16T19:18:13.032+0000] {processor.py:157} INFO - Started process (PID=452) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:18:13.034+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:18:13.035+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:18:13.035+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:18:13.083+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:18:13.122+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:18:13.121+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:18:13.158+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:18:13.157+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:18:13.194+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.168 seconds
[2025-06-16T19:18:43.582+0000] {processor.py:157} INFO - Started process (PID=475) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:18:43.584+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:18:43.586+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:18:43.586+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:18:43.646+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:18:43.701+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:18:43.701+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:18:43.738+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:18:43.737+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:18:43.782+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.206 seconds
[2025-06-16T19:19:14.461+0000] {processor.py:157} INFO - Started process (PID=498) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:19:14.463+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:19:14.465+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:19:14.465+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:19:14.527+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:19:14.573+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:19:14.573+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:19:14.610+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:19:14.609+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:19:14.649+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.196 seconds
[2025-06-16T19:19:45.485+0000] {processor.py:157} INFO - Started process (PID=521) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:19:45.488+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:19:45.489+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:19:45.489+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:19:45.583+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:19:45.643+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:19:45.643+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:19:45.685+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:19:45.684+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:19:45.731+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.252 seconds
[2025-06-16T19:20:16.051+0000] {processor.py:157} INFO - Started process (PID=544) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:20:16.053+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:20:16.055+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:20:16.054+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:20:16.119+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:20:16.165+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:20:16.165+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:20:16.211+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:20:16.211+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:20:16.251+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.205 seconds
[2025-06-16T19:20:46.407+0000] {processor.py:157} INFO - Started process (PID=559) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:20:46.409+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:20:46.411+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:20:46.411+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:20:46.499+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:20:46.559+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:20:46.559+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:20:46.604+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:20:46.604+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:20:46.644+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.245 seconds
[2025-06-16T19:21:17.342+0000] {processor.py:157} INFO - Started process (PID=583) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:21:17.345+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:21:17.348+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:21:17.347+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:21:17.440+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:21:17.517+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:21:17.517+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:21:17.562+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:21:17.562+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:21:17.601+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.266 seconds
[2025-06-16T19:21:48.286+0000] {processor.py:157} INFO - Started process (PID=606) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:21:48.288+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:21:48.290+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:21:48.289+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:21:48.350+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:21:48.401+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:21:48.400+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:21:48.439+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:21:48.438+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:21:48.474+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.193 seconds
[2025-06-16T19:22:19.217+0000] {processor.py:157} INFO - Started process (PID=629) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:22:19.222+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:22:19.227+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:22:19.226+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:22:19.311+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:22:19.368+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:22:19.368+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:22:19.416+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:22:19.415+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:22:19.458+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.250 seconds
[2025-06-16T19:22:50.345+0000] {processor.py:157} INFO - Started process (PID=652) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:22:50.347+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:22:50.348+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:22:50.348+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:22:50.410+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:22:50.457+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:22:50.457+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:22:50.489+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:22:50.489+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:22:50.533+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.194 seconds
[2025-06-16T19:23:21.326+0000] {processor.py:157} INFO - Started process (PID=675) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:23:21.329+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:23:21.332+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:23:21.331+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:23:21.417+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:23:21.482+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:23:21.481+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:23:21.523+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:23:21.523+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:23:21.573+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.256 seconds
[2025-06-16T19:23:52.255+0000] {processor.py:157} INFO - Started process (PID=698) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:23:52.257+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:23:52.260+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:23:52.259+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:23:52.336+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:23:52.385+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:23:52.384+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:23:52.420+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:23:52.419+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:23:52.457+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.211 seconds
[2025-06-16T19:24:22.668+0000] {processor.py:157} INFO - Started process (PID=721) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:24:22.670+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:24:22.673+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:24:22.672+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:24:22.737+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:24:22.799+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:24:22.798+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:24:22.844+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:24:22.843+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:24:22.881+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.219 seconds
[2025-06-16T19:24:53.552+0000] {processor.py:157} INFO - Started process (PID=744) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:24:53.554+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:24:53.556+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:24:53.555+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:24:53.622+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:24:53.671+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:24:53.671+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:24:53.707+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:24:53.707+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:24:53.743+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.197 seconds
[2025-06-16T19:25:24.031+0000] {processor.py:157} INFO - Started process (PID=765) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:25:24.033+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:25:24.035+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:25:24.035+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:25:24.115+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:25:24.173+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:25:24.172+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:25:24.214+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:25:24.213+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:25:24.254+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.232 seconds
[2025-06-16T19:25:54.419+0000] {processor.py:157} INFO - Started process (PID=788) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:25:54.422+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:25:54.424+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:25:54.423+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:25:54.498+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:25:54.557+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:25:54.556+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:25:54.599+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:25:54.598+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:25:54.639+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.225 seconds
[2025-06-16T19:26:24.942+0000] {processor.py:157} INFO - Started process (PID=812) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:26:24.945+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:26:24.947+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:26:24.946+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:26:25.013+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:26:25.078+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:26:25.078+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:26:25.119+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:26:25.119+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:26:25.163+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.227 seconds
[2025-06-16T19:26:56.102+0000] {processor.py:157} INFO - Started process (PID=837) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:26:56.105+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:26:56.107+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:26:56.106+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:26:56.164+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:26:56.214+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:26:56.214+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:26:56.247+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:26:56.246+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:26:56.281+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.187 seconds
[2025-06-16T19:27:26.947+0000] {processor.py:157} INFO - Started process (PID=860) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:27:26.949+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:27:26.951+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:27:26.950+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:27:27.024+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:27:27.064+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:27:27.064+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:27:27.096+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:27:27.096+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:27:27.139+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.197 seconds
[2025-06-16T19:27:58.202+0000] {processor.py:157} INFO - Started process (PID=882) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:27:58.206+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:27:58.212+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:27:58.211+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:27:58.295+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:27:58.354+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:27:58.354+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:27:58.399+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:27:58.399+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:27:58.444+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.253 seconds
[2025-06-16T19:28:29.149+0000] {processor.py:157} INFO - Started process (PID=898) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:28:29.151+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:28:29.153+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:28:29.152+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:28:29.211+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:28:29.258+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:28:29.258+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:28:29.291+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:28:29.290+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:28:29.324+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.181 seconds
[2025-06-16T19:29:00.120+0000] {processor.py:157} INFO - Started process (PID=921) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:29:00.123+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:29:00.125+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:29:00.124+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:29:00.179+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:29:00.220+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:29:00.220+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:29:00.252+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:29:00.251+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:29:00.294+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.180 seconds
[2025-06-16T19:29:30.981+0000] {processor.py:157} INFO - Started process (PID=944) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:29:30.984+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:29:30.987+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:29:30.986+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:29:31.059+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:29:31.119+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:29:31.119+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:29:31.158+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:29:31.158+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:29:31.200+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.226 seconds
[2025-06-16T19:30:01.854+0000] {processor.py:157} INFO - Started process (PID=967) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:30:01.857+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:30:01.858+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:30:01.858+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:30:01.914+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:30:01.974+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:30:01.973+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:30:02.010+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:30:02.010+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:30:02.046+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.198 seconds
[2025-06-16T19:30:32.714+0000] {processor.py:157} INFO - Started process (PID=990) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:30:32.716+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:30:32.718+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:30:32.718+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:30:32.782+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:30:32.826+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:30:32.826+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:30:32.860+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:30:32.860+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:30:32.898+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.191 seconds
[2025-06-16T19:31:03.622+0000] {processor.py:157} INFO - Started process (PID=1013) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:31:03.624+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:31:03.626+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:31:03.626+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:31:03.695+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:31:03.749+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:31:03.749+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:31:03.784+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:31:03.784+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:31:03.819+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.204 seconds
[2025-06-16T19:31:34.577+0000] {processor.py:157} INFO - Started process (PID=1036) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:31:34.579+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:31:34.581+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:31:34.581+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:31:34.673+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:31:34.725+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:31:34.725+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:31:34.759+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:31:34.759+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:31:34.816+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.247 seconds
[2025-06-16T19:32:05.513+0000] {processor.py:157} INFO - Started process (PID=1059) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:32:05.515+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:32:05.517+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:32:05.516+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:32:05.575+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:32:05.628+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:32:05.628+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:32:05.666+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:32:05.666+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:32:05.703+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.196 seconds
[2025-06-16T19:32:35.843+0000] {processor.py:157} INFO - Started process (PID=1081) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:32:35.846+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:32:35.849+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:32:35.848+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:32:35.918+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:32:35.977+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:32:35.977+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:32:36.014+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:32:36.014+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:32:36.048+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.211 seconds
[2025-06-16T19:33:06.935+0000] {processor.py:157} INFO - Started process (PID=1106) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:33:06.937+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:33:06.939+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:33:06.938+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:33:06.997+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:33:07.047+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:33:07.047+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:33:07.085+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:33:07.084+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:33:07.116+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.188 seconds
[2025-06-16T19:33:37.815+0000] {processor.py:157} INFO - Started process (PID=1129) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:33:37.817+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:33:37.819+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:33:37.818+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:33:37.884+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:33:37.931+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:33:37.930+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:33:37.964+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:33:37.964+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:33:38.001+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.194 seconds
[2025-06-16T19:34:08.748+0000] {processor.py:157} INFO - Started process (PID=1152) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:34:08.751+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:34:08.754+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:34:08.753+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:34:08.833+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:34:08.878+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:34:08.878+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:34:08.915+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:34:08.914+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:34:08.953+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.215 seconds
[2025-06-16T19:34:39.739+0000] {processor.py:157} INFO - Started process (PID=1176) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:34:39.741+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:34:39.743+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:34:39.742+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:34:39.808+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:34:39.859+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:34:39.859+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:34:39.894+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:34:39.894+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:34:39.951+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.218 seconds
[2025-06-16T19:35:10.659+0000] {processor.py:157} INFO - Started process (PID=1199) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:35:10.662+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:35:10.664+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:35:10.663+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:35:10.746+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:35:10.813+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:35:10.813+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:35:10.854+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:35:10.853+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:35:10.897+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.247 seconds
[2025-06-16T19:35:42.844+0000] {processor.py:157} INFO - Started process (PID=1222) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:35:42.845+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:35:42.847+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:35:42.847+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:35:42.911+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:35:42.957+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:35:42.957+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:35:42.994+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:35:42.994+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:35:43.038+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.200 seconds
[2025-06-16T19:36:13.811+0000] {processor.py:157} INFO - Started process (PID=1237) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:36:13.814+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:36:13.816+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:36:13.816+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:36:13.888+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:36:13.944+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:36:13.944+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:36:13.982+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:36:13.982+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:36:14.023+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.220 seconds
[2025-06-16T19:36:44.842+0000] {processor.py:157} INFO - Started process (PID=1260) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:36:44.845+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:36:44.849+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:36:44.848+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:36:44.936+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:36:45.000+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:36:45.000+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:36:45.047+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:36:45.046+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:36:45.094+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.260 seconds
[2025-06-16T19:37:15.832+0000] {processor.py:157} INFO - Started process (PID=1283) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:37:15.834+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:37:15.837+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:37:15.836+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:37:15.892+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:37:15.946+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:37:15.945+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:37:15.982+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:37:15.982+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:37:16.026+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.205 seconds
[2025-06-16T19:37:46.371+0000] {processor.py:157} INFO - Started process (PID=1306) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:37:46.373+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:37:46.376+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:37:46.375+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:37:46.456+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:37:46.511+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:37:46.510+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:37:46.550+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:37:46.549+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:37:46.599+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.237 seconds
[2025-06-16T19:38:17.070+0000] {processor.py:157} INFO - Started process (PID=1329) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:38:17.073+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:38:17.077+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:38:17.076+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:38:17.163+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:38:17.222+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:38:17.222+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:38:17.259+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:38:17.259+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:38:17.317+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.255 seconds
[2025-06-16T19:38:48.141+0000] {processor.py:157} INFO - Started process (PID=1352) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:38:48.143+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:38:48.146+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:38:48.145+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:38:48.223+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:38:48.284+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:38:48.284+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:38:48.319+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:38:48.318+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:38:48.355+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.223 seconds
[2025-06-16T19:39:19.141+0000] {processor.py:157} INFO - Started process (PID=1376) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:39:19.144+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:39:19.147+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:39:19.147+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:39:19.243+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:39:19.302+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:39:19.302+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:39:19.344+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:39:19.343+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:39:19.396+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.263 seconds
[2025-06-16T19:39:50.319+0000] {processor.py:157} INFO - Started process (PID=1400) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:39:50.321+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:39:50.323+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:39:50.323+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:39:50.397+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:39:50.449+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:39:50.449+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:39:50.491+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:39:50.490+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:39:50.547+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.234 seconds
[2025-06-16T19:40:21.304+0000] {processor.py:157} INFO - Started process (PID=1423) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:40:21.306+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:40:21.308+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:40:21.307+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:40:21.359+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:40:21.400+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:40:21.400+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:40:21.438+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:40:21.438+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:40:21.476+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.176 seconds
[2025-06-16T19:40:52.187+0000] {processor.py:157} INFO - Started process (PID=1446) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:40:52.190+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:40:52.193+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:40:52.192+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:40:52.280+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:40:52.343+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:40:52.342+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:40:52.389+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:40:52.388+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:40:52.429+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.248 seconds
[2025-06-16T19:41:22.750+0000] {processor.py:157} INFO - Started process (PID=1469) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:41:22.753+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:41:22.756+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:41:22.755+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:41:22.862+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:41:22.921+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:41:22.921+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:41:22.956+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:41:22.956+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:41:22.992+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.247 seconds
[2025-06-16T19:41:53.413+0000] {processor.py:157} INFO - Started process (PID=1492) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:41:53.415+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:41:53.418+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:41:53.417+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:41:53.503+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:41:53.555+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:41:53.554+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:41:53.589+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:41:53.589+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:41:53.626+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.220 seconds
[2025-06-16T19:42:23.775+0000] {processor.py:157} INFO - Started process (PID=1515) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:42:23.777+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:42:23.780+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:42:23.779+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:42:23.843+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:42:23.888+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:42:23.888+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:42:23.920+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:42:23.919+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:42:23.957+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.188 seconds
[2025-06-16T19:42:54.255+0000] {processor.py:157} INFO - Started process (PID=1538) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:42:54.258+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:42:54.261+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:42:54.260+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:42:54.339+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:42:54.398+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:42:54.397+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:42:54.455+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:42:54.454+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:42:54.502+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.256 seconds
[2025-06-16T19:43:24.626+0000] {processor.py:157} INFO - Started process (PID=1561) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:43:24.628+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:43:24.630+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:43:24.629+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:43:24.709+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:43:24.757+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:43:24.756+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:43:24.802+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:43:24.802+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:43:24.846+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.226 seconds
[2025-06-16T19:43:55.629+0000] {processor.py:157} INFO - Started process (PID=1576) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:43:55.632+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:43:55.635+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:43:55.634+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:43:55.709+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:43:55.762+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:43:55.762+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:43:55.801+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:43:55.801+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:43:55.837+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.216 seconds
[2025-06-16T19:44:26.699+0000] {processor.py:157} INFO - Started process (PID=1599) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:44:26.702+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:44:26.706+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:44:26.704+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:44:26.785+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:44:26.831+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:44:26.831+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:44:26.867+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:44:26.867+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:44:26.914+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.221 seconds
[2025-06-16T19:44:57.105+0000] {processor.py:157} INFO - Started process (PID=1622) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:44:57.108+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:44:57.110+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:44:57.110+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:44:57.183+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:44:57.233+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:44:57.232+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:44:57.273+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:44:57.272+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:44:57.326+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.227 seconds
[2025-06-16T19:45:27.495+0000] {processor.py:157} INFO - Started process (PID=1645) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:45:27.498+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:45:27.501+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:45:27.500+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:45:27.598+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:45:27.659+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:45:27.659+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:45:27.702+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:45:27.701+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:45:27.759+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.272 seconds
[2025-06-16T19:45:58.804+0000] {processor.py:157} INFO - Started process (PID=1668) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:45:58.807+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:45:58.809+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:45:58.809+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:45:58.891+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:45:58.940+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:45:58.940+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:45:58.987+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:45:58.986+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:45:59.029+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.231 seconds
[2025-06-16T19:46:29.186+0000] {processor.py:157} INFO - Started process (PID=1691) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:46:29.189+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:46:29.193+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:46:29.192+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:46:29.287+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:46:29.349+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:46:29.349+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:46:29.396+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:46:29.396+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:46:29.437+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.258 seconds
[2025-06-16T19:46:59.852+0000] {processor.py:157} INFO - Started process (PID=1714) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:46:59.856+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:46:59.859+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:46:59.858+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:46:59.939+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:46:59.989+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:46:59.989+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:47:00.032+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:47:00.031+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:47:00.069+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.227 seconds
[2025-06-16T19:47:30.907+0000] {processor.py:157} INFO - Started process (PID=1737) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:47:30.909+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:47:30.912+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:47:30.911+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:47:31.000+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:47:31.066+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:47:31.066+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:47:31.115+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:47:31.115+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:47:31.171+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.272 seconds
[2025-06-16T19:48:01.348+0000] {processor.py:157} INFO - Started process (PID=1760) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:48:01.351+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:48:01.354+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:48:01.354+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:48:01.442+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:48:01.502+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:48:01.502+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:48:01.538+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:48:01.538+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:48:01.589+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.250 seconds
[2025-06-16T19:48:32.100+0000] {processor.py:157} INFO - Started process (PID=1783) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:48:32.102+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:48:32.105+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:48:32.104+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:48:32.190+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:48:32.261+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:48:32.261+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:48:32.319+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:48:32.319+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:48:32.362+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.270 seconds
[2025-06-16T19:49:02.564+0000] {processor.py:157} INFO - Started process (PID=1806) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:49:02.567+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:49:02.569+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:49:02.568+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:49:02.656+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:49:02.722+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:49:02.721+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:49:02.777+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:49:02.777+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:49:02.820+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.263 seconds
[2025-06-16T19:49:32.931+0000] {processor.py:157} INFO - Started process (PID=1821) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:49:32.933+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:49:32.937+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:49:32.936+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:49:33.001+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:49:33.050+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:49:33.049+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:49:33.089+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:49:33.089+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:49:33.130+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.208 seconds
[2025-06-16T19:50:03.306+0000] {processor.py:157} INFO - Started process (PID=1845) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:50:03.308+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:50:03.310+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:50:03.310+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:50:03.414+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:50:03.468+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:50:03.468+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:50:03.503+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:50:03.503+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:50:03.548+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.248 seconds
[2025-06-16T19:50:34.179+0000] {processor.py:157} INFO - Started process (PID=1868) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:50:34.182+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:50:34.185+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:50:34.184+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:50:34.242+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:50:34.286+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:50:34.286+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:50:34.321+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:50:34.321+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:50:34.360+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.186 seconds
[2025-06-16T19:51:05.315+0000] {processor.py:157} INFO - Started process (PID=1891) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:51:05.317+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:51:05.320+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:51:05.319+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:51:05.407+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:51:05.473+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:51:05.473+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:51:05.516+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:51:05.515+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:51:05.558+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.249 seconds
[2025-06-16T19:51:36.466+0000] {processor.py:157} INFO - Started process (PID=1914) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:51:36.468+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:51:36.471+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:51:36.470+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:51:36.538+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:51:36.586+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:51:36.586+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:51:36.625+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:51:36.625+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:51:36.669+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.209 seconds
[2025-06-16T19:52:06.833+0000] {processor.py:157} INFO - Started process (PID=1937) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:52:06.835+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:52:06.836+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:52:06.836+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:52:06.898+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:52:06.954+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:52:06.954+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:52:06.995+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:52:06.995+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:52:07.030+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.204 seconds
[2025-06-16T19:52:37.439+0000] {processor.py:157} INFO - Started process (PID=1960) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:52:37.445+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:52:37.450+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:52:37.449+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:52:37.561+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:52:37.620+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:52:37.620+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:52:37.666+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:52:37.666+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:52:37.708+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.276 seconds
[2025-06-16T19:53:08.478+0000] {processor.py:157} INFO - Started process (PID=1983) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:53:08.480+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T19:53:08.482+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:53:08.482+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:53:08.570+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T19:53:08.612+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:53:08.612+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:53:08.654+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:53:08.653+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T19:53:08.712+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.241 seconds
[2025-06-16T20:02:35.942+0000] {processor.py:157} INFO - Started process (PID=2134) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:02:35.945+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:02:35.947+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:02:35.947+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:02:36.067+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:02:36.324+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:02:36.323+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:02:36.364+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:02:36.363+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:02:36.411+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.475 seconds
[2025-06-16T20:03:07.459+0000] {processor.py:157} INFO - Started process (PID=2157) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:03:07.462+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:03:07.465+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:03:07.464+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:03:07.544+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:03:07.599+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:03:07.599+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:03:07.645+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:03:07.645+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:03:07.679+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.226 seconds
[2025-06-16T20:03:38.205+0000] {processor.py:157} INFO - Started process (PID=2180) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:03:38.208+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:03:38.211+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:03:38.210+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:03:38.291+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:03:38.359+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:03:38.358+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:03:38.411+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:03:38.410+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:03:38.475+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.279 seconds
[2025-06-16T20:04:09.206+0000] {processor.py:157} INFO - Started process (PID=2203) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:04:09.209+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:04:09.212+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:04:09.211+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:04:09.293+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:04:09.350+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:04:09.350+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:04:09.398+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:04:09.398+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:04:09.446+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.249 seconds
[2025-06-16T20:04:40.264+0000] {processor.py:157} INFO - Started process (PID=2226) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:04:40.266+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:04:40.269+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:04:40.268+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:04:40.353+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:04:40.402+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:04:40.401+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:04:40.442+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:04:40.442+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:04:40.501+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.246 seconds
[2025-06-16T20:05:11.211+0000] {processor.py:157} INFO - Started process (PID=2249) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:05:11.213+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:05:11.215+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:05:11.214+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:05:11.277+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:05:11.338+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:05:11.338+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:05:11.378+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:05:11.377+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:05:11.418+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.212 seconds
[2025-06-16T20:05:42.472+0000] {processor.py:157} INFO - Started process (PID=2272) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:05:42.474+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:05:42.476+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:05:42.476+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:05:42.539+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:05:42.585+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:05:42.584+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:05:42.623+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:05:42.623+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:05:42.658+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.191 seconds
[2025-06-16T20:06:13.485+0000] {processor.py:157} INFO - Started process (PID=2287) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:06:13.488+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:06:13.491+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:06:13.490+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:06:13.560+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:06:13.619+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:06:13.619+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:06:13.651+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:06:13.650+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:06:13.687+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.210 seconds
[2025-06-16T20:06:44.509+0000] {processor.py:157} INFO - Started process (PID=2310) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:06:44.512+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:06:44.515+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:06:44.514+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:06:44.586+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:06:44.655+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:06:44.655+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:06:44.702+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:06:44.702+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:06:44.760+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.259 seconds
[2025-06-16T20:07:14.978+0000] {processor.py:157} INFO - Started process (PID=2333) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:07:14.982+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:07:14.986+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:07:14.985+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:07:15.100+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:07:15.155+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:07:15.155+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:07:15.209+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:07:15.209+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:07:15.256+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.285 seconds
[2025-06-16T20:07:45.681+0000] {processor.py:157} INFO - Started process (PID=2355) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:07:45.683+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:07:45.685+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:07:45.684+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:07:45.752+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:07:45.806+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:07:45.806+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:07:45.844+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:07:45.844+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:07:45.875+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.202 seconds
[2025-06-16T20:08:16.333+0000] {processor.py:157} INFO - Started process (PID=2378) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:08:16.335+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:08:16.337+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:08:16.337+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:08:16.402+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:08:16.448+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:08:16.447+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:08:16.480+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:08:16.480+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:08:16.515+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.188 seconds
[2025-06-16T20:08:46.704+0000] {processor.py:157} INFO - Started process (PID=2401) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:08:46.707+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:08:46.710+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:08:46.709+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:08:46.796+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:08:46.843+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:08:46.842+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:08:46.874+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:08:46.873+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:08:46.913+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.215 seconds
[2025-06-16T20:09:17.132+0000] {processor.py:157} INFO - Started process (PID=2422) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:09:17.135+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:09:17.137+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:09:17.137+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:09:17.227+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:09:17.296+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:09:17.296+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:09:17.337+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:09:17.337+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:09:17.371+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.247 seconds
[2025-06-16T20:09:47.854+0000] {processor.py:157} INFO - Started process (PID=2447) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:09:47.857+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:09:47.859+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:09:47.858+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:09:47.922+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:09:47.965+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:09:47.965+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:09:48.002+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:09:48.002+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:09:48.052+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.208 seconds
[2025-06-16T20:10:18.247+0000] {processor.py:157} INFO - Started process (PID=2471) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:10:18.250+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:10:18.253+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:10:18.252+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:10:18.356+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:10:18.428+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:10:18.427+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:10:18.469+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:10:18.468+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:10:18.506+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.268 seconds
[2025-06-16T20:10:49.294+0000] {processor.py:157} INFO - Started process (PID=2494) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:10:49.296+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:10:49.299+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:10:49.298+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:10:49.382+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:10:49.435+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:10:49.435+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:10:49.474+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:10:49.473+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:10:49.525+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.238 seconds
[2025-06-16T20:11:19.896+0000] {processor.py:157} INFO - Started process (PID=2517) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:11:19.898+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:11:19.900+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:11:19.900+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:11:19.967+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:11:20.013+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:11:20.013+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:11:20.049+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:11:20.049+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:11:20.083+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.191 seconds
[2025-06-16T20:11:50.418+0000] {processor.py:157} INFO - Started process (PID=2540) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:11:50.421+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:11:50.424+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:11:50.423+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:11:50.553+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:11:50.600+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:11:50.600+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:11:50.635+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:11:50.635+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:11:50.685+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.274 seconds
[2025-06-16T20:12:21.500+0000] {processor.py:157} INFO - Started process (PID=2563) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:12:21.503+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:12:21.505+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:12:21.505+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:12:21.583+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:12:21.639+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:12:21.638+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:12:21.675+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:12:21.674+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:12:21.708+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.214 seconds
[2025-06-16T20:12:52.445+0000] {processor.py:157} INFO - Started process (PID=2586) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:12:52.449+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:12:52.452+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:12:52.451+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:12:52.536+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:12:52.602+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:12:52.602+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:12:52.653+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:12:52.653+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:12:52.695+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.258 seconds
[2025-06-16T20:13:23.471+0000] {processor.py:157} INFO - Started process (PID=2608) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:13:23.474+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:13:23.476+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:13:23.476+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:13:23.551+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:13:23.610+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:13:23.610+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:13:23.651+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:13:23.651+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:13:23.683+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.219 seconds
[2025-06-16T20:13:54.486+0000] {processor.py:157} INFO - Started process (PID=2624) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:13:54.489+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:13:54.492+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:13:54.491+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:13:54.579+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:13:54.647+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:13:54.647+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:13:54.700+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:13:54.699+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:13:54.747+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.269 seconds
[2025-06-16T20:14:25.709+0000] {processor.py:157} INFO - Started process (PID=2647) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:14:25.712+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:14:25.714+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:14:25.714+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:14:25.805+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:14:25.866+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:14:25.866+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:14:25.900+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:14:25.899+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:14:25.939+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.236 seconds
[2025-06-16T20:14:56.142+0000] {processor.py:157} INFO - Started process (PID=2671) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:14:56.145+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:14:56.148+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:14:56.147+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:14:56.230+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:14:56.295+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:14:56.295+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:14:56.342+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:14:56.341+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:14:56.393+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.257 seconds
[2025-06-16T20:15:26.550+0000] {processor.py:157} INFO - Started process (PID=2694) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:15:26.552+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:15:26.555+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:15:26.554+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:15:26.652+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:15:26.712+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:15:26.711+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:15:26.751+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:15:26.751+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:15:26.791+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.249 seconds
[2025-06-16T20:15:56.922+0000] {processor.py:157} INFO - Started process (PID=2717) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:15:56.924+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:15:56.926+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:15:56.926+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:15:57.012+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:15:57.065+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:15:57.065+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:15:57.109+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:15:57.109+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:15:57.144+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.229 seconds
[2025-06-16T20:16:28.226+0000] {processor.py:157} INFO - Started process (PID=2740) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:16:28.228+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:16:28.230+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:16:28.229+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:16:28.300+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:16:28.364+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:16:28.363+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:16:28.408+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:16:28.408+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:16:28.464+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.245 seconds
[2025-06-16T20:16:58.650+0000] {processor.py:157} INFO - Started process (PID=2763) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:16:58.653+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:16:58.655+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:16:58.655+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:16:58.743+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:16:58.790+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:16:58.790+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:16:58.837+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:16:58.837+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:16:58.884+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.243 seconds
[2025-06-16T20:17:29.440+0000] {processor.py:157} INFO - Started process (PID=2786) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:17:29.443+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:17:29.451+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:17:29.450+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:17:29.586+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:17:29.656+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:17:29.656+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:17:29.725+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:17:29.725+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:17:29.849+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.415 seconds
[2025-06-16T20:18:00.459+0000] {processor.py:157} INFO - Started process (PID=2809) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:18:00.462+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:18:00.465+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:18:00.464+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:18:00.548+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:18:00.599+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:18:00.598+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:18:00.638+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:18:00.638+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:18:00.687+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.235 seconds
[2025-06-16T20:18:31.117+0000] {processor.py:157} INFO - Started process (PID=2832) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:18:31.128+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:18:31.133+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:18:31.132+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:18:31.268+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:18:31.344+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:18:31.344+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:18:31.387+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:18:31.387+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:18:31.434+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.328 seconds
[2025-06-16T20:19:02.047+0000] {processor.py:157} INFO - Started process (PID=2855) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:19:02.050+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:19:02.053+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:19:02.053+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:19:02.134+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:19:02.183+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:19:02.183+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:19:02.217+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:19:02.217+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:19:02.260+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.220 seconds
[2025-06-16T20:19:32.384+0000] {processor.py:157} INFO - Started process (PID=2878) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:19:32.386+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:19:32.389+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:19:32.388+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:19:32.459+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:19:32.521+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:19:32.521+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:19:32.565+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:19:32.564+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:19:32.609+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.232 seconds
[2025-06-16T20:20:03.364+0000] {processor.py:157} INFO - Started process (PID=2893) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:20:03.366+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:20:03.368+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:20:03.367+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:20:03.454+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:20:03.523+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:20:03.523+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:20:03.591+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:20:03.590+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:20:03.630+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.272 seconds
[2025-06-16T20:20:34.149+0000] {processor.py:157} INFO - Started process (PID=2916) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:20:34.152+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:20:34.156+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:20:34.155+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:20:34.225+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:20:34.278+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:20:34.277+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:20:34.318+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:20:34.318+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:20:34.354+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.214 seconds
[2025-06-16T20:21:04.756+0000] {processor.py:157} INFO - Started process (PID=2939) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:21:04.758+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:21:04.760+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:21:04.759+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:21:04.824+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:21:04.885+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:21:04.885+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:21:04.925+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:21:04.925+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:21:04.965+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.215 seconds
[2025-06-16T20:21:35.720+0000] {processor.py:157} INFO - Started process (PID=2962) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:21:35.723+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:21:35.726+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:21:35.725+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:21:35.805+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:21:35.861+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:21:35.860+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:21:35.901+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:21:35.900+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:21:36.460+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.752 seconds
[2025-06-16T20:22:07.523+0000] {processor.py:157} INFO - Started process (PID=2985) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:22:07.525+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:22:07.527+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:22:07.527+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:22:07.605+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:22:07.667+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:22:07.666+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:22:07.709+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:22:07.709+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:22:07.753+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.236 seconds
[2025-06-16T20:22:38.264+0000] {processor.py:157} INFO - Started process (PID=3008) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:22:38.266+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:22:38.269+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:22:38.268+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:22:38.350+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:22:38.401+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:22:38.401+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:22:38.609+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:22:38.609+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:22:38.650+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.393 seconds
[2025-06-16T20:23:09.718+0000] {processor.py:157} INFO - Started process (PID=3031) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:23:09.721+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:23:09.723+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:23:09.723+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:23:09.827+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:23:09.881+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:23:09.881+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:23:09.928+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:23:09.928+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:23:09.967+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.256 seconds
[2025-06-16T20:23:40.875+0000] {processor.py:157} INFO - Started process (PID=3054) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:23:40.876+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:23:40.878+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:23:40.878+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:23:40.938+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:23:40.996+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:23:40.996+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:23:41.060+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:23:41.059+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:23:41.119+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.249 seconds
[2025-06-16T20:24:11.910+0000] {processor.py:157} INFO - Started process (PID=3077) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:24:11.912+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:24:11.916+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:24:11.916+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:24:12.013+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:24:12.071+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:24:12.070+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:24:12.116+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:24:12.116+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:24:12.329+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.426 seconds
[2025-06-16T20:24:42.624+0000] {processor.py:157} INFO - Started process (PID=3100) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:24:42.628+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:24:42.630+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:24:42.630+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:24:42.720+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:24:42.949+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:24:42.948+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:24:42.982+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:24:42.982+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:24:43.037+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.420 seconds
[2025-06-16T20:25:13.901+0000] {processor.py:157} INFO - Started process (PID=3123) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:25:13.903+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:25:13.905+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:25:13.905+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:25:13.986+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:25:14.047+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:25:14.047+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:25:14.338+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:25:14.337+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:25:14.375+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.480 seconds
[2025-06-16T20:25:45.222+0000] {processor.py:157} INFO - Started process (PID=3146) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:25:45.225+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:25:45.228+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:25:45.227+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:25:45.549+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:25:45.595+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:25:45.595+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:25:45.630+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:25:45.630+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:25:45.670+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.454 seconds
[2025-06-16T20:26:17.641+0000] {processor.py:157} INFO - Started process (PID=3169) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:26:17.654+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:26:17.665+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:26:17.664+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:26:17.890+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:26:17.946+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:26:17.946+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:26:17.996+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:26:17.995+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:26:18.048+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.414 seconds
[2025-06-16T20:26:48.913+0000] {processor.py:157} INFO - Started process (PID=3192) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:26:48.915+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:26:48.917+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:26:48.917+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:26:48.974+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:26:49.026+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:26:49.025+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:26:49.067+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:26:49.067+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:26:49.112+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.208 seconds
[2025-06-16T20:27:19.314+0000] {processor.py:157} INFO - Started process (PID=3207) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:27:19.316+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:27:19.319+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:27:19.319+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:27:19.365+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:27:19.428+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:27:19.427+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:27:19.478+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:27:19.478+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:27:19.527+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.219 seconds
[2025-06-16T20:27:50.376+0000] {processor.py:157} INFO - Started process (PID=3230) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:27:50.379+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:27:50.381+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:27:50.380+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:27:50.430+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:27:50.488+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:27:50.488+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:27:50.530+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:27:50.529+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:27:50.582+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.214 seconds
[2025-06-16T20:28:21.443+0000] {processor.py:157} INFO - Started process (PID=3253) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:28:21.445+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:28:21.448+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:28:21.447+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:28:21.528+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:28:21.580+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:28:21.580+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:28:21.621+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:28:21.620+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:28:21.666+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.229 seconds
[2025-06-16T20:28:52.233+0000] {processor.py:157} INFO - Started process (PID=3276) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:28:52.236+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:28:52.239+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:28:52.238+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:28:52.406+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:28:52.473+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:28:52.472+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:28:52.535+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:28:52.535+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:28:52.628+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.400 seconds
[2025-06-16T20:29:23.318+0000] {processor.py:157} INFO - Started process (PID=3299) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:29:23.321+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:29:23.323+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:29:23.322+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:29:23.390+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:29:23.448+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:29:23.448+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:29:23.493+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:29:23.493+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:29:23.538+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.226 seconds
[2025-06-16T20:29:54.734+0000] {processor.py:157} INFO - Started process (PID=3322) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:29:54.735+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:29:54.737+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:29:54.736+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:29:54.791+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:29:54.857+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:29:54.857+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:29:54.912+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:29:54.911+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:29:54.952+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.223 seconds
[2025-06-16T20:30:25.683+0000] {processor.py:157} INFO - Started process (PID=3345) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:30:25.685+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:30:25.688+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:30:25.687+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:30:25.844+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:30:25.926+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:30:25.926+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:30:25.990+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:30:25.989+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:30:26.059+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.380 seconds
[2025-06-16T20:30:56.768+0000] {processor.py:157} INFO - Started process (PID=3368) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:30:56.770+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:30:56.772+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:30:56.772+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:30:56.829+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:30:56.882+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:30:56.882+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:30:56.922+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:30:56.921+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:30:56.969+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.206 seconds
[2025-06-16T20:31:27.735+0000] {processor.py:157} INFO - Started process (PID=3391) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:31:27.738+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:31:27.741+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:31:27.741+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:31:27.812+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:31:27.865+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:31:27.865+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:31:27.909+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:31:27.908+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:31:27.959+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.228 seconds
[2025-06-16T20:31:58.302+0000] {processor.py:157} INFO - Started process (PID=3414) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:31:58.307+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:31:58.310+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:31:58.309+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:31:58.381+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:31:58.432+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:31:58.432+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:31:58.471+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:31:58.471+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:31:58.521+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.228 seconds
[2025-06-16T20:32:28.692+0000] {processor.py:157} INFO - Started process (PID=3437) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:32:28.695+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:32:28.698+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:32:28.698+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:32:28.805+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:32:28.868+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:32:28.868+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:32:28.940+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:32:28.939+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:32:29.001+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.317 seconds
[2025-06-16T20:32:59.318+0000] {processor.py:157} INFO - Started process (PID=3460) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:32:59.321+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:32:59.323+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:32:59.322+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:32:59.383+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:32:59.437+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:32:59.437+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:32:59.475+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:32:59.475+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:32:59.529+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.219 seconds
[2025-06-16T20:33:30.116+0000] {processor.py:157} INFO - Started process (PID=3483) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:33:30.120+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:33:30.130+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:33:30.122+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:33:30.212+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:33:30.262+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:33:30.262+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:33:30.298+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:33:30.298+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:33:30.332+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.234 seconds
[2025-06-16T20:34:00.493+0000] {processor.py:157} INFO - Started process (PID=3498) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:34:00.500+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:34:00.503+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:34:00.502+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:34:00.577+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:34:00.646+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:34:00.645+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:34:00.704+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:34:00.703+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:34:00.754+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.270 seconds
[2025-06-16T20:34:31.519+0000] {processor.py:157} INFO - Started process (PID=3521) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:34:31.521+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:34:31.523+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:34:31.523+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:34:31.562+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:34:31.691+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:34:31.690+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:34:31.745+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:34:31.745+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:34:31.788+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.275 seconds
[2025-06-16T20:35:02.075+0000] {processor.py:157} INFO - Started process (PID=3544) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:35:02.079+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:35:02.082+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:35:02.081+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:35:02.200+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:35:02.265+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:35:02.265+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:35:02.311+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:35:02.311+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:35:02.387+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.320 seconds
[2025-06-16T20:35:32.577+0000] {processor.py:157} INFO - Started process (PID=3567) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:35:32.580+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:35:32.583+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:35:32.582+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:35:32.667+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:35:32.738+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:35:32.737+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:35:32.777+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:35:32.777+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:35:32.816+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.248 seconds
[2025-06-16T20:36:02.941+0000] {processor.py:157} INFO - Started process (PID=3590) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:36:02.943+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:36:02.946+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:36:02.945+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:36:03.008+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:36:03.058+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:36:03.057+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:36:03.107+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:36:03.107+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:36:03.161+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.225 seconds
[2025-06-16T20:36:34.013+0000] {processor.py:157} INFO - Started process (PID=3613) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:36:34.016+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:36:34.018+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:36:34.017+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:36:34.093+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:36:34.142+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:36:34.142+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:36:34.181+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:36:34.181+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:36:34.245+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.241 seconds
[2025-06-16T20:37:04.703+0000] {processor.py:157} INFO - Started process (PID=3636) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:37:04.706+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:37:04.710+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:37:04.709+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:37:04.804+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:37:04.878+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:37:04.877+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:37:04.934+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:37:04.933+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:37:04.987+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.291 seconds
[2025-06-16T20:37:35.716+0000] {processor.py:157} INFO - Started process (PID=3660) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:37:35.720+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:37:35.722+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:37:35.721+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:37:35.786+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:37:35.853+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:37:35.853+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:37:35.932+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:37:35.931+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:37:35.984+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.273 seconds
[2025-06-16T20:38:06.610+0000] {processor.py:157} INFO - Started process (PID=3683) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:38:06.622+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:38:06.630+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:38:06.629+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:38:06.837+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:38:06.908+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:38:06.908+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:38:06.970+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:38:06.970+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:38:07.056+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.453 seconds
[2025-06-16T20:38:37.211+0000] {processor.py:157} INFO - Started process (PID=3706) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:38:37.213+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:38:37.215+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:38:37.215+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:38:37.265+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:38:37.312+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:38:37.312+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:38:37.365+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:38:37.364+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:38:37.419+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.216 seconds
[2025-06-16T20:39:08.163+0000] {processor.py:157} INFO - Started process (PID=3729) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:39:08.165+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:39:08.166+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:39:08.166+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:39:08.231+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:39:08.288+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:39:08.287+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:39:08.327+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:39:08.327+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:39:08.362+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.204 seconds
[2025-06-16T20:39:38.760+0000] {processor.py:157} INFO - Started process (PID=3752) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:39:38.763+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:39:38.765+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:39:38.765+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:39:38.846+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:39:38.910+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:39:38.910+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:39:38.955+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:39:38.954+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:39:39.025+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.276 seconds
[2025-06-16T20:40:10.440+0000] {processor.py:157} INFO - Started process (PID=3767) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:40:10.451+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:40:10.453+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:40:10.452+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:40:10.613+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:40:10.685+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:40:10.685+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:40:10.746+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:40:10.746+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:40:10.984+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.551 seconds
[2025-06-16T20:40:41.833+0000] {processor.py:157} INFO - Started process (PID=3789) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:40:41.836+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:40:41.838+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:40:41.838+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:40:41.902+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:40:41.957+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:40:41.957+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:40:41.999+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:40:41.999+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:40:42.038+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.214 seconds
[2025-06-16T20:41:13.121+0000] {processor.py:157} INFO - Started process (PID=3812) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:41:13.279+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:41:13.287+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:41:13.286+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:41:13.493+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:41:13.638+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:41:13.638+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:41:13.763+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:41:13.763+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:41:13.884+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.769 seconds
[2025-06-16T20:41:44.643+0000] {processor.py:157} INFO - Started process (PID=3835) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:41:44.651+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:41:44.653+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:41:44.652+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:41:44.701+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:41:44.757+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:41:44.757+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:41:44.806+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:41:44.805+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:41:44.861+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.223 seconds
[2025-06-16T20:42:15.083+0000] {processor.py:157} INFO - Started process (PID=3859) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:42:15.087+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:42:15.089+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:42:15.088+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:42:15.139+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:42:15.190+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:42:15.189+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:42:15.233+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:42:15.232+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:42:15.266+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.189 seconds
[2025-06-16T20:42:45.408+0000] {processor.py:157} INFO - Started process (PID=3882) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:42:45.414+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:42:45.426+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:42:45.425+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:42:45.525+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:42:45.588+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:42:45.588+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:42:45.632+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:42:45.632+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:42:45.697+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.299 seconds
[2025-06-16T20:43:16.012+0000] {processor.py:157} INFO - Started process (PID=3905) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:43:16.013+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:43:16.015+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:43:16.015+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:43:16.081+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:43:16.153+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:43:16.153+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:43:16.204+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:43:16.203+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:43:16.254+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.248 seconds
[2025-06-16T20:43:46.432+0000] {processor.py:157} INFO - Started process (PID=3928) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:43:46.434+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:43:46.436+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:43:46.436+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:43:46.489+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:43:46.536+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:43:46.536+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:43:46.580+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:43:46.580+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:43:46.633+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.206 seconds
[2025-06-16T20:44:16.877+0000] {processor.py:157} INFO - Started process (PID=3951) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:44:16.881+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:44:16.886+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:44:16.885+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:44:16.967+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:44:17.032+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:44:17.032+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:44:17.156+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:44:17.156+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:44:17.215+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.347 seconds
[2025-06-16T20:44:47.371+0000] {processor.py:157} INFO - Started process (PID=3974) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:44:47.373+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:44:47.375+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:44:47.375+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:44:47.431+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:44:47.489+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:44:47.488+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:44:47.534+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:44:47.531+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:44:47.590+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.226 seconds
[2025-06-16T20:45:17.754+0000] {processor.py:157} INFO - Started process (PID=3997) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:45:17.757+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:45:17.759+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:45:17.758+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:45:17.830+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:45:17.888+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:45:17.888+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:45:17.924+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:45:17.924+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:45:17.962+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.214 seconds
[2025-06-16T20:45:48.934+0000] {processor.py:157} INFO - Started process (PID=4020) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:45:48.937+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:45:48.942+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:45:48.942+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:45:49.016+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:45:49.069+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:45:49.068+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:45:49.106+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:45:49.106+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:45:49.149+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.224 seconds
[2025-06-16T20:46:19.817+0000] {processor.py:157} INFO - Started process (PID=4042) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:46:19.819+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:46:19.821+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:46:19.821+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:46:19.888+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:46:19.939+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:46:19.939+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:46:19.982+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:46:19.982+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:46:20.020+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.209 seconds
[2025-06-16T20:46:50.805+0000] {processor.py:157} INFO - Started process (PID=4058) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:46:50.806+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:46:50.808+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:46:50.808+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:46:50.956+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:46:51.027+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:46:51.027+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:46:51.082+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:46:51.082+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:46:51.149+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.349 seconds
[2025-06-16T20:47:21.509+0000] {processor.py:157} INFO - Started process (PID=4081) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:47:21.512+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:47:21.514+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:47:21.514+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:47:21.598+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:47:21.671+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:47:21.670+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:47:21.723+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:47:21.720+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:47:21.781+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.280 seconds
[2025-06-16T20:47:51.999+0000] {processor.py:157} INFO - Started process (PID=4104) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:47:52.002+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:47:52.005+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:47:52.004+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:47:52.083+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:47:52.155+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:47:52.153+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:47:52.203+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:47:52.202+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:47:52.254+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.262 seconds
[2025-06-16T20:48:24.843+0000] {processor.py:157} INFO - Started process (PID=4127) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:48:24.865+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:48:24.892+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:48:24.891+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:48:25.981+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:48:26.112+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:48:26.112+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:48:26.304+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:48:26.304+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:48:26.448+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 1.611 seconds
[2025-06-16T20:48:57.409+0000] {processor.py:157} INFO - Started process (PID=4150) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:48:57.412+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:48:57.416+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:48:57.414+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:48:57.466+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:48:57.510+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:48:57.510+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:48:57.557+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:48:57.556+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:48:57.606+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.206 seconds
[2025-06-16T20:49:28.427+0000] {processor.py:157} INFO - Started process (PID=4173) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:49:28.429+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:49:28.431+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:49:28.430+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:49:28.491+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:49:28.560+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:49:28.560+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:49:28.602+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:49:28.601+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:49:28.647+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.227 seconds
[2025-06-16T20:49:59.614+0000] {processor.py:157} INFO - Started process (PID=4196) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:49:59.616+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:49:59.619+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:49:59.618+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:49:59.699+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:49:59.757+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:49:59.757+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:49:59.803+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:49:59.802+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:49:59.848+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.240 seconds
[2025-06-16T20:50:31.472+0000] {processor.py:157} INFO - Started process (PID=4220) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:50:31.479+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:50:31.483+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:50:31.483+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:50:31.607+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:50:31.661+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:50:31.660+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:50:31.701+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:50:31.700+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:50:31.745+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.278 seconds
[2025-06-16T20:51:02.384+0000] {processor.py:157} INFO - Started process (PID=4244) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:51:02.387+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:51:02.390+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:51:02.389+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:51:02.436+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:51:02.483+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:51:02.483+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:51:02.520+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:51:02.520+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:51:02.566+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.188 seconds
[2025-06-16T20:51:33.287+0000] {processor.py:157} INFO - Started process (PID=4267) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:51:33.292+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:51:33.295+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:51:33.293+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:51:33.345+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:51:33.387+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:51:33.387+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:51:33.433+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:51:33.432+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:51:33.492+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.212 seconds
[2025-06-16T20:52:04.160+0000] {processor.py:157} INFO - Started process (PID=4290) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:52:04.163+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:52:04.166+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:52:04.165+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:52:04.243+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:52:04.312+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:52:04.312+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:52:04.360+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:52:04.360+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:52:04.398+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.246 seconds
[2025-06-16T20:52:34.562+0000] {processor.py:157} INFO - Started process (PID=4313) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:52:34.565+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:52:34.567+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:52:34.566+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:52:34.627+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:52:34.686+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:52:34.686+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:52:34.730+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:52:34.730+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:52:34.785+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.231 seconds
[2025-06-16T20:53:05.067+0000] {processor.py:157} INFO - Started process (PID=4336) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:53:05.069+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:53:05.073+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:53:05.072+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:53:05.153+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:53:05.208+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:53:05.208+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:53:05.250+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:53:05.249+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:53:05.286+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.227 seconds
[2025-06-16T20:53:35.962+0000] {processor.py:157} INFO - Started process (PID=4359) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:53:35.963+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:53:35.965+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:53:35.965+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:53:36.016+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:53:36.067+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:53:36.067+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:53:36.101+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:53:36.101+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:53:36.137+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.180 seconds
[2025-06-16T20:54:06.787+0000] {processor.py:157} INFO - Started process (PID=4382) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:54:06.789+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:54:06.790+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:54:06.790+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:54:06.850+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:54:06.901+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:54:06.900+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:54:06.940+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:54:06.939+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:54:06.974+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.193 seconds
[2025-06-16T20:54:37.738+0000] {processor.py:157} INFO - Started process (PID=4397) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:54:37.742+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:54:37.746+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:54:37.745+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:54:37.828+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:54:37.889+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:54:37.889+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:54:37.942+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:54:37.942+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:54:37.980+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.251 seconds
[2025-06-16T20:55:08.878+0000] {processor.py:157} INFO - Started process (PID=4420) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:55:08.880+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:55:08.882+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:55:08.882+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:55:08.943+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:55:08.996+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:55:08.996+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:55:09.040+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:55:09.040+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:55:09.077+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.204 seconds
[2025-06-16T20:55:39.884+0000] {processor.py:157} INFO - Started process (PID=4443) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:55:39.886+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:55:39.888+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:55:39.887+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:55:39.949+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:55:40.007+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:55:40.007+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:55:40.048+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:55:40.047+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:55:40.096+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.218 seconds
[2025-06-16T20:56:10.862+0000] {processor.py:157} INFO - Started process (PID=4466) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:56:10.864+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:56:10.866+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:56:10.866+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:56:10.917+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:56:10.961+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:56:10.961+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:56:10.995+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:56:10.995+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:56:11.035+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.180 seconds
[2025-06-16T20:56:41.358+0000] {processor.py:157} INFO - Started process (PID=4489) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:56:41.360+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:56:41.362+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:56:41.361+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:56:41.417+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:56:41.473+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:56:41.473+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:56:41.529+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:56:41.529+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:56:41.604+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.251 seconds
[2025-06-16T20:57:12.282+0000] {processor.py:157} INFO - Started process (PID=4512) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:57:12.284+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:57:12.286+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:57:12.285+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:57:12.328+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:57:12.380+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:57:12.379+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:57:12.429+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:57:12.429+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:57:12.471+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.196 seconds
[2025-06-16T20:57:42.885+0000] {processor.py:157} INFO - Started process (PID=4535) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:57:42.887+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:57:42.889+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:57:42.888+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:57:43.008+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:57:43.060+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:57:43.059+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:57:43.107+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:57:43.107+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:57:43.152+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.274 seconds
[2025-06-16T20:58:13.941+0000] {processor.py:157} INFO - Started process (PID=4558) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:58:13.944+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:58:13.946+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:58:13.945+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:58:13.993+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:58:14.048+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:58:14.048+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:58:14.087+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:58:14.086+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:58:14.127+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.195 seconds
[2025-06-16T20:58:44.946+0000] {processor.py:157} INFO - Started process (PID=4581) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:58:44.949+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:58:44.950+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:58:44.950+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:58:45.061+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:58:45.111+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:58:45.111+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:58:45.159+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:58:45.158+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:58:45.225+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.284 seconds
[2025-06-16T20:59:16.338+0000] {processor.py:157} INFO - Started process (PID=4604) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:59:16.351+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:59:16.354+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:59:16.353+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:59:16.422+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:59:16.469+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:59:16.469+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:59:16.506+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:59:16.506+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:59:16.554+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.221 seconds
[2025-06-16T20:59:47.522+0000] {processor.py:157} INFO - Started process (PID=4627) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:59:47.525+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T20:59:47.527+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:59:47.526+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:59:47.592+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T20:59:47.642+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:59:47.642+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:59:47.696+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:59:47.695+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T20:59:47.741+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.226 seconds
[2025-06-16T21:00:18.491+0000] {processor.py:157} INFO - Started process (PID=4650) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:00:18.495+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:00:18.497+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:00:18.497+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:00:18.546+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:00:18.589+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:00:18.589+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:00:18.639+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:00:18.639+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:00:18.675+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.189 seconds
[2025-06-16T21:00:49.386+0000] {processor.py:157} INFO - Started process (PID=4673) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:00:49.388+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:00:49.391+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:00:49.390+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:00:49.472+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:00:49.533+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:00:49.533+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:00:49.580+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:00:49.580+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:00:49.633+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.253 seconds
[2025-06-16T21:01:20.346+0000] {processor.py:157} INFO - Started process (PID=4696) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:01:20.350+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:01:20.353+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:01:20.352+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:01:20.440+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:01:20.497+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:01:20.496+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:01:20.533+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:01:20.533+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:01:20.594+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.255 seconds
[2025-06-16T21:01:50.720+0000] {processor.py:157} INFO - Started process (PID=4718) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:01:50.722+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:01:50.725+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:01:50.724+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:01:50.811+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:01:50.886+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:01:50.886+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:01:50.938+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:01:50.938+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:01:50.998+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.285 seconds
[2025-06-16T21:02:21.208+0000] {processor.py:157} INFO - Started process (PID=4734) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:02:21.210+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:02:21.213+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:02:21.212+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:02:21.332+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:02:21.412+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:02:21.412+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:02:21.475+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:02:21.475+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:02:21.549+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.346 seconds
[2025-06-16T21:02:53.555+0000] {processor.py:157} INFO - Started process (PID=4757) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:02:53.675+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:02:53.685+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:02:53.684+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:02:55.153+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:02:55.330+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:02:55.330+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:02:55.765+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:02:55.764+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:02:56.204+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 2.656 seconds
[2025-06-16T21:03:28.228+0000] {processor.py:157} INFO - Started process (PID=4780) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:03:28.250+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:03:28.267+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:03:28.266+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:03:28.800+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:03:28.909+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:03:28.908+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:03:28.996+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:03:28.996+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:03:29.099+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.878 seconds
[2025-06-16T21:03:59.286+0000] {processor.py:157} INFO - Started process (PID=4803) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:03:59.288+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:03:59.298+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:03:59.297+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:03:59.395+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:03:59.512+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:03:59.511+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:03:59.568+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:03:59.568+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:03:59.609+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.328 seconds
[2025-06-16T21:04:30.288+0000] {processor.py:157} INFO - Started process (PID=4826) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:04:30.290+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:04:30.293+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:04:30.293+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:04:30.367+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:04:30.424+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:04:30.423+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:04:30.470+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:04:30.470+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:04:30.510+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.231 seconds
[2025-06-16T21:05:01.262+0000] {processor.py:157} INFO - Started process (PID=4849) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:05:01.264+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:05:01.267+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:05:01.266+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:05:01.323+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:05:01.380+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:05:01.380+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:05:01.421+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:05:01.421+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:05:01.477+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.223 seconds
[2025-06-16T21:05:32.175+0000] {processor.py:157} INFO - Started process (PID=4873) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:05:32.177+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:05:32.179+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:05:32.178+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:05:32.256+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:05:32.316+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:05:32.315+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:05:32.356+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:05:32.356+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:05:32.393+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.224 seconds
[2025-06-16T21:06:02.858+0000] {processor.py:157} INFO - Started process (PID=4896) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:06:02.860+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:06:02.862+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:06:02.862+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:06:02.927+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:06:02.999+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:06:02.998+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:06:03.049+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:06:03.048+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:06:03.084+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.233 seconds
[2025-06-16T21:06:33.823+0000] {processor.py:157} INFO - Started process (PID=4919) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:06:33.826+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:06:33.829+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:06:33.829+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:06:33.910+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:06:33.967+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:06:33.967+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:06:34.016+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:06:34.016+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:06:34.078+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.262 seconds
[2025-06-16T21:07:04.846+0000] {processor.py:157} INFO - Started process (PID=4942) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:07:04.848+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:07:04.850+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:07:04.849+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:07:04.914+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:07:04.956+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:07:04.955+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:07:04.995+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:07:04.995+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:07:05.032+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.191 seconds
[2025-06-16T21:07:35.677+0000] {processor.py:157} INFO - Started process (PID=4966) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:07:35.680+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:07:35.682+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:07:35.682+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:07:35.755+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:07:35.809+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:07:35.809+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:07:35.852+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:07:35.851+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:07:35.906+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.235 seconds
[2025-06-16T21:08:06.614+0000] {processor.py:157} INFO - Started process (PID=4989) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:08:06.616+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:08:06.618+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:08:06.618+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:08:06.683+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:08:06.735+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:08:06.734+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:08:06.772+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:08:06.771+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:08:06.813+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.204 seconds
[2025-06-16T21:08:37.570+0000] {processor.py:157} INFO - Started process (PID=5011) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:08:37.573+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:08:37.577+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:08:37.576+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:08:37.878+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:08:37.935+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:08:37.935+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:08:37.980+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:08:37.979+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:08:38.022+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.456 seconds
[2025-06-16T21:09:08.717+0000] {processor.py:157} INFO - Started process (PID=5027) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:09:08.719+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:09:08.721+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:09:08.721+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:09:08.802+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:09:08.857+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:09:08.856+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:09:08.898+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:09:08.898+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:09:08.945+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.235 seconds
[2025-06-16T21:09:39.749+0000] {processor.py:157} INFO - Started process (PID=5051) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:09:39.751+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:09:39.753+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:09:39.752+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:09:39.804+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:09:39.856+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:09:39.856+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:09:39.900+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:09:39.899+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:09:39.946+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.201 seconds
[2025-06-16T21:10:10.653+0000] {processor.py:157} INFO - Started process (PID=5074) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:10:10.654+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:10:10.656+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:10:10.656+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:10:10.706+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:10:10.760+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:10:10.759+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:10:10.800+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:10:10.800+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:10:10.848+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.200 seconds
[2025-06-16T21:10:41.838+0000] {processor.py:157} INFO - Started process (PID=5097) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:10:41.840+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:10:41.842+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:10:41.842+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:10:41.915+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:10:41.977+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:10:41.976+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:10:42.021+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:10:42.021+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:10:42.071+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.237 seconds
[2025-06-16T21:11:12.803+0000] {processor.py:157} INFO - Started process (PID=5120) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:11:12.805+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:11:12.808+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:11:12.807+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:11:12.868+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:11:12.918+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:11:12.917+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:11:12.962+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:11:12.962+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:11:13.020+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.222 seconds
[2025-06-16T21:11:43.836+0000] {processor.py:157} INFO - Started process (PID=5143) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:11:43.838+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:11:43.840+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:11:43.840+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:11:43.901+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:11:43.949+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:11:43.949+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:11:43.986+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:11:43.986+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:11:44.032+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.202 seconds
[2025-06-16T21:12:14.979+0000] {processor.py:157} INFO - Started process (PID=5166) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:12:14.982+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:12:14.984+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:12:14.984+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:12:15.048+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:12:15.092+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:12:15.092+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:12:15.131+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:12:15.131+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:12:15.182+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.207 seconds
[2025-06-16T21:12:46.082+0000] {processor.py:157} INFO - Started process (PID=5189) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:12:46.084+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:12:46.086+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:12:46.086+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:12:46.156+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:12:46.198+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:12:46.198+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:12:46.236+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:12:46.235+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:12:46.276+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.199 seconds
[2025-06-16T21:13:17.159+0000] {processor.py:157} INFO - Started process (PID=5212) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:13:17.162+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:13:17.165+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:13:17.164+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:13:17.318+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:13:17.364+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:13:17.364+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:13:17.403+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:13:17.403+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:13:17.446+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.295 seconds
[2025-06-16T21:13:48.262+0000] {processor.py:157} INFO - Started process (PID=5235) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:13:48.268+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:13:48.271+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:13:48.270+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:13:48.335+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:13:48.383+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:13:48.383+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:13:48.428+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:13:48.427+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:13:48.482+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.225 seconds
[2025-06-16T21:14:19.372+0000] {processor.py:157} INFO - Started process (PID=5258) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:14:19.374+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:14:19.376+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:14:19.376+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:14:19.453+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:14:19.515+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:14:19.515+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:14:19.558+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:14:19.558+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:14:19.624+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.260 seconds
[2025-06-16T21:14:50.233+0000] {processor.py:157} INFO - Started process (PID=5281) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:14:50.235+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:14:50.242+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:14:50.238+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:14:50.313+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:14:50.363+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:14:50.362+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:14:50.407+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:14:50.407+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:14:50.459+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.232 seconds
[2025-06-16T21:15:21.063+0000] {processor.py:157} INFO - Started process (PID=5304) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:15:21.066+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:15:21.068+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:15:21.068+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:15:21.149+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:15:21.215+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:15:21.215+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:15:21.267+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:15:21.266+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:15:21.329+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.273 seconds
[2025-06-16T21:15:51.649+0000] {processor.py:157} INFO - Started process (PID=5327) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:15:51.651+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:15:51.653+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:15:51.653+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:15:51.707+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:15:51.760+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:15:51.760+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:15:51.801+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:15:51.801+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:15:51.841+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.200 seconds
[2025-06-16T21:16:22.686+0000] {processor.py:157} INFO - Started process (PID=5342) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:16:22.688+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:16:22.690+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:16:22.689+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:16:22.758+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:16:22.835+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:16:22.834+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:16:22.890+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:16:22.890+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:16:22.953+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.273 seconds
[2025-06-16T21:16:53.470+0000] {processor.py:157} INFO - Started process (PID=5365) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:16:53.472+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:16:53.473+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:16:53.473+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:16:53.514+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:16:53.571+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:16:53.570+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:16:53.617+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:16:53.617+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:16:53.670+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.204 seconds
[2025-06-16T21:17:24.313+0000] {processor.py:157} INFO - Started process (PID=5388) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:17:24.316+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:17:24.318+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:17:24.317+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:17:24.391+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:17:24.447+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:17:24.447+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:17:24.498+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:17:24.498+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:17:24.557+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.249 seconds
[2025-06-16T21:17:54.945+0000] {processor.py:157} INFO - Started process (PID=5411) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:17:54.947+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:17:54.950+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:17:54.949+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:17:55.027+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:17:55.093+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:17:55.093+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:17:55.153+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:17:55.153+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:17:55.215+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.277 seconds
[2025-06-16T21:18:27.130+0000] {processor.py:157} INFO - Started process (PID=5434) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:18:27.142+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:18:27.158+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:18:27.158+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:18:27.480+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:18:27.540+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:18:27.539+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:18:27.592+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:18:27.592+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:18:27.666+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.543 seconds
[2025-06-16T21:18:58.108+0000] {processor.py:157} INFO - Started process (PID=5457) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:18:58.114+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:18:58.122+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:18:58.121+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:18:58.242+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:18:58.302+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:18:58.302+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:18:58.346+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:18:58.346+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:18:58.396+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.294 seconds
[2025-06-16T21:19:29.403+0000] {processor.py:157} INFO - Started process (PID=5480) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:19:29.405+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:19:29.407+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:19:29.407+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:19:29.477+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:19:29.534+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:19:29.534+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:19:29.577+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:19:29.576+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:19:30.107+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.709 seconds
[2025-06-16T21:20:00.796+0000] {processor.py:157} INFO - Started process (PID=5503) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:20:00.799+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:20:00.800+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:20:00.800+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:20:00.863+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:20:00.912+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:20:00.912+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:20:00.963+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:20:00.962+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:20:01.018+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.228 seconds
[2025-06-16T21:20:31.906+0000] {processor.py:157} INFO - Started process (PID=5526) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:20:31.908+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:20:31.911+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:20:31.910+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:20:31.990+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:20:32.049+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:20:32.048+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:20:32.100+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:20:32.099+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:20:32.143+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.244 seconds
[2025-06-16T21:21:02.424+0000] {processor.py:157} INFO - Started process (PID=5549) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:21:02.426+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:21:02.427+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:21:02.427+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:21:02.471+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:21:02.528+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:21:02.527+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:21:02.573+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:21:02.572+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:21:02.629+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.211 seconds
[2025-06-16T21:21:32.873+0000] {processor.py:157} INFO - Started process (PID=5572) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:21:32.875+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:21:32.877+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:21:32.877+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:21:32.930+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:21:32.990+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:21:32.989+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:21:33.208+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:21:33.207+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:21:33.266+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.401 seconds
[2025-06-16T21:22:03.372+0000] {processor.py:157} INFO - Started process (PID=5595) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:22:03.374+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:22:03.376+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:22:03.376+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:22:03.450+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:22:03.505+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:22:03.504+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:22:03.561+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:22:03.561+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:22:03.617+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.251 seconds
[2025-06-16T21:22:34.014+0000] {processor.py:157} INFO - Started process (PID=5618) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:22:34.016+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:22:34.018+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:22:34.018+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:22:34.072+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:22:34.125+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:22:34.124+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:22:34.165+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:22:34.165+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:22:34.204+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.196 seconds
[2025-06-16T21:23:05.014+0000] {processor.py:157} INFO - Started process (PID=5641) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:23:05.017+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:23:05.019+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:23:05.019+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:23:05.076+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:23:05.156+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:23:05.155+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:23:05.208+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:23:05.207+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:23:05.425+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.418 seconds
[2025-06-16T21:23:35.757+0000] {processor.py:157} INFO - Started process (PID=5656) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:23:35.759+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:23:35.761+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:23:35.760+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:23:35.813+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:23:35.883+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:23:35.882+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:23:35.952+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:23:35.952+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:23:36.002+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.250 seconds
[2025-06-16T21:24:06.706+0000] {processor.py:157} INFO - Started process (PID=5679) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:24:06.708+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:24:06.710+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:24:06.709+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:24:06.754+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:24:06.809+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:24:06.809+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:24:07.053+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:24:07.052+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:24:07.108+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.407 seconds
[2025-06-16T21:24:37.822+0000] {processor.py:157} INFO - Started process (PID=5702) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:24:37.825+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:24:37.827+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:24:37.827+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:24:37.915+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:24:37.972+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:24:37.972+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:24:38.018+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:24:38.018+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:24:38.058+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.244 seconds
[2025-06-16T21:25:08.906+0000] {processor.py:157} INFO - Started process (PID=5725) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:25:08.908+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:25:08.911+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:25:08.910+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:25:08.984+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:25:09.041+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:25:09.040+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:25:09.297+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:25:09.297+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:25:09.354+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.454 seconds
[2025-06-16T21:25:39.964+0000] {processor.py:157} INFO - Started process (PID=5748) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:25:39.966+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:25:39.968+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:25:39.968+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:25:40.035+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:25:40.094+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:25:40.093+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:25:40.139+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:25:40.138+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:25:40.176+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.218 seconds
[2025-06-16T21:26:10.887+0000] {processor.py:157} INFO - Started process (PID=5771) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:26:10.889+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:26:10.891+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:26:10.890+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:26:10.952+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:26:11.002+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:26:11.001+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:26:11.040+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:26:11.040+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:26:11.072+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.192 seconds
[2025-06-16T21:26:41.824+0000] {processor.py:157} INFO - Started process (PID=5794) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:26:41.831+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:26:41.834+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:26:41.833+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:26:41.906+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:26:41.967+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:26:41.967+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:26:42.022+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:26:42.022+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:26:42.076+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.262 seconds
[2025-06-16T21:27:13.013+0000] {processor.py:157} INFO - Started process (PID=5817) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:27:13.015+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:27:13.017+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:27:13.017+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:27:13.065+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:27:13.108+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:27:13.108+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:27:13.142+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:27:13.142+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:27:13.183+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.175 seconds
[2025-06-16T21:27:43.501+0000] {processor.py:157} INFO - Started process (PID=5840) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:27:43.504+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:27:43.506+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:27:43.505+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:27:43.586+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:27:43.649+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:27:43.649+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:27:43.690+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:27:43.690+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:27:43.730+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.236 seconds
[2025-06-16T21:28:14.789+0000] {processor.py:157} INFO - Started process (PID=5863) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:28:14.791+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:28:14.793+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:28:14.793+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:28:14.850+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:28:14.909+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:28:14.908+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:28:14.958+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:28:14.958+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:28:15.014+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.230 seconds
[2025-06-16T21:28:45.707+0000] {processor.py:157} INFO - Started process (PID=5886) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:28:45.710+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:28:45.712+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:28:45.711+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:28:45.776+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:28:45.836+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:28:45.836+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:28:45.884+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:28:45.883+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:28:45.932+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.231 seconds
[2025-06-16T21:29:16.783+0000] {processor.py:157} INFO - Started process (PID=5909) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:29:16.785+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:29:16.787+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:29:16.786+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:29:16.839+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:29:16.895+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:29:16.894+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:29:16.935+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:29:16.934+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:29:16.972+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.194 seconds
[2025-06-16T21:29:47.801+0000] {processor.py:157} INFO - Started process (PID=5932) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:29:47.804+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:29:47.807+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:29:47.806+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:29:47.875+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:29:47.921+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:29:47.921+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:29:47.957+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:29:47.956+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:29:48.011+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.215 seconds
[2025-06-16T21:30:18.414+0000] {processor.py:157} INFO - Started process (PID=5955) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:30:18.415+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:30:18.417+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:30:18.417+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:30:18.473+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:30:18.519+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:30:18.518+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:30:18.556+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:30:18.556+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:30:18.586+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.180 seconds
[2025-06-16T21:30:49.373+0000] {processor.py:157} INFO - Started process (PID=5977) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:30:49.375+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:30:49.378+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:30:49.377+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:30:49.443+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:30:49.499+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:30:49.499+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:30:49.553+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:30:49.553+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:30:49.602+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.235 seconds
[2025-06-16T21:31:20.292+0000] {processor.py:157} INFO - Started process (PID=5993) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:31:20.295+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:31:20.297+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:31:20.296+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:31:20.360+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:31:20.427+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:31:20.426+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:31:20.466+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:31:20.465+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:31:20.505+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.219 seconds
[2025-06-16T21:31:51.320+0000] {processor.py:157} INFO - Started process (PID=6016) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:31:51.322+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:31:51.324+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:31:51.324+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:31:51.367+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:31:51.412+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:31:51.412+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:31:51.452+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:31:51.452+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:31:51.496+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.182 seconds
[2025-06-16T21:32:22.127+0000] {processor.py:157} INFO - Started process (PID=6039) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:32:22.129+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:32:22.131+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:32:22.130+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:32:22.189+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:32:22.232+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:32:22.231+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:32:22.270+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:32:22.270+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:32:22.310+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.188 seconds
[2025-06-16T21:32:52.694+0000] {processor.py:157} INFO - Started process (PID=6062) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:32:52.697+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:32:52.699+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:32:52.698+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:32:52.763+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:32:52.808+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:32:52.808+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:32:52.845+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:32:52.845+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:32:52.891+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 0.203 seconds
[2025-06-16T21:33:25.185+0000] {processor.py:157} INFO - Started process (PID=6085) to work on /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:33:25.237+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/bioshort_automation.py for tasks to queue
[2025-06-16T21:33:25.273+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:33:25.272+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:33:27.898+0000] {processor.py:839} INFO - DAG(s) dict_keys(['bioshort_automation']) retrieved from /opt/airflow/dags/bioshort_automation.py
[2025-06-16T21:33:27.987+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:33:27.986+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:33:28.073+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:33:28.073+0000] {dag.py:3677} INFO - Setting next_dagrun for bioshort_automation to 2025-06-16T09:00:00+00:00, run_after=2025-06-17T09:00:00+00:00
[2025-06-16T21:33:28.247+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/bioshort_automation.py took 3.068 seconds
