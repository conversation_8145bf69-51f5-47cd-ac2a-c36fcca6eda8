import React, { useState } from 'react'
import { api } from '../services/api'
import { env } from '../config/env.js'

const YouTubeUploadSection = ({ video, youtubeContent, personName }) => {
  const [isUploading, setIsUploading] = useState(false)
  const [uploadResult, setUploadResult] = useState(null)
  const [uploadError, setUploadError] = useState(null)
  const [showUploadForm, setShowUploadForm] = useState(false)
  const [uploadLanguage, setUploadLanguage] = useState('english')
  const [isTranslating, setIsTranslating] = useState(false)
  const [uploadForm, setUploadForm] = useState({
    title: personName ? `${personName} - AI Biography` : 'AI Biography Video',
    description: personName ? `An inspiring AI-generated biography video about ${personName}.` : 'An inspiring AI-generated biography video.',
    privacy_status: 'private',
    tags: ['biography', 'ai', 'inspiration']
  })

  // Initialize form with YouTube content when available
  React.useEffect(() => {
    console.log('🔍 YouTube content effect triggered:', { youtubeContent, personName })

    if (youtubeContent && youtubeContent.titles && youtubeContent.description) {
      const defaultTitle = youtubeContent.titles[0] || `${personName} - AI Biography`
      const defaultDescription = youtubeContent.description || `An inspiring AI-generated biography video about ${personName}.`

      console.log('📝 Setting form with YouTube content:', {
        title: defaultTitle,
        description: defaultDescription.substring(0, 100) + '...'
      })

      setUploadForm(prev => ({
        ...prev,
        title: defaultTitle,
        description: defaultDescription,
        tags: ['biography', 'ai', 'inspiration', personName.toLowerCase().replace(' ', '')]
      }))
    } else if (personName) {
      // Fallback if no YouTube content is available
      const fallbackTitle = `${personName} - AI Biography`
      const fallbackDescription = `An inspiring AI-generated biography video about ${personName}.`

      console.log('📝 Setting form with fallback content:', {
        title: fallbackTitle,
        description: fallbackDescription
      })

      setUploadForm(prev => ({
        ...prev,
        title: fallbackTitle,
        description: fallbackDescription,
        tags: ['biography', 'ai', 'inspiration', personName.toLowerCase().replace(' ', '')]
      }))
    }
  }, [youtubeContent, personName])

  const handleUpload = async () => {
    if (!video || !video.base64) {
      setUploadError('No video available for upload')
      return
    }

    if (!uploadForm.title.trim()) {
      setUploadError('Please enter a video title')
      return
    }

    try {
      setIsUploading(true)
      setUploadError(null)

      // Check video size before upload
      const videoSizeMB = (video.base64.length * 3) / (4 * 1024 * 1024) // Approximate base64 to bytes conversion
      console.log(`📊 Estimated video size: ${videoSizeMB.toFixed(2)} MB`)

      if (videoSizeMB > 100) {
        throw new Error(`Video file too large (${videoSizeMB.toFixed(2)} MB). Maximum allowed: 100 MB. Try generating a shorter video.`)
      }

      const uploadData = {
        video_base64: video.base64,
        title: uploadForm.title.trim(),
        description: uploadForm.description.trim(),
        privacy_status: uploadForm.privacy_status,
        tags: uploadForm.tags.filter(tag => tag.trim() !== '')
      }

      console.log('🎬 Uploading to YouTube:', {
        title: uploadData.title,
        titleLength: uploadData.title.length,
        description: uploadData.description.substring(0, 100) + '...',
        descriptionLength: uploadData.description.length,
        privacy: uploadData.privacy_status,
        tags: uploadData.tags,
        videoSize: `${videoSizeMB.toFixed(2)} MB`
      })

      // Validate data before sending
      if (!uploadData.title || uploadData.title.length === 0) {
        throw new Error('Title cannot be empty')
      }

      if (uploadData.title.length > 100) {
        throw new Error('Title too long (max 100 characters)')
      }

      const result = await api.uploadToYouTube(uploadData)

      if (result.success) {
        setUploadResult(result)
        setShowUploadForm(false)
        console.log('✅ Upload successful:', result.video_url)
      } else {
        throw new Error(result.error || 'Upload failed')
      }

    } catch (error) {
      console.error('❌ Upload error:', error)
      let errorMessage = error.message || 'Failed to upload video to YouTube'

      // Handle specific error cases
      if (errorMessage.includes('413') || errorMessage.includes('Request Entity Too Large')) {
        errorMessage = 'Video file is too large for upload. Try generating a shorter video or check your internet connection.'
      } else if (errorMessage.includes('500')) {
        errorMessage = 'Server error during upload. Please try again in a few moments.'
      } else if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
        errorMessage = 'Network error. Please check your internet connection and try again.'
      }

      setUploadError(errorMessage)
    } finally {
      setIsUploading(false)
    }
  }

  const handleFormChange = (field, value) => {
    setUploadForm(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleTagsChange = (value) => {
    const tags = value.split(',').map(tag => tag.trim()).filter(tag => tag !== '')
    setUploadForm(prev => ({
      ...prev,
      tags: tags
    }))
  }

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text)
  }

  const changeUploadLanguage = async (newLanguage) => {
    if (newLanguage === uploadLanguage || !youtubeContent) return

    setIsTranslating(true)
    setUploadError(null)

    try {
      const apiBaseUrl = env.API_BASE_URL
      const response = await fetch(`${apiBaseUrl}/generate-youtube-content`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          person_name: personName,
          language: newLanguage,
          story_data: youtubeContent.story_data || null
        })
      })

      const result = await response.json()

      if (result.success) {
        setUploadLanguage(newLanguage)

        // Update upload form with new language content
        const newTitle = result.titles && result.titles[0] ? result.titles[0] : uploadForm.title
        const newDescription = result.description || uploadForm.description

        setUploadForm(prev => ({
          ...prev,
          title: newTitle,
          description: newDescription
        }))
      } else {
        throw new Error(result.error || 'Failed to translate content')
      }
    } catch (error) {
      console.error('Error changing upload language:', error)
      setUploadError(`Failed to translate to ${newLanguage}: ${error.message}`)
    } finally {
      setIsTranslating(false)
    }
  }

  if (!video || !video.base64) {
    return null
  }

  return (
    <div className="bg-slate-800/90 backdrop-blur-xl rounded-3xl shadow-2xl border border-slate-700/50 p-8 fade-in relative overflow-hidden">
      {/* YouTube background effect */}
      <div className="absolute inset-0 bg-gradient-to-br from-red-500/5 to-pink-500/5"></div>
      
      <div className="relative z-10">
        <h3 className="text-white text-2xl font-bold mb-6 flex items-center gap-2">
          📺 Upload to YouTube
        </h3>

        {/* Upload Success */}
        {uploadResult && (
          <div className="bg-green-900/30 border border-green-500/50 rounded-xl p-6 mb-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="text-2xl">🎉</div>
              <div>
                <h4 className="text-green-300 font-bold text-lg">Upload Successful!</h4>
                <p className="text-green-200 text-sm">Your video is now live on YouTube</p>
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <span className="text-green-300 font-medium">Video URL:</span>
                <a 
                  href={uploadResult.video_url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-blue-400 hover:text-blue-300 underline flex-1 truncate"
                >
                  {uploadResult.video_url}
                </a>
                <button
                  onClick={() => copyToClipboard(uploadResult.video_url)}
                  className="bg-slate-700 hover:bg-slate-600 text-white px-3 py-1 rounded-lg text-sm transition-colors"
                >
                  Copy
                </button>
              </div>
              
              <div className="flex items-center gap-2">
                <span className="text-green-300 font-medium">Privacy:</span>
                <span className="text-green-200 capitalize">{uploadResult.privacy_status}</span>
              </div>
            </div>

            <div className="mt-4 flex gap-3">
              <a
                href={uploadResult.video_url}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2"
              >
                <span>🎥</span>
                Watch on YouTube
              </a>
              <button
                onClick={() => {
                  setUploadResult(null)
                  setShowUploadForm(true)
                }}
                className="bg-slate-700 hover:bg-slate-600 text-white px-4 py-2 rounded-lg font-medium transition-colors"
              >
                Upload Another
              </button>
            </div>
          </div>
        )}

        {/* Upload Error */}
        {uploadError && (
          <div className="bg-red-900/30 border border-red-500/50 rounded-xl p-4 mb-6">
            <div className="flex items-center gap-2 text-red-300">
              <span>❌</span>
              <span className="font-medium">Upload Failed:</span>
            </div>
            <p className="text-red-200 mt-1">{uploadError}</p>
          </div>
        )}

        {/* Upload Form */}
        {showUploadForm && !uploadResult && (
          <div className="space-y-6">
            {/* Video Info */}
            <div className="bg-slate-700/30 border border-slate-600/50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-slate-300 font-medium">📊 Video Information</h4>

                {/* Language Toggle for Upload */}
                {youtubeContent && (
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-slate-400">Content Language:</span>
                    <div className="flex bg-slate-600/50 rounded-lg p-1">
                      <button
                        onClick={() => changeUploadLanguage('english')}
                        disabled={isTranslating}
                        className={`px-2 py-1 text-xs font-medium rounded transition-all duration-200 ${
                          uploadLanguage === 'english'
                            ? 'bg-red-500 text-white'
                            : 'text-slate-300 hover:text-white hover:bg-slate-500/50'
                        } ${isTranslating ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                      >
                        🇺🇸 EN
                      </button>
                      <button
                        onClick={() => changeUploadLanguage('hindi')}
                        disabled={isTranslating}
                        className={`px-2 py-1 text-xs font-medium rounded transition-all duration-200 ${
                          uploadLanguage === 'hindi'
                            ? 'bg-red-500 text-white'
                            : 'text-slate-300 hover:text-white hover:bg-slate-500/50'
                        } ${isTranslating ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                      >
                        🇮🇳 HI
                      </button>
                    </div>
                  </div>
                )}
              </div>

              <div className="text-sm text-slate-400 space-y-1">
                <p>Size: ~{video && video.base64 ? ((video.base64.length * 3) / (4 * 1024 * 1024)).toFixed(2) : '0'} MB</p>
                <p>Format: MP4 (YouTube Shorts ready)</p>
                <p>Max upload size: 100 MB</p>
                {isTranslating && (
                  <p className="text-blue-400 flex items-center gap-1">
                    <div className="animate-spin rounded-full h-3 w-3 border border-blue-400 border-t-transparent"></div>
                    Translating content to {uploadLanguage === 'hindi' ? 'Hindi' : 'English'}...
                  </p>
                )}
              </div>
            </div>

            <div>
              <label className="block text-slate-300 font-medium mb-2">Video Title *</label>
              <input
                type="text"
                value={uploadForm.title}
                onChange={(e) => handleFormChange('title', e.target.value)}
                className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-cyan-400 focus:outline-none transition-colors"
                placeholder="Enter video title..."
                maxLength={100}
              />
              <p className="text-slate-400 text-sm mt-1">{uploadForm.title.length}/100 characters</p>
            </div>

            <div>
              <label className="block text-slate-300 font-medium mb-2">Description</label>
              <textarea
                value={uploadForm.description}
                onChange={(e) => handleFormChange('description', e.target.value)}
                className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-cyan-400 focus:outline-none transition-colors h-32 resize-none"
                placeholder="Enter video description..."
                maxLength={5000}
              />
              <p className="text-slate-400 text-sm mt-1">{uploadForm.description.length}/5000 characters</p>
            </div>

            <div>
              <label className="block text-slate-300 font-medium mb-2">Privacy Setting</label>
              <select
                value={uploadForm.privacy_status}
                onChange={(e) => handleFormChange('privacy_status', e.target.value)}
                className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-4 py-3 text-white focus:border-cyan-400 focus:outline-none transition-colors"
              >
                <option value="private">Private (Only you can see)</option>
                <option value="unlisted">Unlisted (Anyone with link can see)</option>
                <option value="public">Public (Everyone can see)</option>
              </select>
            </div>

            <div>
              <label className="block text-slate-300 font-medium mb-2">Tags (comma-separated)</label>
              <input
                type="text"
                value={uploadForm.tags.join(', ')}
                onChange={(e) => handleTagsChange(e.target.value)}
                className="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-cyan-400 focus:outline-none transition-colors"
                placeholder="biography, ai, inspiration..."
              />
            </div>

            <div className="flex gap-3">
              <button
                onClick={handleUpload}
                disabled={isUploading || isTranslating || !uploadForm.title.trim() || uploadForm.title.trim().length === 0}
                className="bg-red-600 hover:bg-red-700 disabled:bg-slate-600 disabled:cursor-not-allowed text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2"
              >
                {isUploading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                    Uploading...
                  </>
                ) : (
                  <>
                    <span>📺</span>
                    Upload to YouTube
                  </>
                )}
              </button>
              
              <button
                onClick={() => setShowUploadForm(false)}
                className="bg-slate-700 hover:bg-slate-600 text-white px-6 py-3 rounded-lg font-medium transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        )}

        {/* Upload Button */}
        {!showUploadForm && !uploadResult && (
          <div className="text-center">
            <p className="text-slate-300 mb-4">
              Ready to share your AI-generated biography video with the world?
            </p>
            <button
              onClick={() => setShowUploadForm(true)}
              className="bg-red-600 hover:bg-red-700 text-white px-8 py-4 rounded-xl font-bold text-lg transition-colors flex items-center gap-3 mx-auto shadow-lg hover:shadow-xl transform hover:scale-105"
            >
              <span className="text-2xl">📺</span>
              Upload to YouTube
            </button>
            <p className="text-slate-400 text-sm mt-3">
              Your video will be uploaded to your connected YouTube account
            </p>
          </div>
        )}
      </div>
    </div>
  )
}

export default YouTubeUploadSection
