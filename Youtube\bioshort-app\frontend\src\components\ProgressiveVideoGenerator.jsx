import React, { useState } from 'react'
import InputForm from './InputForm'
import ErrorSection from './ErrorSection'
import YouTubeContentSection from './YouTubeContentSection'
import YouTubeUploadSection from './YouTubeUploadSection'
import ImageModal from './ImageModal'
import { generateVideo } from '../services/api'
import { useVideoGeneration } from '../context/VideoGenerationContext'

const ProgressiveVideoGenerator = () => {
  const { state, actions } = useVideoGeneration()

  // Local UI states (not persisted)
  const [language, setLanguage] = useState('english')
  const [voiceType, setVoiceType] = useState('hindi_male')
  const [finalResults, setFinalResults] = useState(null)
  const [abortController, setAbortController] = useState(null)

  // Image modal states
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [selectedImageIndex, setSelectedImageIndex] = useState(0)



  const resetState = () => {
    actions.resetGeneration()
    setFinalResults(null)
    setIsModalOpen(false)
    setSelectedImageIndex(0)
    if (abortController) {
      abortController.abort()
      setAbortController(null)
    }
  }

  const handleCancel = () => {
    if (abortController) {
      abortController.abort()
      setAbortController(null)
    }
    actions.cancelGeneration()
  }

  // Image modal handlers
  const openImageModal = (index) => {
    console.log('Opening image modal for index:', index, 'Total images:', state.results.images.length)
    console.log('Image data:', state.results.images[index])
    setSelectedImageIndex(index)
    setIsModalOpen(true)
    console.log('Modal state set to open')
  }

  const closeImageModal = () => {
    setIsModalOpen(false)
  }

  const navigateImage = (index) => {
    setSelectedImageIndex(index)
  }

  const generateStory = async (personName, controller) => {
    try {
      actions.updateProgress(`Generating inspiring story for ${personName}...`, 10)

      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL
      const response = await fetch(`${apiBaseUrl}/generate-story`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ person_name: personName }),
        signal: controller.signal
      })

      const result = await response.json()
      if (result.success) {
        actions.setStory(result.story)
        actions.updateProgress('Story generated! Creating AI images...', 25)
        return result.story
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      throw new Error(`Story generation failed: ${error.message}`)
    }
  }

  const generateImages = async (personName, story, controller) => {
    try {
      actions.updateProgress('Creating AI photorealistic images...', 30)

      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL
      const response = await fetch(`${apiBaseUrl}/generate-images`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          person_name: personName,
          segments: story.segments
        }),
        signal: controller.signal
      })

      const result = await response.json()
      if (result.success) {
        // Add images one by one for progressive display
        result.images.forEach(image => actions.addImage(image))
        actions.updateProgress('Images generated! Creating YouTube content...', 50)
        return result.images
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      throw new Error(`Image generation failed: ${error.message}`)
    }
  }

  const generateYouTubeContent = async (personName, language, storyData = null, controller) => {
    try {
      actions.updateProgress('Generating YouTube titles & description...', 60)

      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL
      const response = await fetch(`${apiBaseUrl}/generate-youtube-content`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          person_name: personName,
          language: language,
          story_data: storyData
        }),
        signal: controller.signal
      })

      const result = await response.json()
      if (result.success) {
        actions.setYoutubeContent(result)
        actions.updateProgress(`Synthesizing ${language} voiceover (${voiceType})...`, 70)
        return result
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      throw new Error(`YouTube content generation failed: ${error.message}`)
    }
  }

  const handleGenerate = async (requestData) => {
    try {
      resetState()

      // Create abort controller for cancellation
      const controller = new AbortController()
      setAbortController(controller)

      const personNameValue = typeof requestData === 'string' ? requestData : requestData.person_name
      const languageValue = typeof requestData === 'object' ? requestData.language : 'english'
      const voiceTypeValue = typeof requestData === 'object' ? requestData.voice_type : 'hindi_male'

      setLanguage(languageValue)
      setVoiceType(voiceTypeValue)

      actions.startGeneration(personNameValue)
      actions.updateProgress(`Starting generation for ${personNameValue}...`, 5)

      // Step 1: Generate story
      const storyResult = await generateStory(personNameValue, controller)

      // Step 2: Generate images
      const imagesResult = await generateImages(personNameValue, storyResult, controller)

      // Step 3: Generate YouTube content based on the story
      actions.updateProgress('Generating YouTube titles & description...', 60)
      const youtubeResult = await generateYouTubeContent(personNameValue, languageValue, storyResult.story, controller)

      // Step 4: Generate complete video using already generated content
      actions.updateProgress('Generating voice and compiling video...', 80)

      // Create request with pre-generated content to avoid regeneration
      const videoRequest = {
        ...requestData,
        pregenerated_story: storyResult,
        pregenerated_images: imagesResult,
        pregenerated_youtube: youtubeResult
      }

      console.log('🎬 Starting final video generation with request:', videoRequest)

      // Add intermediate progress updates
      actions.updateProgress('Synthesizing voiceover...', 85)

      const finalResult = await generateVideo(videoRequest, controller.signal)

      console.log('📹 Final result received:', finalResult)
      console.log('📹 Result type:', typeof finalResult)
      console.log('📹 Result keys:', finalResult ? Object.keys(finalResult) : 'null')

      actions.updateProgress('Processing video...', 95)

      if (finalResult && finalResult.success) {
        console.log('✅ Video generation completed successfully!')

        // Set audio and video in global state
        if (finalResult.audio) {
          actions.setAudio(finalResult.audio)
        }
        if (finalResult.video) {
          actions.setVideo(finalResult.video)
        }

        // Set final results for UI
        setFinalResults(finalResult)

        setAbortController(null) // Clear controller on success

      } else {
        console.error('❌ Video generation failed:', finalResult)
        throw new Error(finalResult?.error || 'Video generation failed - no success response')
      }

    } catch (err) {
      console.error('❌ Generation error:', err)
      console.error('❌ Error name:', err.name)
      console.error('❌ Error message:', err.message)
      console.error('❌ Error stack:', err.stack)

      if (err.name === 'AbortError') {
        console.log('🛑 Generation was cancelled by user')
        actions.cancelGeneration()
      } else {
        console.log('💥 Setting error in state:', err.message)
        actions.setError(err.message)
      }
      setAbortController(null) // Clear controller on error
    }
  }

  const handleRetry = () => {
    resetState()
  }

  const downloadVideo = () => {
    const video = state.results.video
    if (video && video.base64) {
      const link = document.createElement('a')
      link.href = `data:video/mp4;base64,${video.base64}`
      link.download = video.filename || 'bioshort_video.mp4'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  const downloadAudio = () => {
    const audio = state.results.audio
    if (audio && audio.base64) {
      const audioBlob = new Blob([Uint8Array.from(atob(audio.base64), c => c.charCodeAt(0))], { type: 'audio/mpeg' })
      const url = URL.createObjectURL(audioBlob)
      const a = document.createElement('a')
      a.href = url
      a.download = audio.filename || 'audio.mp3'
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } else if (finalResults && finalResults.audio && finalResults.audio.base64) {
      const audioBlob = new Blob([Uint8Array.from(atob(finalResults.audio.base64), c => c.charCodeAt(0))], { type: 'audio/mpeg' })
      const url = URL.createObjectURL(audioBlob)
      const a = document.createElement('a')
      a.href = url
      a.download = finalResults.audio.filename || 'audio.mp3'
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    }
  }

  return (
    <main className="max-w-6xl mx-auto">
      {/* Input Form */}
      <InputForm
        onGenerate={handleGenerate}
        isLoading={state.isGenerating}
      />

      {/* Loading Section with Progress */}
      {state.isGenerating && (
        <div className="bg-slate-800/90 backdrop-blur-xl rounded-3xl shadow-2xl border border-slate-700/50 p-8 mb-8 slide-up relative overflow-hidden">
          {/* Digital loading effects */}
          <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/5 to-purple-500/5"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(59,130,246,0.1),transparent_50%)]"></div>

          <div className="text-center mb-6 relative z-10">
            <div className="text-4xl mb-4">⚡</div>
            <h2 className="text-2xl font-bold text-white mb-2">
              Generating Your Video
            </h2>
            <p className="text-slate-300 mb-6">{state.currentStep}</p>

            {/* Progress Bar */}
            <div className="w-full bg-white/20 rounded-full h-3 mb-4">
              <div
                className="bg-gradient-to-r from-blue-500 to-purple-600 h-3 rounded-full transition-all duration-500 ease-out"
                style={{ width: `${state.progress}%` }}
              ></div>
            </div>
            <p className="text-slate-400 text-sm mb-6">{state.progress}% Complete</p>

            {/* Cancel Button */}
            <button
              onClick={handleCancel}
              className="bg-red-500/20 hover:bg-red-500/30 border border-red-500/50 text-red-300 hover:text-red-200 px-6 py-3 rounded-xl font-medium transition-all duration-300 flex items-center gap-2 mx-auto"
            >
              <span>❌</span>
              Cancel Generation
            </button>
          </div>
        </div>
      )}
      
      {/* Error Section */}
      {state.error && (
        <ErrorSection
          message={state.error}
          onRetry={handleRetry}
        />
      )}
      
      {/* Progressive Results Display */}
      <div className="space-y-8">
        {/* Success Message */}
        {state.personName && (state.results.story || state.results.images.length > 0 || state.results.youtubeContent || finalResults) && (
          <div className="bg-slate-800/90 backdrop-blur-xl rounded-3xl shadow-2xl border border-slate-700/50 p-6 text-center fade-in relative overflow-hidden">
            {/* Success glow effect */}
            <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/10 to-blue-500/10"></div>
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(16,185,129,0.15),transparent_50%)]"></div>

            <div className="relative z-10">
              <div className="text-4xl mb-2">🎉</div>
              <h2 className="text-2xl font-bold text-white mb-2">
                {finalResults ? 'Video Generated Successfully!' : 'Generation in Progress...'}
              </h2>
              <p className="text-slate-300">
                {finalResults ? 'Your AI-powered biography video for' : 'Creating content for'} <span className="font-semibold text-cyan-400">{state.personName}</span> {finalResults ? 'is ready' : ''}
              </p>
            </div>
          </div>
        )}

        {/* Story Section */}
        {state.results.story && (
          <div className="bg-slate-800/90 backdrop-blur-xl rounded-3xl shadow-2xl border border-slate-700/50 p-8 fade-in relative overflow-hidden">
            {/* Story background effect */}
            <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-purple-500/5"></div>

            <div className="relative z-10">
              <h3 className="text-white text-2xl font-bold mb-6 flex items-center gap-2">
                📖 Generated Story
                <span className="text-sm font-normal text-slate-400">({state.results.story.segments.length} segments)</span>
              </h3>
            <div className="space-y-4">
              {state.results.story.segments.map((segment, index) => (
                <div key={index} className="p-4 bg-slate-700/50 rounded-xl border border-slate-600/50 backdrop-blur-sm">
                  <div className="flex items-start gap-3">
                    <span className="bg-gradient-to-r from-cyan-500 to-blue-600 text-white text-xs font-bold px-3 py-1 rounded-full min-w-[2rem] text-center shadow-lg">
                      {index + 1}
                    </span>
                    <div className="flex-1">
                      <p className="text-slate-100 mb-2 leading-relaxed">
                        {segment.text}
                      </p>
                      <p className="text-slate-400 text-sm italic">
                        Scene: {segment.scene_description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            </div>
          </div>
        )}

        {/* Images Section */}
        {state.results.images.length > 0 && (
          <div className="bg-slate-800/90 backdrop-blur-xl rounded-3xl shadow-2xl border border-slate-700/50 p-8 fade-in relative overflow-hidden">
            {/* Images background effect */}
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5"></div>

            <div className="relative z-10">
              <h3 className="text-white text-2xl font-bold mb-4 flex items-center gap-2">
                🎨 Generated Images
                <span className="text-sm font-normal text-slate-400">({state.results.images.length} scenes)</span>
              </h3>
              <p className="text-slate-300 text-sm mb-6">Click on any image to view in full size</p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {state.results.images.map((image, index) => (
                <div
                  key={image.filename}
                  className="relative group cursor-pointer"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Div clicked for image', index);
                    openImageModal(index);
                  }}
                >
                  <img
                    src={`data:image/png;base64,${image.base64}`}
                    alt={`Generated Scene ${index + 1}`}
                    className="w-full h-32 object-cover rounded-xl border-2 border-slate-600/50 hover:border-cyan-400/70 transition-all duration-300 transform hover:scale-105 pointer-events-none shadow-lg"
                  />
                  <div className="absolute bottom-2 left-2 bg-slate-900/80 backdrop-blur-sm text-cyan-300 text-xs px-2 py-1 rounded-lg border border-slate-700/50 pointer-events-none">
                    Scene {index + 1}
                  </div>
                  {/* Click indicator */}
                  <div className="absolute inset-0 bg-slate-900/0 hover:bg-slate-900/30 rounded-xl transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100 pointer-events-none backdrop-blur-sm">
                    <div className="bg-slate-800/90 border border-cyan-400/50 text-cyan-300 px-3 py-1 rounded-full text-sm font-medium shadow-lg">
                      Click to view
                    </div>
                  </div>
                </div>
              ))}
            </div>
            </div>
          </div>
        )}

        {/* YouTube Content Section */}
        {state.results.youtubeContent && (
          <div className="fade-in">
            <YouTubeContentSection
              personName={state.personName}
              language={language}
              storyData={state.results.story}
            />
          </div>
        )}

        {/* Final Video Section */}
        {state.results.video && state.results.video.base64 && (
          <div className="bg-slate-800/90 backdrop-blur-xl rounded-3xl shadow-2xl border border-slate-700/50 p-8 fade-in relative overflow-hidden">
            {/* Video background effect */}
            <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-blue-500/5"></div>

            <div className="relative z-10">
              <h3 className="text-white text-2xl font-bold mb-6">🎥 Your Video</h3>
              <div className="aspect-w-9 aspect-h-16 bg-black rounded-lg overflow-hidden max-w-md mx-auto">
                <video
                  controls
                  className="w-full h-full object-cover"
                  style={{ maxHeight: '600px' }}
                  src={`data:video/mp4;base64,${state.results.video.base64}`}
                >
                  Your browser does not support the video tag.
                </video>
              </div>
            </div>
          </div>
        )}

        {/* Download Section */}
        {(state.results.audio || state.results.video) && (
          <div className="bg-slate-800/90 backdrop-blur-xl rounded-3xl shadow-2xl border border-slate-700/50 p-8 fade-in relative overflow-hidden">
            {/* Download background effect */}
            <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-500/5"></div>

            <div className="relative z-10">
              <h3 className="text-white text-2xl font-bold mb-6">📥 Download</h3>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <button
                onClick={downloadVideo}
                className="flex-1 btn-primary bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700"
              >
                📱 Download Video (MP4)
              </button>
              <button
                onClick={downloadAudio}
                className="flex-1 btn-primary bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700"
              >
                🎵 Download Audio (MP3)
              </button>
            </div>
            </div>
          </div>
        )}

        {/* YouTube Upload Section */}
        {state.results.video && state.results.video.base64 && (
          <div className="fade-in">
            <YouTubeUploadSection
              video={state.results.video}
              youtubeContent={state.results.youtubeContent}
              personName={state.personName}
            />
          </div>
        )}
      </div>

      {/* Image Modal */}
      <ImageModal
        isOpen={isModalOpen}
        onClose={closeImageModal}
        image={state.results.images[selectedImageIndex]}
        imageIndex={selectedImageIndex}
        totalImages={state.results.images.length}
        onNavigate={navigateImage}
      />
    </main>
  )
}

export default ProgressiveVideoGenerator
