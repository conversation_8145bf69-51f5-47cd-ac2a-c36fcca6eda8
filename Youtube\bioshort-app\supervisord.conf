[supervisord]
nodaemon=true
user=root
logfile=/app/logs/supervisord.log
pidfile=/app/logs/supervisord.pid

[program:nginx]
command=nginx -g "daemon off;"
autostart=true
autorestart=true
stderr_logfile=/app/logs/nginx.err.log
stdout_logfile=/app/logs/nginx.out.log

[program:backend]
command=python -m flask run --host=0.0.0.0 --port=5000
directory=/app/backend
environment=FLASK_APP=app.py,FLASK_ENV=production
autostart=true
autorestart=true
stderr_logfile=/app/logs/backend.err.log
stdout_logfile=/app/logs/backend.out.log

[program:airflow-webserver]
command=airflow webserver --port 8080
directory=/app
environment=AIRFLOW_HOME=/app/airflow
autostart=true
autorestart=true
stderr_logfile=/app/logs/airflow-webserver.err.log
stdout_logfile=/app/logs/airflow-webserver.out.log

[program:airflow-scheduler]
command=airflow scheduler
directory=/app
environment=AIRFLOW_HOME=/app/airflow
autostart=true
autorestart=true
stderr_logfile=/app/logs/airflow-scheduler.err.log
stdout_logfile=/app/logs/airflow-scheduler.out.log
