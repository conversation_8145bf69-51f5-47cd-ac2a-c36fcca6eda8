# Logs and temporary files
*.log
logs/
airflow/logs/
backend/logs/
frontend/logs/

# Airflow generated files
airflow/logs/
airflow/__pycache__/
airflow/dags/__pycache__/

# Python cache and temporary files
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.so
.pytest_cache/

# Node.js dependencies and logs
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment files
.env.local
.env.development.local
.env.test.local
.env.production.local

# Database files (if you want to exclude them)
*.db
*.sqlite
*.sqlite3

# Docker volumes and temporary data
volumes/
data/
temp/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Build outputs
dist/
build/
*.egg-info/

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml

# Temporary directories
tmp/
temp/
.tmp/

# Documentation
README.md