import React, { useState, useEffect } from 'react'
import { env } from '../config/env.js'

const InputForm = ({ onGenerate, isLoading }) => {
  const [personName, setPersonName] = useState('')
  const [voiceType, setVoiceType] = useState('hindi_male_madhur')
  const [language, setLanguage] = useState('hindi')
  const [isPlayingPreview, setIsPlayingPreview] = useState(false)
  const [currentAudio, setCurrentAudio] = useState(null)
  const [exampleNames, setExampleNames] = useState([])
  const [isLoadingNames, setIsLoadingNames] = useState(false)
  const [voiceOptions, setVoiceOptions] = useState([])
  const [isLoadingVoices, setIsLoadingVoices] = useState(true)
  const [namesError, setNamesError] = useState(null)

  const handleSubmit = (e) => {
    e.preventDefault()
    if (personName.trim() && !isLoading) {
      onGenerate({
        person_name: personName.trim(),
        voice_type: voiceType,
        language: language
      })
    }
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      handleSubmit(e)
    }
  }

  // Load voice options from API
  useEffect(() => {
    const loadVoiceOptions = async () => {
      try {
        setIsLoadingVoices(true)
        const apiBaseUrl = env.API_BASE_URL
        const response = await fetch(`${apiBaseUrl}/voice-options`)
        const data = await response.json()

        if (data.success) {
          // Convert API response to flat array for UI
          const flatOptions = []

          // Hindi Male voices
          data.voice_options.hindi_male.forEach(voice => {
            flatOptions.push({
              value: voice.id,
              label: `🇮🇳 ${voice.name}`,
              description: voice.description,
              language: 'hindi',
              category: 'Hindi Male'
            })
          })

          // Hindi Female voices
          data.voice_options.hindi_female.forEach(voice => {
            flatOptions.push({
              value: voice.id,
              label: `🇮🇳 ${voice.name}`,
              description: voice.description,
              language: 'hindi',
              category: 'Hindi Female'
            })
          })

          // English voices
          data.voice_options.english_male.forEach(voice => {
            flatOptions.push({
              value: voice.id,
              label: `🇺🇸 ${voice.name}`,
              description: voice.description,
              language: 'english',
              category: 'English Male'
            })
          })

          data.voice_options.english_female.forEach(voice => {
            flatOptions.push({
              value: voice.id,
              label: `🇺🇸 ${voice.name}`,
              description: voice.description,
              language: 'english',
              category: 'English Female'
            })
          })

          setVoiceOptions(flatOptions)
        } else {
          console.error('Failed to load voice options:', data.error)
          // Fallback to basic options
          setVoiceOptions([
            { value: 'hindi_male_madhur', label: '🇮🇳 Hindi Male', language: 'hindi', category: 'Hindi Male' },
            { value: 'hindi_female_swara', label: '🇮🇳 Hindi Female', language: 'hindi', category: 'Hindi Female' },
            { value: 'english_male', label: '🇺🇸 English Male', language: 'english', category: 'English Male' },
            { value: 'english_female', label: '🇺🇸 English Female', language: 'english', category: 'English Female' }
          ])
        }
      } catch (error) {
        console.error('Error loading voice options:', error)
        // Fallback to basic options
        setVoiceOptions([
          { value: 'hindi_male_madhur', label: '🇮🇳 Hindi Male', language: 'hindi', category: 'Hindi Male' },
          { value: 'hindi_female_swara', label: '🇮🇳 Hindi Female', language: 'hindi', category: 'Hindi Female' },
          { value: 'english_male', label: '🇺🇸 English Male', language: 'english', category: 'English Male' },
          { value: 'english_female', label: '🇺🇸 English Female', language: 'english', category: 'English Female' }
        ])
      } finally {
        setIsLoadingVoices(false)
      }
    }

    loadVoiceOptions()
  }, [])

  // Load random names on component mount
  useEffect(() => {
    fetchRandomNames()
  }, [])

  const fetchRandomNames = async () => {
    console.log('🔄 Fetching random names...')
    setIsLoadingNames(true)
    setNamesError(null)

    try {
      // Add multiple cache-busting parameters to ensure completely fresh results
      const timestamp = Date.now()
      const randomId = Math.random().toString(36).substring(2, 15)
      const sessionId = Math.random().toString(36).substring(2, 10)
      const apiBaseUrl = env.API_BASE_URL
      const url = `${apiBaseUrl}/generate-random-names?t=${timestamp}&r=${randomId}&s=${sessionId}&fresh=true`

      const response = await fetch(url, {
        method: 'GET',
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      })

      console.log('📡 Response status:', response.status)
      console.log('📡 Response ok:', response.ok)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      console.log('📡 Response data:', result)

      if (result.success) {
        setExampleNames(result.names)
        console.log('✅ New names loaded:', result.names)
      } else {
        throw new Error(result.error || 'Failed to fetch names')
      }
    } catch (error) {
      console.error('❌ Error fetching random names:', error)
      setNamesError(`Failed to load names: ${error.message}`)
      // Fallback to static names if API fails
      console.log('🔄 Using fallback names')
      setExampleNames([
        'Albert Einstein', 'Marie Curie', 'Nelson Mandela', 'Leonardo da Vinci',
        'Frida Kahlo', 'Steve Jobs', 'Maya Angelou', 'Mahatma Gandhi'
      ])
    } finally {
      setIsLoadingNames(false)
    }
  }

  const handleVoiceChange = (selectedVoice) => {
    setVoiceType(selectedVoice)
    const voiceOption = voiceOptions.find(v => v.value === selectedVoice)
    if (voiceOption) {
      setLanguage(voiceOption.language)
    }
  }

  const playVoicePreview = async (selectedVoice) => {
    try {
      console.log(`🎤 Starting voice preview for: ${selectedVoice}`)

      // Stop current audio if playing
      if (currentAudio) {
        currentAudio.pause()
        currentAudio.currentTime = 0
        setCurrentAudio(null)
      }

      setIsPlayingPreview(true)

      const voiceOption = voiceOptions.find(v => v.value === selectedVoice)
      console.log(`🔍 Voice option found:`, voiceOption)

      const requestData = {
        voice_type: selectedVoice,
        language: voiceOption?.language || 'hindi'
      }
      console.log(`📡 Sending request:`, requestData)
      const apiBaseUrl = env.API_BASE_URL
      const response = await fetch(`${apiBaseUrl}/voice-sample`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
      })

      console.log(`📡 Response status: ${response.status}`)

      if (!response.ok) {
        throw new Error('Failed to generate voice sample')
      }

      const result = await response.json()

      if (result.success) {
        console.log('🎤 Voice sample result:', {
          type: result.type,
          hasAudioBase64: !!result.audio_base64,
          filename: result.filename
        })

        // Play the audio sample from base64 data (memory-based)
        if (result.audio_base64) {
          try {
            console.log('🎵 Creating audio blob from base64 data...')

            // Decode base64 to binary data
            const binaryString = atob(result.audio_base64)
            const bytes = new Uint8Array(binaryString.length)
            for (let i = 0; i < binaryString.length; i++) {
              bytes[i] = binaryString.charCodeAt(i)
            }

            // Create blob with proper MIME type
            const audioBlob = new Blob([bytes], { type: 'audio/mpeg' })
            console.log(`📊 Audio blob created: ${audioBlob.size} bytes`)

            const audioUrl = URL.createObjectURL(audioBlob)
            const audio = new Audio(audioUrl)

            // Set up event handlers before loading
            audio.oncanplaythrough = () => {
              console.log('🎵 Audio can play through, starting playback...')
              audio.play().then(() => {
                setCurrentAudio(audio)
                console.log('✅ Playing voice sample from memory')
              }).catch(err => {
                console.error('❌ Play failed:', err)
                setIsPlayingPreview(false)
                setCurrentAudio(null)
                URL.revokeObjectURL(audioUrl)
              })
            }

            audio.onended = () => {
              setIsPlayingPreview(false)
              setCurrentAudio(null)
              URL.revokeObjectURL(audioUrl) // Clean up blob URL
              console.log('🔇 Voice sample playback ended')
            }

            audio.onerror = (e) => {
              setIsPlayingPreview(false)
              setCurrentAudio(null)
              URL.revokeObjectURL(audioUrl) // Clean up blob URL
              console.error('❌ Error playing voice sample:', e)
              console.error('Audio error details:', audio.error)
            }

            // Load the audio
            audio.load()

          } catch (blobError) {
            console.error('❌ Error creating audio blob:', blobError)
            setIsPlayingPreview(false)
          }
        } else {
          // Fallback: try file-based approach (for backward compatibility)
          console.log('⚠️ No base64 audio data, trying file-based approach...')
          const apiBaseUrl = env.API_BASE_URL
          const audioUrl = `${apiBaseUrl}/download-audio/${result.filename}`
          const audio = new Audio(audioUrl)

          audio.onloadeddata = () => {
            audio.play()
            setCurrentAudio(audio)
          }

          audio.onended = () => {
            setIsPlayingPreview(false)
            setCurrentAudio(null)
          }

          audio.onerror = () => {
            setIsPlayingPreview(false)
            setCurrentAudio(null)
            console.error('Error playing voice sample from file')
          }
        }
      } else {
        throw new Error(result.error || 'Voice sample generation failed')
      }

    } catch (error) {
      console.error('Voice preview error:', error)
      setIsPlayingPreview(false)
      // You could show a toast notification here
    }
  }

  const handleExampleClick = (name) => {
    setPersonName(name)
  }

  return (
    <section className="bg-slate-800/90 backdrop-blur-xl rounded-3xl shadow-2xl border border-slate-700/50 p-8 mb-8 slide-up relative overflow-hidden">
      {/* Digital background effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/5 to-purple-500/5"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.1),transparent_50%)]"></div>

      <div className="max-w-2xl mx-auto relative z-10">
        <h2 className="text-2xl font-bold text-white mb-6 text-center">
          Enter a Famous Person's Name
        </h2>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label className="block text-slate-300 text-sm font-medium mb-2">
              Famous Person's Name
            </label>
            <input
              type="text"
              value={personName}
              onChange={(e) => setPersonName(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="e.g., Albert Einstein, Marie Curie, Gandhi..."
              className="input-field text-lg"
              disabled={isLoading}
              autoFocus
            />
          </div>

          {/* Voice Selection */}
          <div>
            <label className="block text-slate-300 text-sm font-medium mb-3">
              🎤 Voice & Language Options
            </label>
            {isLoadingVoices ? (
              <div className="flex items-center justify-center p-8 bg-slate-700/30 rounded-xl border border-slate-600/50">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-cyan-400"></div>
                <span className="ml-3 text-slate-300">Loading voice options...</span>
              </div>
            ) : (
              <>
                {/* Group voices by category */}
                {['Hindi Male', 'Hindi Female', 'English Male', 'English Female'].map(category => {
                  const categoryVoices = voiceOptions.filter(voice => voice.category === category)
                  if (categoryVoices.length === 0) return null

                  return (
                    <div key={category} className="mb-4">
                      <h4 className="text-slate-400 text-xs font-medium mb-2 uppercase tracking-wide">
                        {category}
                      </h4>
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                        {categoryVoices.map((voice) => (
                          <div
                            key={voice.value}
                            className={`relative p-3 rounded-xl border-2 transition-all duration-200 backdrop-blur-sm ${
                              voiceType === voice.value
                                ? 'border-cyan-400/70 bg-cyan-500/20 shadow-lg shadow-cyan-500/20'
                                : 'border-slate-600/50 bg-slate-700/30 hover:border-cyan-400/50 hover:bg-slate-700/50'
                            }`}
                          >
                            <button
                              type="button"
                              onClick={() => handleVoiceChange(voice.value)}
                              disabled={isLoading}
                              className={`w-full text-left ${isLoading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                            >
                              <div className={`text-sm font-medium ${
                                voiceType === voice.value ? 'text-white' : 'text-slate-300'
                              }`}>
                                {voice.label}
                              </div>
                              {voice.description && (
                                <div className="text-xs text-slate-400 mt-1">
                                  {voice.description}
                                </div>
                              )}
                            </button>

                            {/* Voice Preview Button */}
                            <button
                              type="button"
                              onClick={(e) => {
                                e.stopPropagation()
                                playVoicePreview(voice.value)
                              }}
                              disabled={isLoading || isPlayingPreview}
                              className={`absolute top-2 right-2 w-8 h-8 rounded-full flex items-center justify-center transition-all duration-200 ${
                                isPlayingPreview
                                  ? 'bg-green-500/30 text-green-300 animate-pulse'
                                  : 'bg-blue-500/30 text-blue-300 hover:bg-blue-500/50 hover:text-white'
                              } ${isLoading || isPlayingPreview ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                              title={isPlayingPreview ? "Playing voice sample..." : "Preview voice"}
                            >
                              {isPlayingPreview ? '🎵' : '🔊'}
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )
                })}
              </>
            )}
            <p className="text-slate-400 text-xs mt-2">
              Selected: {voiceOptions.find(v => v.value === voiceType)?.label}
              {language === 'hindi' && ' (Story will be translated to Hindi)'}
              <br />
              💡 Click the 🔊 button to preview each voice before generating your video
            </p>
          </div>
          
          <button
            type="submit"
            disabled={!personName.trim() || isLoading}
            className={`w-full btn-primary text-lg py-4 ${
              (!personName.trim() || isLoading) 
                ? 'opacity-50 cursor-not-allowed transform-none' 
                : ''
            }`}
          >
            {isLoading ? (
              <div className="flex items-center justify-center gap-3">
                <div className="loading-spinner"></div>
                Generating...
              </div>
            ) : (
              '🎬 Generate Video'
            )}
          </button>
        </form>
        
        {/* Example Names */}
        <div className="mt-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <p className="text-slate-300 text-sm">
              Or try one of these AI-generated examples:
            </p>
            <button
              onClick={fetchRandomNames}
              disabled={isLoading || isLoadingNames}
              className={`p-2 rounded-full transition-all duration-200 ${
                isLoadingNames
                  ? 'bg-blue-500/30 text-blue-300 animate-spin'
                  : 'bg-blue-500/20 text-blue-300 hover:bg-blue-500/40 hover:text-white'
              } ${(isLoading || isLoadingNames) ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
              title="Generate new random names"
            >
              {isLoadingNames ? '⟳' : '🔄'}
            </button>
          </div>

          {namesError && (
            <p className="text-red-400 text-xs text-center mb-3">
              {namesError} (showing fallback names)
            </p>
          )}

          <div className="flex flex-wrap gap-2 justify-center">
            {isLoadingNames ? (
              <div className="flex items-center gap-2 text-blue-300 text-sm">
                <div className="loading-spinner w-4 h-4"></div>
                Generating new names...
              </div>
            ) : (
              exampleNames.map((name) => (
                <button
                  key={name}
                  onClick={() => handleExampleClick(name)}
                  disabled={isLoading}
                  className="btn-secondary text-xs px-3 py-1 hover:bg-blue-500/30 disabled:opacity-50 transition-all duration-200"
                >
                  {name}
                </button>
              ))
            )}
          </div>
        </div>
        
        {/* Instructions */}
        <div className="mt-8 p-4 bg-slate-700/30 rounded-xl border border-slate-600/50 backdrop-blur-sm">
          <h3 className="text-slate-200 font-semibold mb-2">How it works:</h3>
          <ol className="text-slate-300 text-sm space-y-1">
            <li>1. Enter any famous person's name</li>
            <li>2. AI generates an inspiring biographical story</li>
            <li>3. Creates animated scene images for each segment</li>
            <li>4. Adds natural voice narration</li>
            <li>5. Compiles everything into a 1-minute video</li>
          </ol>
        </div>
      </div>
    </section>
  )
}

export default InputForm
