[2025-06-16T19:12:32.261+0000] {processor.py:157} INFO - Started process (PID=184) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:12:32.264+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:12:32.266+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:12:32.266+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:12:32.320+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:12:32.368+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:12:32.368+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:12:32.393+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:12:32.393+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:12:32.435+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.179 seconds
[2025-06-16T19:13:03.186+0000] {processor.py:157} INFO - Started process (PID=212) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:13:03.188+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:13:03.192+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:13:03.191+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:13:03.276+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:13:03.341+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:13:03.340+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:13:03.361+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:13:03.361+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:13:03.400+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.222 seconds
[2025-06-16T19:13:34.265+0000] {processor.py:157} INFO - Started process (PID=235) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:13:34.269+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:13:34.272+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:13:34.271+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:13:34.352+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:13:34.407+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:13:34.406+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:13:34.427+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:13:34.427+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:13:34.469+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.214 seconds
[2025-06-16T19:14:05.207+0000] {processor.py:157} INFO - Started process (PID=258) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:14:05.210+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:14:05.214+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:14:05.213+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:14:05.297+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:14:05.376+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:14:05.375+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:14:05.406+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:14:05.406+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:14:05.634+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.437 seconds
[2025-06-16T19:14:36.244+0000] {processor.py:157} INFO - Started process (PID=284) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:14:36.246+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:14:36.248+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:14:36.248+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:14:36.309+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:14:36.361+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:14:36.360+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:14:36.380+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:14:36.380+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:14:36.417+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.178 seconds
[2025-06-16T19:15:07.131+0000] {processor.py:157} INFO - Started process (PID=305) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:15:07.134+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:15:07.136+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:15:07.135+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:15:07.202+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:15:07.251+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:15:07.250+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:15:07.271+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:15:07.271+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:15:07.309+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.185 seconds
[2025-06-16T19:15:38.077+0000] {processor.py:157} INFO - Started process (PID=328) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:15:38.081+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:15:38.084+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:15:38.083+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:15:38.177+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:15:38.250+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:15:38.249+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:15:38.283+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:15:38.283+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:15:38.329+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.262 seconds
[2025-06-16T19:16:08.871+0000] {processor.py:157} INFO - Started process (PID=353) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:16:08.873+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:16:08.875+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:16:08.874+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:16:08.955+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:16:09.011+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:16:09.010+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:16:09.029+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:16:09.029+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:16:09.068+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.204 seconds
[2025-06-16T19:16:39.775+0000] {processor.py:157} INFO - Started process (PID=374) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:16:39.777+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:16:39.779+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:16:39.779+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:16:39.848+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:16:39.898+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:16:39.897+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:16:39.918+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:16:39.918+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:16:39.958+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.187 seconds
[2025-06-16T19:17:10.708+0000] {processor.py:157} INFO - Started process (PID=397) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:17:10.711+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:17:10.715+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:17:10.714+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:17:10.792+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:17:10.845+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:17:10.845+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:17:10.869+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:17:10.868+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:17:10.904+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.208 seconds
[2025-06-16T19:17:41.853+0000] {processor.py:157} INFO - Started process (PID=422) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:17:41.855+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:17:41.858+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:17:41.857+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:17:41.929+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:17:41.981+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:17:41.981+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:17:42.002+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:17:42.001+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:17:42.042+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.195 seconds
[2025-06-16T19:18:12.765+0000] {processor.py:157} INFO - Started process (PID=445) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:18:12.769+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:18:12.772+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:18:12.771+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:18:12.861+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:18:12.917+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:18:12.917+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:18:12.941+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:18:12.941+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:18:12.973+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.217 seconds
[2025-06-16T19:18:43.281+0000] {processor.py:157} INFO - Started process (PID=468) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:18:43.284+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:18:43.286+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:18:43.286+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:18:43.360+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:18:43.423+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:18:43.422+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:18:43.448+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:18:43.447+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:18:43.493+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.218 seconds
[2025-06-16T19:19:14.195+0000] {processor.py:157} INFO - Started process (PID=491) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:19:14.197+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:19:14.199+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:19:14.198+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:19:14.271+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:19:14.325+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:19:14.324+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:19:14.345+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:19:14.345+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:19:14.384+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.196 seconds
[2025-06-16T19:19:45.119+0000] {processor.py:157} INFO - Started process (PID=514) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:19:45.121+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:19:45.124+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:19:45.123+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:19:45.201+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:19:45.259+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:19:45.257+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:19:45.282+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:19:45.282+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:19:45.343+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.230 seconds
[2025-06-16T19:20:15.757+0000] {processor.py:157} INFO - Started process (PID=539) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:20:15.759+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:20:15.760+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:20:15.760+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:20:15.827+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:20:15.892+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:20:15.891+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:20:15.917+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:20:15.916+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:20:15.956+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.204 seconds
[2025-06-16T19:20:46.089+0000] {processor.py:157} INFO - Started process (PID=552) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:20:46.091+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:20:46.094+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:20:46.093+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:20:46.165+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:20:46.222+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:20:46.221+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:20:46.247+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:20:46.246+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:20:46.295+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.214 seconds
[2025-06-16T19:21:17.041+0000] {processor.py:157} INFO - Started process (PID=576) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:21:17.044+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:21:17.047+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:21:17.046+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:21:17.126+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:21:17.183+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:21:17.182+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:21:17.206+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:21:17.206+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:21:17.247+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.214 seconds
[2025-06-16T19:21:47.976+0000] {processor.py:157} INFO - Started process (PID=599) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:21:47.978+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:21:47.980+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:21:47.980+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:21:48.050+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:21:48.170+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:21:48.169+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:21:48.194+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:21:48.193+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:21:48.230+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.263 seconds
[2025-06-16T19:22:18.946+0000] {processor.py:157} INFO - Started process (PID=622) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:22:18.948+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:22:18.950+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:22:18.950+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:22:19.016+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:22:19.066+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:22:19.065+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:22:19.087+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:22:19.087+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:22:19.124+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.186 seconds
[2025-06-16T19:22:50.080+0000] {processor.py:157} INFO - Started process (PID=645) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:22:50.082+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:22:50.084+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:22:50.083+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:22:50.155+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:22:50.202+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:22:50.201+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:22:50.224+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:22:50.224+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:22:50.258+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.184 seconds
[2025-06-16T19:23:21.028+0000] {processor.py:157} INFO - Started process (PID=668) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:23:21.030+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:23:21.033+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:23:21.032+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:23:21.111+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:23:21.181+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:23:21.180+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:23:21.202+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:23:21.201+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:23:21.240+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.219 seconds
[2025-06-16T19:23:51.958+0000] {processor.py:157} INFO - Started process (PID=691) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:23:51.960+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:23:51.963+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:23:51.962+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:23:52.040+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:23:52.098+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:23:52.098+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:23:52.118+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:23:52.118+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:23:52.152+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.202 seconds
[2025-06-16T19:24:22.396+0000] {processor.py:157} INFO - Started process (PID=714) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:24:22.398+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:24:22.400+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:24:22.399+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:24:22.467+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:24:22.527+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:24:22.526+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:24:22.551+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:24:22.550+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:24:22.588+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.199 seconds
[2025-06-16T19:24:53.291+0000] {processor.py:157} INFO - Started process (PID=737) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:24:53.293+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:24:53.295+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:24:53.294+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:24:53.361+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:24:53.416+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:24:53.416+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:24:53.441+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:24:53.440+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:24:53.478+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.193 seconds
[2025-06-16T19:25:24.009+0000] {processor.py:157} INFO - Started process (PID=762) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:25:24.012+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:25:24.015+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:25:24.014+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:25:24.092+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:25:24.152+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:25:24.151+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:25:24.175+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:25:24.175+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:25:24.223+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.223 seconds
[2025-06-16T19:25:54.400+0000] {processor.py:157} INFO - Started process (PID=785) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:25:54.402+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:25:54.404+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:25:54.403+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:25:54.473+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:25:54.525+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:25:54.524+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:25:54.549+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:25:54.549+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:25:54.584+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.190 seconds
[2025-06-16T19:26:24.931+0000] {processor.py:157} INFO - Started process (PID=809) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:26:24.935+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:26:24.939+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:26:24.938+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:26:25.007+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:26:25.072+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:26:25.071+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:26:25.095+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:26:25.095+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:26:25.141+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.217 seconds
[2025-06-16T19:26:55.853+0000] {processor.py:157} INFO - Started process (PID=830) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:26:55.855+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:26:55.857+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:26:55.856+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:26:55.926+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:26:55.969+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:26:55.968+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:26:55.986+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:26:55.986+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:26:56.023+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.175 seconds
[2025-06-16T19:27:26.708+0000] {processor.py:157} INFO - Started process (PID=853) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:27:26.711+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:27:26.713+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:27:26.712+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:27:26.774+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:27:26.820+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:27:26.819+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:27:26.841+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:27:26.840+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:27:26.874+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.172 seconds
[2025-06-16T19:27:57.889+0000] {processor.py:157} INFO - Started process (PID=875) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:27:57.892+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:27:57.894+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:27:57.893+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:27:57.964+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:27:58.020+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:27:58.020+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:27:58.048+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:27:58.047+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:27:58.093+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.210 seconds
[2025-06-16T19:28:28.895+0000] {processor.py:157} INFO - Started process (PID=891) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:28:28.898+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:28:28.901+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:28:28.900+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:28:28.976+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:28:29.034+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:28:29.034+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:28:29.054+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:28:29.054+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:28:29.086+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.199 seconds
[2025-06-16T19:28:59.803+0000] {processor.py:157} INFO - Started process (PID=914) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:28:59.806+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:28:59.810+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:28:59.809+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:28:59.871+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:28:59.920+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:28:59.919+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:28:59.943+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:28:59.943+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:28:59.977+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.182 seconds
[2025-06-16T19:29:30.688+0000] {processor.py:157} INFO - Started process (PID=937) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:29:30.690+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:29:30.692+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:29:30.691+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:29:30.761+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:29:30.810+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:29:30.809+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:29:30.829+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:29:30.828+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:29:30.866+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.184 seconds
[2025-06-16T19:30:01.597+0000] {processor.py:157} INFO - Started process (PID=960) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:30:01.598+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:30:01.601+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:30:01.600+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:30:01.679+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:30:01.732+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:30:01.732+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:30:01.754+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:30:01.754+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:30:01.786+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.196 seconds
[2025-06-16T19:30:32.447+0000] {processor.py:157} INFO - Started process (PID=983) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:30:32.449+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:30:32.452+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:30:32.451+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:30:32.532+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:30:32.582+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:30:32.581+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:30:32.600+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:30:32.600+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:30:32.636+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.196 seconds
[2025-06-16T19:31:03.369+0000] {processor.py:157} INFO - Started process (PID=1006) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:31:03.372+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:31:03.374+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:31:03.373+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:31:03.443+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:31:03.493+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:31:03.492+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:31:03.514+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:31:03.514+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:31:03.547+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.185 seconds
[2025-06-16T19:31:34.300+0000] {processor.py:157} INFO - Started process (PID=1029) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:31:34.302+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:31:34.305+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:31:34.304+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:31:34.378+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:31:34.431+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:31:34.430+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:31:34.454+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:31:34.454+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:31:34.488+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.194 seconds
[2025-06-16T19:32:05.237+0000] {processor.py:157} INFO - Started process (PID=1052) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:32:05.240+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:32:05.243+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:32:05.242+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:32:05.310+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:32:05.367+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:32:05.366+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:32:05.389+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:32:05.388+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:32:05.434+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.204 seconds
[2025-06-16T19:32:35.827+0000] {processor.py:157} INFO - Started process (PID=1078) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:32:35.830+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:32:35.831+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:32:35.831+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:32:35.896+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:32:35.946+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:32:35.945+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:32:35.970+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:32:35.969+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:32:36.011+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.190 seconds
[2025-06-16T19:33:06.691+0000] {processor.py:157} INFO - Started process (PID=1099) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:33:06.693+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:33:06.696+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:33:06.695+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:33:06.762+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:33:06.818+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:33:06.818+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:33:06.838+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:33:06.838+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:33:06.871+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.186 seconds
[2025-06-16T19:33:37.536+0000] {processor.py:157} INFO - Started process (PID=1122) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:33:37.539+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:33:37.541+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:33:37.540+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:33:37.618+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:33:37.671+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:33:37.671+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:33:37.692+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:33:37.692+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:33:37.729+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.199 seconds
[2025-06-16T19:34:08.429+0000] {processor.py:157} INFO - Started process (PID=1145) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:34:08.431+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:34:08.433+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:34:08.433+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:34:08.513+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:34:08.570+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:34:08.569+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:34:08.594+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:34:08.594+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:34:08.632+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.211 seconds
[2025-06-16T19:34:39.449+0000] {processor.py:157} INFO - Started process (PID=1169) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:34:39.454+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:34:39.456+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:34:39.455+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:34:39.542+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:34:39.604+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:34:39.603+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:34:39.630+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:34:39.630+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:34:39.665+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.231 seconds
[2025-06-16T19:35:10.366+0000] {processor.py:157} INFO - Started process (PID=1192) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:35:10.369+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:35:10.371+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:35:10.371+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:35:10.444+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:35:10.490+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:35:10.490+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:35:10.509+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:35:10.509+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:35:10.554+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.194 seconds
[2025-06-16T19:35:42.403+0000] {processor.py:157} INFO - Started process (PID=1215) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:35:42.411+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:35:42.417+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:35:42.417+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:35:42.641+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:35:42.704+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:35:42.703+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:35:42.729+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:35:42.729+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:35:42.774+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.376 seconds
[2025-06-16T19:36:13.541+0000] {processor.py:157} INFO - Started process (PID=1230) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:36:13.543+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:36:13.545+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:36:13.544+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:36:13.622+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:36:13.672+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:36:13.672+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:36:13.692+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:36:13.692+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:36:13.729+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.195 seconds
[2025-06-16T19:36:44.537+0000] {processor.py:157} INFO - Started process (PID=1253) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:36:44.540+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:36:44.542+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:36:44.542+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:36:44.628+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:36:44.688+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:36:44.687+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:36:44.711+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:36:44.711+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:36:44.751+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.222 seconds
[2025-06-16T19:37:15.520+0000] {processor.py:157} INFO - Started process (PID=1276) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:37:15.522+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:37:15.524+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:37:15.523+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:37:15.596+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:37:15.655+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:37:15.654+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:37:15.683+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:37:15.683+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:37:15.734+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.220 seconds
[2025-06-16T19:37:46.047+0000] {processor.py:157} INFO - Started process (PID=1299) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:37:46.050+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:37:46.052+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:37:46.051+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:37:46.128+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:37:46.193+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:37:46.192+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:37:46.215+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:37:46.215+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:37:46.258+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.217 seconds
[2025-06-16T19:38:16.713+0000] {processor.py:157} INFO - Started process (PID=1324) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:38:16.716+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:38:16.719+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:38:16.718+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:38:16.806+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:38:16.863+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:38:16.862+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:38:16.886+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:38:16.885+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:38:16.931+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.226 seconds
[2025-06-16T19:38:47.059+0000] {processor.py:157} INFO - Started process (PID=1347) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:38:47.061+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:38:47.063+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:38:47.062+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:38:47.139+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:38:47.187+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:38:47.186+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:38:47.206+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:38:47.206+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:38:47.251+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.198 seconds
[2025-06-16T19:39:18.019+0000] {processor.py:157} INFO - Started process (PID=1369) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:39:18.023+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:39:18.026+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:39:18.025+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:39:18.115+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:39:18.171+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:39:18.171+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:39:18.198+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:39:18.197+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:39:18.238+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.227 seconds
[2025-06-16T19:39:49.000+0000] {processor.py:157} INFO - Started process (PID=1393) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:39:49.002+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:39:49.004+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:39:49.004+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:39:49.074+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:39:49.142+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:39:49.141+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:39:49.162+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:39:49.161+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:39:49.194+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.202 seconds
[2025-06-16T19:40:19.993+0000] {processor.py:157} INFO - Started process (PID=1416) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:40:19.996+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:40:19.999+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:40:19.998+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:40:20.089+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:40:20.149+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:40:20.148+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:40:20.180+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:40:20.179+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:40:20.211+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.227 seconds
[2025-06-16T19:40:51.010+0000] {processor.py:157} INFO - Started process (PID=1439) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:40:51.014+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:40:51.017+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:40:51.016+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:40:51.097+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:40:51.151+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:40:51.150+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:40:51.180+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:40:51.180+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:40:51.215+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.214 seconds
[2025-06-16T19:41:21.486+0000] {processor.py:157} INFO - Started process (PID=1462) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:41:21.490+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:41:21.493+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:41:21.492+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:41:21.572+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:41:21.627+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:41:21.626+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:41:21.648+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:41:21.648+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:41:21.686+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.208 seconds
[2025-06-16T19:41:52.068+0000] {processor.py:157} INFO - Started process (PID=1487) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:41:52.071+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:41:52.073+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:41:52.073+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:41:52.146+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:41:52.201+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:41:52.200+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:41:52.223+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:41:52.222+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:41:52.264+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.204 seconds
[2025-06-16T19:42:22.433+0000] {processor.py:157} INFO - Started process (PID=1510) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:42:22.435+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:42:22.436+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:42:22.436+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:42:22.498+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:42:22.547+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:42:22.546+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:42:22.568+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:42:22.568+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:42:22.619+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.192 seconds
[2025-06-16T19:42:52.913+0000] {processor.py:157} INFO - Started process (PID=1533) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:42:52.915+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:42:52.917+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:42:52.917+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:42:52.978+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:42:53.025+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:42:53.025+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:42:53.043+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:42:53.043+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:42:53.084+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.179 seconds
[2025-06-16T19:43:23.281+0000] {processor.py:157} INFO - Started process (PID=1552) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:43:23.284+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:43:23.287+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:43:23.286+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:43:23.368+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:43:23.431+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:43:23.430+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:43:23.456+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:43:23.456+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:43:23.497+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.224 seconds
[2025-06-16T19:43:54.313+0000] {processor.py:157} INFO - Started process (PID=1569) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:43:54.316+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:43:54.318+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:43:54.317+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:43:54.392+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:43:54.453+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:43:54.453+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:43:54.477+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:43:54.477+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:43:54.516+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.210 seconds
[2025-06-16T19:44:25.359+0000] {processor.py:157} INFO - Started process (PID=1592) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:44:25.361+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:44:25.364+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:44:25.363+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:44:25.445+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:44:25.500+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:44:25.499+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:44:25.523+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:44:25.523+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:44:25.564+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.212 seconds
[2025-06-16T19:44:56.052+0000] {processor.py:157} INFO - Started process (PID=1617) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:44:56.054+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:44:56.056+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:44:56.056+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:44:56.146+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:44:56.204+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:44:56.204+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:44:56.226+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:44:56.226+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:44:56.281+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.237 seconds
[2025-06-16T19:45:27.119+0000] {processor.py:157} INFO - Started process (PID=1640) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:45:27.122+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:45:27.125+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:45:27.124+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:45:27.224+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:45:27.274+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:45:27.273+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:45:27.294+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:45:27.294+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:45:27.347+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.236 seconds
[2025-06-16T19:45:57.537+0000] {processor.py:157} INFO - Started process (PID=1663) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:45:57.539+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:45:57.541+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:45:57.541+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:45:57.605+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:45:57.647+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:45:57.646+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:45:57.664+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:45:57.663+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:45:57.706+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.175 seconds
[2025-06-16T19:46:28.107+0000] {processor.py:157} INFO - Started process (PID=1686) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:46:28.111+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:46:28.114+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:46:28.113+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:46:28.189+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:46:28.240+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:46:28.239+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:46:28.258+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:46:28.257+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:46:28.309+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.209 seconds
[2025-06-16T19:46:58.461+0000] {processor.py:157} INFO - Started process (PID=1709) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:46:58.464+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:46:58.467+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:46:58.466+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:46:58.545+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:46:58.593+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:46:58.593+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:46:58.615+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:46:58.614+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:46:58.668+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.214 seconds
[2025-06-16T19:47:28.812+0000] {processor.py:157} INFO - Started process (PID=1732) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:47:28.814+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:47:28.816+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:47:28.816+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:47:28.897+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:47:28.945+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:47:28.944+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:47:28.966+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:47:28.965+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:47:29.030+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.224 seconds
[2025-06-16T19:47:59.260+0000] {processor.py:157} INFO - Started process (PID=1755) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:47:59.263+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:47:59.265+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:47:59.264+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:47:59.354+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:47:59.415+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:47:59.414+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:47:59.441+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:47:59.441+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:47:59.495+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.242 seconds
[2025-06-16T19:48:30.047+0000] {processor.py:157} INFO - Started process (PID=1778) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:48:30.051+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:48:30.055+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:48:30.054+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:48:30.176+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:48:30.239+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:48:30.239+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:48:30.261+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:48:30.261+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:48:30.300+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.263 seconds
[2025-06-16T19:49:00.476+0000] {processor.py:157} INFO - Started process (PID=1801) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:49:00.478+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:49:00.481+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:49:00.480+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:49:00.602+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:49:00.692+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:49:00.691+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:49:00.716+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:49:00.716+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:49:00.753+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.283 seconds
[2025-06-16T19:49:31.578+0000] {processor.py:157} INFO - Started process (PID=1816) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:49:31.581+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:49:31.584+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:49:31.583+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:49:31.664+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:49:31.718+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:49:31.717+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:49:31.741+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:49:31.741+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:49:31.784+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.215 seconds
[2025-06-16T19:50:02.249+0000] {processor.py:157} INFO - Started process (PID=1840) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:50:02.253+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:50:02.256+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:50:02.255+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:50:02.336+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:50:02.389+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:50:02.388+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:50:02.411+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:50:02.411+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:50:02.445+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.206 seconds
[2025-06-16T19:50:33.102+0000] {processor.py:157} INFO - Started process (PID=1863) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:50:33.104+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:50:33.106+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:50:33.105+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:50:33.192+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:50:33.288+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:50:33.286+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:50:33.330+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:50:33.329+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:50:33.391+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.297 seconds
[2025-06-16T19:51:04.090+0000] {processor.py:157} INFO - Started process (PID=1886) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:51:04.092+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:51:04.096+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:51:04.095+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:51:04.193+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:51:04.238+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:51:04.237+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:51:04.257+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:51:04.257+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:51:04.304+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.220 seconds
[2025-06-16T19:51:35.280+0000] {processor.py:157} INFO - Started process (PID=1909) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:51:35.283+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:51:35.286+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:51:35.285+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:51:35.358+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:51:35.422+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:51:35.422+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:51:35.444+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:51:35.444+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:51:35.479+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.207 seconds
[2025-06-16T19:52:06.419+0000] {processor.py:157} INFO - Started process (PID=1932) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:52:06.422+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:52:06.425+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:52:06.424+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:52:06.516+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:52:06.597+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:52:06.596+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:52:06.626+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:52:06.625+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:52:06.670+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.260 seconds
[2025-06-16T19:52:36.932+0000] {processor.py:157} INFO - Started process (PID=1955) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:52:36.935+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:52:36.938+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:52:36.937+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:52:37.039+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:52:37.107+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:52:37.106+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:52:37.130+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:52:37.130+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:52:37.191+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.266 seconds
[2025-06-16T19:53:07.444+0000] {processor.py:157} INFO - Started process (PID=1978) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:53:07.446+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T19:53:07.449+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:53:07.448+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:53:07.522+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T19:53:07.582+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:53:07.581+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:53:07.604+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:53:07.603+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T19:53:07.636+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.200 seconds
[2025-06-16T20:02:35.206+0000] {processor.py:157} INFO - Started process (PID=2127) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:02:35.207+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:02:35.210+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:02:35.209+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:02:35.346+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:02:35.761+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:02:35.760+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:02:35.781+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:02:35.780+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:02:35.834+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.634 seconds
[2025-06-16T20:03:06.143+0000] {processor.py:157} INFO - Started process (PID=2150) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:03:06.146+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:03:06.148+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:03:06.148+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:03:06.227+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:03:06.279+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:03:06.278+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:03:06.299+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:03:06.298+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:03:06.336+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.202 seconds
[2025-06-16T20:03:37.101+0000] {processor.py:157} INFO - Started process (PID=2173) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:03:37.104+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:03:37.106+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:03:37.105+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:03:37.176+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:03:37.237+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:03:37.237+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:03:37.262+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:03:37.262+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:03:37.310+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.214 seconds
[2025-06-16T20:04:08.098+0000] {processor.py:157} INFO - Started process (PID=2196) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:04:08.100+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:04:08.102+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:04:08.101+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:04:08.188+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:04:08.267+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:04:08.266+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:04:08.293+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:04:08.293+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:04:08.336+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.243 seconds
[2025-06-16T20:04:39.139+0000] {processor.py:157} INFO - Started process (PID=2219) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:04:39.141+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:04:39.143+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:04:39.143+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:04:39.215+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:04:39.267+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:04:39.267+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:04:39.289+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:04:39.288+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:04:39.322+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.189 seconds
[2025-06-16T20:05:10.072+0000] {processor.py:157} INFO - Started process (PID=2242) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:05:10.074+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:05:10.076+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:05:10.076+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:05:10.140+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:05:10.195+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:05:10.194+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:05:10.226+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:05:10.225+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:05:10.265+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.199 seconds
[2025-06-16T20:05:42.118+0000] {processor.py:157} INFO - Started process (PID=2265) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:05:42.122+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:05:42.127+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:05:42.126+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:05:42.255+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:05:42.321+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:05:42.320+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:05:42.350+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:05:42.350+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:05:42.397+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.286 seconds
[2025-06-16T20:06:13.166+0000] {processor.py:157} INFO - Started process (PID=2280) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:06:13.168+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:06:13.171+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:06:13.170+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:06:13.255+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:06:13.324+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:06:13.323+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:06:13.346+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:06:13.346+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:06:13.391+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.231 seconds
[2025-06-16T20:06:44.209+0000] {processor.py:157} INFO - Started process (PID=2303) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:06:44.212+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:06:44.214+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:06:44.214+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:06:44.287+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:06:44.357+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:06:44.356+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:06:44.378+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:06:44.378+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:06:44.412+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.210 seconds
[2025-06-16T20:07:14.608+0000] {processor.py:157} INFO - Started process (PID=2326) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:07:14.612+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:07:14.615+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:07:14.614+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:07:14.710+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:07:14.793+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:07:14.793+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:07:14.832+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:07:14.832+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:07:14.875+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.276 seconds
[2025-06-16T20:07:45.343+0000] {processor.py:157} INFO - Started process (PID=2350) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:07:45.345+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:07:45.346+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:07:45.346+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:07:45.416+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:07:45.476+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:07:45.475+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:07:45.498+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:07:45.497+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:07:45.550+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.212 seconds
[2025-06-16T20:08:15.967+0000] {processor.py:157} INFO - Started process (PID=2373) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:08:15.969+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:08:15.972+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:08:15.971+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:08:16.073+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:08:16.129+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:08:16.129+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:08:16.155+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:08:16.154+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:08:16.202+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.242 seconds
[2025-06-16T20:08:46.342+0000] {processor.py:157} INFO - Started process (PID=2396) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:08:46.345+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:08:46.347+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:08:46.346+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:08:46.431+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:08:46.482+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:08:46.481+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:08:46.504+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:08:46.504+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:08:46.554+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.219 seconds
[2025-06-16T20:09:17.105+0000] {processor.py:157} INFO - Started process (PID=2419) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:09:17.108+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:09:17.110+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:09:17.110+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:09:17.199+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:09:17.271+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:09:17.270+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:09:17.295+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:09:17.294+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:09:17.330+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.230 seconds
[2025-06-16T20:09:47.503+0000] {processor.py:157} INFO - Started process (PID=2442) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:09:47.506+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:09:47.509+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:09:47.508+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:09:47.590+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:09:47.635+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:09:47.634+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:09:47.654+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:09:47.654+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:09:47.705+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.209 seconds
[2025-06-16T20:10:17.885+0000] {processor.py:157} INFO - Started process (PID=2466) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:10:17.888+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:10:17.892+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:10:17.891+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:10:17.974+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:10:18.034+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:10:18.034+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:10:18.057+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:10:18.056+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:10:18.101+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.229 seconds
[2025-06-16T20:10:48.208+0000] {processor.py:157} INFO - Started process (PID=2489) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:10:48.210+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:10:48.212+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:10:48.212+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:10:48.290+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:10:48.341+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:10:48.341+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:10:48.363+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:10:48.363+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:10:48.419+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.217 seconds
[2025-06-16T20:11:18.611+0000] {processor.py:157} INFO - Started process (PID=2510) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:11:18.614+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:11:18.616+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:11:18.616+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:11:18.692+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:11:18.755+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:11:18.754+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:11:18.788+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:11:18.787+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:11:18.833+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.229 seconds
[2025-06-16T20:11:49.234+0000] {processor.py:157} INFO - Started process (PID=2533) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:11:49.237+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:11:49.239+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:11:49.239+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:11:49.318+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:11:49.378+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:11:49.377+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:11:49.400+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:11:49.400+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:11:49.437+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.212 seconds
[2025-06-16T20:12:20.244+0000] {processor.py:157} INFO - Started process (PID=2556) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:12:20.246+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:12:20.250+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:12:20.248+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:12:20.319+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:12:20.361+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:12:20.360+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:12:20.378+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:12:20.378+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:12:20.412+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.174 seconds
[2025-06-16T20:12:51.314+0000] {processor.py:157} INFO - Started process (PID=2579) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:12:51.317+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:12:51.319+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:12:51.318+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:12:51.398+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:12:51.468+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:12:51.468+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:12:51.496+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:12:51.496+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:12:51.544+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.238 seconds
[2025-06-16T20:13:22.371+0000] {processor.py:157} INFO - Started process (PID=2594) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:13:22.373+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:13:22.376+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:13:22.375+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:13:22.462+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:13:22.518+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:13:22.518+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:13:22.540+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:13:22.540+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:13:22.581+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.219 seconds
[2025-06-16T20:13:53.358+0000] {processor.py:157} INFO - Started process (PID=2617) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:13:53.362+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:13:53.365+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:13:53.364+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:13:53.450+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:13:53.503+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:13:53.503+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:13:53.524+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:13:53.524+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:13:53.563+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.213 seconds
[2025-06-16T20:14:24.375+0000] {processor.py:157} INFO - Started process (PID=2640) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:14:24.380+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:14:24.383+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:14:24.382+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:14:24.459+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:14:24.512+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:14:24.511+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:14:24.537+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:14:24.536+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:14:24.581+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.217 seconds
[2025-06-16T20:14:55.074+0000] {processor.py:157} INFO - Started process (PID=2666) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:14:55.076+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:14:55.079+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:14:55.078+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:14:55.167+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:14:55.224+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:14:55.223+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:14:55.250+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:14:55.250+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:14:55.293+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.227 seconds
[2025-06-16T20:15:25.476+0000] {processor.py:157} INFO - Started process (PID=2689) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:15:25.479+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:15:25.481+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:15:25.481+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:15:25.565+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:15:25.633+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:15:25.632+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:15:25.656+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:15:25.656+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:15:25.716+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.247 seconds
[2025-06-16T20:15:55.860+0000] {processor.py:157} INFO - Started process (PID=2712) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:15:55.863+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:15:55.865+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:15:55.864+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:15:55.943+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:15:55.997+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:15:55.996+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:15:56.019+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:15:56.019+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:15:56.081+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.228 seconds
[2025-06-16T20:16:26.906+0000] {processor.py:157} INFO - Started process (PID=2733) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:16:26.909+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:16:26.911+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:16:26.910+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:16:26.988+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:16:27.042+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:16:27.041+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:16:27.061+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:16:27.061+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:16:27.100+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.200 seconds
[2025-06-16T20:16:57.587+0000] {processor.py:157} INFO - Started process (PID=2758) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:16:57.590+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:16:57.592+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:16:57.592+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:16:57.665+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:16:57.711+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:16:57.710+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:16:57.729+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:16:57.729+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:16:57.763+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.184 seconds
[2025-06-16T20:17:27.948+0000] {processor.py:157} INFO - Started process (PID=2781) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:17:27.951+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:17:27.953+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:17:27.953+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:17:28.037+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:17:28.101+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:17:28.100+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:17:28.122+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:17:28.121+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:17:28.184+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.244 seconds
[2025-06-16T20:17:58.345+0000] {processor.py:157} INFO - Started process (PID=2804) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:17:58.348+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:17:58.350+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:17:58.349+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:17:58.415+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:17:58.470+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:17:58.469+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:17:58.493+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:17:58.492+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:17:58.554+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.216 seconds
[2025-06-16T20:18:29.206+0000] {processor.py:157} INFO - Started process (PID=2825) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:18:29.213+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:18:29.219+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:18:29.219+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:18:29.484+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:18:29.568+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:18:29.567+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:18:29.610+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:18:29.609+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:18:29.701+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.503 seconds
[2025-06-16T20:18:59.959+0000] {processor.py:157} INFO - Started process (PID=2850) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:18:59.963+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:18:59.966+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:18:59.965+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:19:00.053+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:19:00.104+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:19:00.104+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:19:00.126+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:19:00.126+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:19:00.161+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.209 seconds
[2025-06-16T20:19:30.349+0000] {processor.py:157} INFO - Started process (PID=2865) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:19:30.352+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:19:30.355+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:19:30.354+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:19:30.446+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:19:30.517+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:19:30.517+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:19:30.540+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:19:30.539+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:19:30.574+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.232 seconds
[2025-06-16T20:20:01.080+0000] {processor.py:157} INFO - Started process (PID=2886) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:20:01.083+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:20:01.086+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:20:01.085+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:20:01.170+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:20:01.229+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:20:01.228+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:20:01.250+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:20:01.250+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:20:01.286+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.216 seconds
[2025-06-16T20:20:31.747+0000] {processor.py:157} INFO - Started process (PID=2909) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:20:31.750+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:20:31.752+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:20:31.752+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:20:31.825+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:20:31.890+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:20:31.889+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:20:31.918+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:20:31.917+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:20:31.973+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.234 seconds
[2025-06-16T20:21:02.619+0000] {processor.py:157} INFO - Started process (PID=2932) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:21:02.622+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:21:02.624+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:21:02.624+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:21:02.704+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:21:02.760+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:21:02.760+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:21:02.783+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:21:02.783+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:21:02.823+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.209 seconds
[2025-06-16T20:21:33.575+0000] {processor.py:157} INFO - Started process (PID=2955) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:21:33.577+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:21:33.580+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:21:33.579+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:21:33.657+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:21:33.711+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:21:33.711+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:21:33.732+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:21:33.731+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:21:33.766+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.198 seconds
[2025-06-16T20:22:04.167+0000] {processor.py:157} INFO - Started process (PID=2978) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:22:04.170+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:22:04.175+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:22:04.174+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:22:04.244+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:22:04.296+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:22:04.295+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:22:04.316+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:22:04.315+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:22:04.365+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.203 seconds
[2025-06-16T20:22:35.144+0000] {processor.py:157} INFO - Started process (PID=3001) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:22:35.147+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:22:35.149+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:22:35.148+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:22:35.214+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:22:35.259+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:22:35.259+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:22:35.279+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:22:35.278+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:22:35.313+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.177 seconds
[2025-06-16T20:23:06.350+0000] {processor.py:157} INFO - Started process (PID=3024) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:23:06.353+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:23:06.356+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:23:06.355+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:23:06.434+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:23:06.496+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:23:06.496+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:23:06.516+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:23:06.516+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:23:06.557+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.214 seconds
[2025-06-16T20:23:37.339+0000] {processor.py:157} INFO - Started process (PID=3047) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:23:37.341+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:23:37.343+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:23:37.342+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:23:37.408+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:23:37.458+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:23:37.457+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:23:37.481+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:23:37.480+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:23:37.735+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.403 seconds
[2025-06-16T20:24:08.541+0000] {processor.py:157} INFO - Started process (PID=3070) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:24:08.544+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:24:08.547+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:24:08.546+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:24:08.613+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:24:08.670+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:24:08.669+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:24:08.692+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:24:08.691+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:24:08.728+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.197 seconds
[2025-06-16T20:24:39.053+0000] {processor.py:157} INFO - Started process (PID=3093) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:24:39.056+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:24:39.058+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:24:39.057+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:24:39.142+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:24:39.201+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:24:39.200+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:24:39.390+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:24:39.389+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:24:39.430+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.389 seconds
[2025-06-16T20:25:09.743+0000] {processor.py:157} INFO - Started process (PID=3116) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:25:09.746+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:25:09.748+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:25:09.747+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:25:09.818+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:25:09.867+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:25:09.867+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:25:09.888+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:25:09.888+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:25:09.926+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.189 seconds
[2025-06-16T20:25:40.655+0000] {processor.py:157} INFO - Started process (PID=3139) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:25:40.658+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:25:40.661+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:25:40.660+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:25:40.726+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:25:40.981+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:25:40.980+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:25:40.998+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:25:40.997+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:25:41.035+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.388 seconds
[2025-06-16T20:26:11.203+0000] {processor.py:157} INFO - Started process (PID=3154) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:26:11.206+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:26:11.210+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:26:11.209+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:26:11.295+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:26:11.351+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:26:11.350+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:26:11.375+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:26:11.375+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:26:11.417+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.222 seconds
[2025-06-16T20:26:41.739+0000] {processor.py:157} INFO - Started process (PID=3177) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:26:41.741+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:26:41.744+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:26:41.743+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:26:41.805+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:26:41.857+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:26:41.856+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:26:41.880+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:26:41.879+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:26:41.916+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.184 seconds
[2025-06-16T20:27:13.061+0000] {processor.py:157} INFO - Started process (PID=3200) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:27:13.065+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:27:13.068+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:27:13.067+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:27:13.189+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:27:13.274+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:27:13.273+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:27:13.327+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:27:13.327+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:27:13.443+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.388 seconds
[2025-06-16T20:27:43.964+0000] {processor.py:157} INFO - Started process (PID=3223) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:27:43.966+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:27:43.973+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:27:43.969+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:27:44.042+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:27:44.096+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:27:44.095+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:27:44.118+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:27:44.117+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:27:44.153+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.198 seconds
[2025-06-16T20:28:14.837+0000] {processor.py:157} INFO - Started process (PID=3246) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:28:14.839+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:28:14.842+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:28:14.841+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:28:14.895+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:28:14.944+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:28:14.944+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:28:14.967+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:28:14.967+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:28:15.007+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.179 seconds
[2025-06-16T20:28:45.461+0000] {processor.py:157} INFO - Started process (PID=3269) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:28:45.464+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:28:45.467+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:28:45.466+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:28:45.619+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:28:45.732+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:28:45.732+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:28:45.805+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:28:45.805+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:28:45.857+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.403 seconds
[2025-06-16T20:29:16.131+0000] {processor.py:157} INFO - Started process (PID=3292) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:29:16.134+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:29:16.137+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:29:16.137+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:29:16.194+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:29:16.255+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:29:16.254+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:29:16.280+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:29:16.279+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:29:16.332+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.209 seconds
[2025-06-16T20:29:47.124+0000] {processor.py:157} INFO - Started process (PID=3315) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:29:47.126+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:29:47.129+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:29:47.128+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:29:47.183+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:29:47.235+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:29:47.235+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:29:47.257+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:29:47.257+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:29:47.295+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.179 seconds
[2025-06-16T20:30:17.486+0000] {processor.py:157} INFO - Started process (PID=3338) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:30:17.489+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:30:17.493+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:30:17.492+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:30:17.909+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:30:17.980+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:30:17.979+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:30:18.002+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:30:18.001+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:30:18.050+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.577 seconds
[2025-06-16T20:30:48.347+0000] {processor.py:157} INFO - Started process (PID=3361) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:30:48.350+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:30:48.351+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:30:48.351+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:30:48.405+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:30:48.467+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:30:48.466+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:30:48.498+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:30:48.497+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:30:48.539+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.198 seconds
[2025-06-16T20:31:19.318+0000] {processor.py:157} INFO - Started process (PID=3384) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:31:19.320+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:31:19.321+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:31:19.321+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:31:19.372+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:31:19.423+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:31:19.423+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:31:19.444+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:31:19.444+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:31:19.475+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.163 seconds
[2025-06-16T20:31:49.714+0000] {processor.py:157} INFO - Started process (PID=3407) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:31:49.719+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:31:49.721+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:31:49.720+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:31:49.860+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:31:49.904+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:31:49.903+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:31:49.923+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:31:49.923+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:31:49.957+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.251 seconds
[2025-06-16T20:32:20.461+0000] {processor.py:157} INFO - Started process (PID=3431) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:32:20.463+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:32:20.464+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:32:20.464+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:32:20.541+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:32:20.619+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:32:20.618+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:32:20.648+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:32:20.648+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:32:20.698+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.243 seconds
[2025-06-16T20:32:50.795+0000] {processor.py:157} INFO - Started process (PID=3447) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:32:50.798+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:32:50.805+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:32:50.804+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:32:50.873+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:32:50.924+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:32:50.923+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:32:50.945+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:32:50.944+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:32:50.989+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.201 seconds
[2025-06-16T20:33:24.093+0000] {processor.py:157} INFO - Started process (PID=3468) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:33:24.117+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:33:24.147+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:33:24.146+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:33:25.617+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:33:25.894+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:33:25.893+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:33:26.016+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:33:26.016+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:33:26.304+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 2.233 seconds
[2025-06-16T20:33:57.106+0000] {processor.py:157} INFO - Started process (PID=3493) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:33:57.112+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:33:57.114+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:33:57.114+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:33:57.183+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:33:57.254+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:33:57.253+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:33:57.279+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:33:57.279+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:33:57.319+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.231 seconds
[2025-06-16T20:34:28.299+0000] {processor.py:157} INFO - Started process (PID=3516) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:34:28.309+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:34:28.316+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:34:28.315+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:34:28.436+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:34:28.510+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:34:28.510+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:34:28.536+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:34:28.535+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:34:28.578+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.286 seconds
[2025-06-16T20:34:58.992+0000] {processor.py:157} INFO - Started process (PID=3539) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:34:58.998+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:34:59.002+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:34:59.001+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:34:59.214+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:34:59.281+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:34:59.280+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:34:59.306+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:34:59.306+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:34:59.361+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.374 seconds
[2025-06-16T20:35:31.167+0000] {processor.py:157} INFO - Started process (PID=3562) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:35:31.173+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:35:31.179+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:35:31.178+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:35:31.286+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:35:31.350+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:35:31.350+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:35:31.382+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:35:31.381+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:35:31.426+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.265 seconds
[2025-06-16T20:36:01.651+0000] {processor.py:157} INFO - Started process (PID=3585) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:36:01.656+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:36:01.659+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:36:01.658+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:36:01.745+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:36:01.813+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:36:01.812+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:36:01.835+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:36:01.835+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:36:01.894+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.252 seconds
[2025-06-16T20:36:32.905+0000] {processor.py:157} INFO - Started process (PID=3608) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:36:32.907+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:36:32.909+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:36:32.909+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:36:32.983+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:36:33.051+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:36:33.050+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:36:33.074+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:36:33.073+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:36:33.112+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.211 seconds
[2025-06-16T20:37:03.303+0000] {processor.py:157} INFO - Started process (PID=3631) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:37:03.307+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:37:03.312+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:37:03.311+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:37:03.423+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:37:03.486+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:37:03.486+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:37:03.511+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:37:03.511+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:37:03.556+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.264 seconds
[2025-06-16T20:37:33.678+0000] {processor.py:157} INFO - Started process (PID=3655) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:37:33.682+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:37:33.684+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:37:33.684+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:37:33.756+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:37:33.808+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:37:33.807+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:37:33.833+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:37:33.832+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:37:33.892+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.221 seconds
[2025-06-16T20:38:04.223+0000] {processor.py:157} INFO - Started process (PID=3678) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:38:04.226+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:38:04.232+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:38:04.231+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:38:04.387+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:38:04.450+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:38:04.449+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:38:04.480+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:38:04.480+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:38:04.537+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.322 seconds
[2025-06-16T20:38:34.920+0000] {processor.py:157} INFO - Started process (PID=3701) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:38:34.924+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:38:34.927+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:38:34.927+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:38:34.988+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:38:35.037+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:38:35.037+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:38:35.057+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:38:35.057+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:38:35.115+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.206 seconds
[2025-06-16T20:39:06.021+0000] {processor.py:157} INFO - Started process (PID=3724) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:39:06.027+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:39:06.039+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:39:06.038+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:39:06.129+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:39:06.182+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:39:06.181+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:39:06.201+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:39:06.201+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:39:06.238+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.234 seconds
[2025-06-16T20:39:36.638+0000] {processor.py:157} INFO - Started process (PID=3739) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:39:36.642+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:39:36.645+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:39:36.645+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:39:36.728+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:39:36.822+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:39:36.821+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:39:36.846+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:39:36.845+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:39:36.895+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.266 seconds
[2025-06-16T20:40:07.539+0000] {processor.py:157} INFO - Started process (PID=3762) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:40:07.542+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:40:07.545+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:40:07.544+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:40:07.635+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:40:07.695+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:40:07.693+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:40:07.722+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:40:07.722+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:40:07.778+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.251 seconds
[2025-06-16T20:40:38.271+0000] {processor.py:157} INFO - Started process (PID=3782) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:40:38.278+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:40:38.289+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:40:38.288+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:40:38.525+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:40:38.590+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:40:38.590+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:40:38.625+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:40:38.624+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:40:38.680+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.416 seconds
[2025-06-16T20:41:09.370+0000] {processor.py:157} INFO - Started process (PID=3805) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:41:09.371+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:41:09.471+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:41:09.470+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:41:09.660+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:41:09.811+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:41:09.810+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:41:09.915+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:41:09.915+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:41:10.307+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.946 seconds
[2025-06-16T20:41:40.558+0000] {processor.py:157} INFO - Started process (PID=3830) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:41:40.561+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:41:40.563+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:41:40.563+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:41:40.629+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:41:40.695+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:41:40.694+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:41:40.720+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:41:40.720+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:41:40.776+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.226 seconds
[2025-06-16T20:42:10.939+0000] {processor.py:157} INFO - Started process (PID=3852) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:42:10.941+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:42:10.943+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:42:10.943+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:42:11.009+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:42:11.058+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:42:11.057+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:42:11.092+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:42:11.092+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:42:11.140+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.207 seconds
[2025-06-16T20:42:41.982+0000] {processor.py:157} INFO - Started process (PID=3875) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:42:41.984+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:42:41.986+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:42:41.986+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:42:42.046+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:42:42.147+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:42:42.146+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:42:42.171+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:42:42.170+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:42:42.212+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.236 seconds
[2025-06-16T20:43:12.635+0000] {processor.py:157} INFO - Started process (PID=3900) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:43:12.637+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:43:12.639+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:43:12.638+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:43:12.690+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:43:12.746+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:43:12.745+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:43:12.775+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:43:12.775+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:43:12.829+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.201 seconds
[2025-06-16T20:43:43.095+0000] {processor.py:157} INFO - Started process (PID=3923) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:43:43.097+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:43:43.099+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:43:43.098+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:43:43.153+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:43:43.208+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:43:43.207+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:43:43.236+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:43:43.235+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:43:43.280+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.191 seconds
[2025-06-16T20:44:13.488+0000] {processor.py:157} INFO - Started process (PID=3946) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:44:13.490+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:44:13.493+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:44:13.492+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:44:13.561+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:44:13.624+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:44:13.623+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:44:13.658+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:44:13.658+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:44:13.704+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.227 seconds
[2025-06-16T20:44:44.059+0000] {processor.py:157} INFO - Started process (PID=3969) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:44:44.062+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:44:44.063+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:44:44.063+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:44:44.113+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:44:44.165+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:44:44.165+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:44:44.184+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:44:44.183+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:44:44.216+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.163 seconds
[2025-06-16T20:45:14.416+0000] {processor.py:157} INFO - Started process (PID=3992) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:45:14.418+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:45:14.419+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:45:14.419+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:45:14.467+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:45:14.516+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:45:14.515+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:45:14.536+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:45:14.535+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:45:14.581+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.172 seconds
[2025-06-16T20:45:44.741+0000] {processor.py:157} INFO - Started process (PID=4007) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:45:44.743+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:45:44.744+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:45:44.744+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:45:44.794+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:45:44.842+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:45:44.841+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:45:44.864+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:45:44.864+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:45:44.909+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.175 seconds
[2025-06-16T20:46:15.661+0000] {processor.py:157} INFO - Started process (PID=4028) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:46:15.663+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:46:15.665+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:46:15.665+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:46:15.721+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:46:15.772+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:46:15.771+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:46:15.794+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:46:15.794+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:46:15.825+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.170 seconds
[2025-06-16T20:46:46.507+0000] {processor.py:157} INFO - Started process (PID=4051) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:46:46.509+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:46:46.512+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:46:46.511+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:46:46.572+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:46:46.622+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:46:46.621+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:46:46.645+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:46:46.645+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:46:46.683+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.182 seconds
[2025-06-16T20:47:17.096+0000] {processor.py:157} INFO - Started process (PID=4076) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:47:17.098+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:47:17.100+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:47:17.100+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:47:17.155+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:47:17.211+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:47:17.210+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:47:17.238+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:47:17.238+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:47:17.300+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.210 seconds
[2025-06-16T20:47:47.631+0000] {processor.py:157} INFO - Started process (PID=4099) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:47:47.633+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:47:47.635+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:47:47.634+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:47:47.687+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:47:47.745+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:47:47.744+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:47:47.775+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:47:47.775+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:47:47.808+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.182 seconds
[2025-06-16T20:48:19.751+0000] {processor.py:157} INFO - Started process (PID=4120) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:48:19.792+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:48:19.881+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:48:19.881+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:48:23.026+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:48:23.283+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:48:23.282+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:48:23.346+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:48:23.345+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:48:23.470+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 3.746 seconds
[2025-06-16T20:48:54.223+0000] {processor.py:157} INFO - Started process (PID=4145) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:48:54.225+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:48:54.229+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:48:54.229+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:48:54.276+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:48:54.320+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:48:54.320+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:48:54.340+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:48:54.340+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:48:54.389+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.171 seconds
[2025-06-16T20:49:25.266+0000] {processor.py:157} INFO - Started process (PID=4168) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:49:25.269+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:49:25.273+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:49:25.272+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:49:25.354+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:49:25.407+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:49:25.406+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:49:25.431+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:49:25.430+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:49:25.478+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.219 seconds
[2025-06-16T20:49:56.254+0000] {processor.py:157} INFO - Started process (PID=4191) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:49:56.257+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:49:56.260+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:49:56.259+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:49:56.326+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:49:56.383+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:49:56.382+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:49:56.407+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:49:56.407+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:49:56.454+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.206 seconds
[2025-06-16T20:50:26.781+0000] {processor.py:157} INFO - Started process (PID=4215) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:50:26.784+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:50:26.785+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:50:26.785+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:50:26.829+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:50:26.879+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:50:26.879+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:50:26.903+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:50:26.903+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:50:26.951+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.176 seconds
[2025-06-16T20:50:57.143+0000] {processor.py:157} INFO - Started process (PID=4237) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:50:57.145+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:50:57.146+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:50:57.146+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:50:57.205+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:50:57.249+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:50:57.248+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:50:57.274+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:50:57.274+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:50:57.338+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.200 seconds
[2025-06-16T20:51:28.125+0000] {processor.py:157} INFO - Started process (PID=4260) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:51:28.128+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:51:28.130+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:51:28.129+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:51:28.176+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:51:28.226+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:51:28.226+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:51:28.246+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:51:28.246+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:51:28.278+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.159 seconds
[2025-06-16T20:51:58.934+0000] {processor.py:157} INFO - Started process (PID=4283) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:51:58.938+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:51:58.940+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:51:58.940+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:51:59.013+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:51:59.074+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:51:59.074+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:51:59.106+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:51:59.105+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:51:59.168+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.242 seconds
[2025-06-16T20:52:29.417+0000] {processor.py:157} INFO - Started process (PID=4306) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:52:29.419+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:52:29.421+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:52:29.421+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:52:29.496+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:52:29.553+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:52:29.552+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:52:29.582+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:52:29.582+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:52:29.629+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.218 seconds
[2025-06-16T20:53:00.643+0000] {processor.py:157} INFO - Started process (PID=4329) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:53:00.645+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:53:00.647+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:53:00.646+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:53:00.759+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:53:00.823+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:53:00.822+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:53:00.848+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:53:00.848+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:53:00.885+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.247 seconds
[2025-06-16T20:53:31.668+0000] {processor.py:157} INFO - Started process (PID=4344) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:53:31.671+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:53:31.674+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:53:31.673+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:53:31.721+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:53:31.774+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:53:31.773+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:53:31.798+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:53:31.797+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:53:31.837+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.177 seconds
[2025-06-16T20:54:02.576+0000] {processor.py:157} INFO - Started process (PID=4367) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:54:02.578+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:54:02.583+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:54:02.579+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:54:02.641+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:54:02.694+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:54:02.694+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:54:02.717+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:54:02.717+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:54:02.754+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.184 seconds
[2025-06-16T20:54:33.578+0000] {processor.py:157} INFO - Started process (PID=4390) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:54:33.580+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:54:33.582+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:54:33.581+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:54:33.644+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:54:33.696+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:54:33.696+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:54:33.725+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:54:33.725+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:54:33.763+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.189 seconds
[2025-06-16T20:55:04.460+0000] {processor.py:157} INFO - Started process (PID=4413) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:55:04.465+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:55:04.468+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:55:04.467+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:55:04.577+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:55:04.647+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:55:04.646+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:55:04.672+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:55:04.672+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:55:04.717+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.265 seconds
[2025-06-16T20:55:35.389+0000] {processor.py:157} INFO - Started process (PID=4436) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:55:35.394+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:55:35.399+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:55:35.397+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:55:35.511+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:55:35.573+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:55:35.572+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:55:35.600+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:55:35.600+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:55:35.642+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.265 seconds
[2025-06-16T20:56:06.492+0000] {processor.py:157} INFO - Started process (PID=4459) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:56:06.497+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:56:06.500+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:56:06.499+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:56:06.602+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:56:06.668+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:56:06.668+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:56:06.689+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:56:06.689+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:56:06.725+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.240 seconds
[2025-06-16T20:56:37.223+0000] {processor.py:157} INFO - Started process (PID=4482) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:56:37.225+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:56:37.228+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:56:37.227+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:56:37.295+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:56:37.357+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:56:37.356+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:56:37.381+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:56:37.380+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:56:37.415+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.222 seconds
[2025-06-16T20:57:08.165+0000] {processor.py:157} INFO - Started process (PID=4505) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:57:08.167+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:57:08.169+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:57:08.168+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:57:08.226+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:57:08.275+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:57:08.274+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:57:08.295+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:57:08.295+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:57:08.326+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.166 seconds
[2025-06-16T20:57:38.773+0000] {processor.py:157} INFO - Started process (PID=4528) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:57:38.776+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:57:38.778+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:57:38.777+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:57:38.833+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:57:38.889+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:57:38.889+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:57:38.910+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:57:38.910+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:57:38.947+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.181 seconds
[2025-06-16T20:58:09.807+0000] {processor.py:157} INFO - Started process (PID=4551) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:58:09.809+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:58:09.811+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:58:09.811+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:58:09.896+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:58:09.980+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:58:09.979+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:58:10.021+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:58:10.019+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:58:10.067+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.265 seconds
[2025-06-16T20:58:40.598+0000] {processor.py:157} INFO - Started process (PID=4574) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:58:40.601+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:58:40.603+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:58:40.603+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:58:40.674+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:58:40.731+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:58:40.730+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:58:40.752+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:58:40.752+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:58:40.789+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.199 seconds
[2025-06-16T20:59:10.962+0000] {processor.py:157} INFO - Started process (PID=4597) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:59:10.965+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:59:10.967+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:59:10.966+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:59:11.022+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:59:11.098+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:59:11.097+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:59:11.124+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:59:11.124+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:59:11.163+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.206 seconds
[2025-06-16T20:59:42.077+0000] {processor.py:157} INFO - Started process (PID=4620) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:59:42.080+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T20:59:42.082+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:59:42.081+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:59:42.140+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T20:59:42.194+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:59:42.194+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:59:42.221+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:59:42.221+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T20:59:42.258+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.193 seconds
[2025-06-16T21:00:13.113+0000] {processor.py:157} INFO - Started process (PID=4643) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:00:13.116+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:00:13.119+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:00:13.118+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:00:13.178+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:00:13.240+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:00:13.239+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:00:13.280+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:00:13.280+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:00:13.327+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.222 seconds
[2025-06-16T21:00:44.010+0000] {processor.py:157} INFO - Started process (PID=4658) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:00:44.012+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:00:44.020+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:00:44.014+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:00:44.075+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:00:44.121+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:00:44.121+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:00:44.146+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:00:44.145+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:00:44.194+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.189 seconds
[2025-06-16T21:01:14.999+0000] {processor.py:157} INFO - Started process (PID=4681) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:01:15.001+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:01:15.003+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:01:15.002+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:01:15.056+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:01:15.109+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:01:15.108+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:01:15.135+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:01:15.135+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:01:15.169+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.176 seconds
[2025-06-16T21:01:45.314+0000] {processor.py:157} INFO - Started process (PID=4704) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:01:45.318+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:01:45.321+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:01:45.320+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:01:45.388+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:01:45.444+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:01:45.444+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:01:45.469+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:01:45.469+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:01:45.507+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.199 seconds
[2025-06-16T21:02:15.860+0000] {processor.py:157} INFO - Started process (PID=4727) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:02:15.862+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:02:15.863+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:02:15.863+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:02:15.919+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:02:15.990+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:02:15.989+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:02:16.016+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:02:16.015+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:02:16.052+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.198 seconds
[2025-06-16T21:02:46.535+0000] {processor.py:157} INFO - Started process (PID=4750) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:02:46.537+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:02:46.539+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:02:46.538+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:02:46.594+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:02:46.654+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:02:46.653+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:02:46.678+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:02:46.678+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:02:46.716+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.188 seconds
[2025-06-16T21:03:19.513+0000] {processor.py:157} INFO - Started process (PID=4773) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:03:19.545+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:03:19.553+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:03:19.551+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:03:21.600+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:03:21.784+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:03:21.783+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:03:21.909+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:03:21.908+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:03:22.203+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 2.704 seconds
[2025-06-16T21:03:52.647+0000] {processor.py:157} INFO - Started process (PID=4796) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:03:52.652+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:03:52.656+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:03:52.655+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:03:52.819+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:03:52.901+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:03:52.900+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:03:52.943+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:03:52.943+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:03:52.994+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.354 seconds
[2025-06-16T21:04:23.373+0000] {processor.py:157} INFO - Started process (PID=4819) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:04:23.375+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:04:23.378+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:04:23.377+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:04:23.508+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:04:23.593+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:04:23.592+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:04:23.621+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:04:23.620+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:04:23.674+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.308 seconds
[2025-06-16T21:04:53.858+0000] {processor.py:157} INFO - Started process (PID=4842) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:04:53.860+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:04:53.862+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:04:53.862+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:04:53.943+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:04:54.006+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:04:54.005+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:04:54.046+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:04:54.045+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:04:54.093+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.241 seconds
[2025-06-16T21:05:25.195+0000] {processor.py:157} INFO - Started process (PID=4866) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:05:25.197+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:05:25.198+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:05:25.198+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:05:25.253+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:05:25.311+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:05:25.310+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:05:25.338+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:05:25.338+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:05:25.377+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.188 seconds
[2025-06-16T21:05:55.683+0000] {processor.py:157} INFO - Started process (PID=4889) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:05:55.693+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:05:55.696+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:05:55.695+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:05:55.781+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:05:55.841+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:05:55.840+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:05:55.864+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:05:55.863+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:05:55.905+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.228 seconds
[2025-06-16T21:06:26.567+0000] {processor.py:157} INFO - Started process (PID=4912) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:06:26.570+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:06:26.572+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:06:26.571+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:06:26.630+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:06:26.692+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:06:26.692+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:06:26.717+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:06:26.717+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:06:26.757+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.197 seconds
[2025-06-16T21:06:57.456+0000] {processor.py:157} INFO - Started process (PID=4927) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:06:57.458+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:06:57.460+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:06:57.460+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:06:57.525+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:06:57.583+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:06:57.582+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:06:57.607+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:06:57.606+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:06:57.646+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.196 seconds
[2025-06-16T21:07:28.280+0000] {processor.py:157} INFO - Started process (PID=4950) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:07:28.283+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:07:28.285+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:07:28.285+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:07:28.349+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:07:28.399+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:07:28.398+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:07:28.418+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:07:28.418+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:07:28.454+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.180 seconds
[2025-06-16T21:07:59.272+0000] {processor.py:157} INFO - Started process (PID=4974) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:07:59.275+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:07:59.277+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:07:59.277+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:07:59.339+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:07:59.396+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:07:59.395+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:07:59.420+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:07:59.420+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:07:59.456+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.191 seconds
[2025-06-16T21:08:30.122+0000] {processor.py:157} INFO - Started process (PID=4997) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:08:30.125+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:08:30.128+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:08:30.127+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:08:30.202+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:08:30.326+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:08:30.325+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:08:30.360+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:08:30.360+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:08:30.402+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.286 seconds
[2025-06-16T21:09:00.538+0000] {processor.py:157} INFO - Started process (PID=5020) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:09:00.540+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:09:00.543+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:09:00.542+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:09:00.606+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:09:00.658+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:09:00.657+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:09:00.680+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:09:00.679+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:09:00.717+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.186 seconds
[2025-06-16T21:09:31.380+0000] {processor.py:157} INFO - Started process (PID=5044) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:09:31.382+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:09:31.385+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:09:31.384+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:09:31.451+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:09:31.496+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:09:31.495+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:09:31.519+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:09:31.519+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:09:31.554+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.182 seconds
[2025-06-16T21:10:02.222+0000] {processor.py:157} INFO - Started process (PID=5067) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:10:02.224+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:10:02.226+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:10:02.225+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:10:02.294+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:10:02.356+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:10:02.355+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:10:02.387+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:10:02.387+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:10:02.425+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.212 seconds
[2025-06-16T21:10:33.391+0000] {processor.py:157} INFO - Started process (PID=5090) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:10:33.393+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:10:33.395+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:10:33.395+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:10:33.466+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:10:33.533+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:10:33.533+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:10:33.566+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:10:33.565+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:10:33.611+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.238 seconds
[2025-06-16T21:11:04.441+0000] {processor.py:157} INFO - Started process (PID=5113) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:11:04.445+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:11:04.449+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:11:04.448+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:11:04.507+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:11:04.563+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:11:04.562+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:11:04.584+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:11:04.584+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:11:04.619+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.190 seconds
[2025-06-16T21:11:35.399+0000] {processor.py:157} INFO - Started process (PID=5136) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:11:35.401+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:11:35.408+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:11:35.404+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:11:35.472+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:11:35.520+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:11:35.519+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:11:35.549+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:11:35.549+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:11:35.580+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.186 seconds
[2025-06-16T21:12:06.478+0000] {processor.py:157} INFO - Started process (PID=5159) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:12:06.480+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:12:06.483+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:12:06.482+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:12:06.548+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:12:06.619+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:12:06.618+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:12:06.659+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:12:06.658+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:12:06.703+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.233 seconds
[2025-06-16T21:12:37.578+0000] {processor.py:157} INFO - Started process (PID=5182) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:12:37.580+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:12:37.582+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:12:37.581+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:12:37.627+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:12:37.677+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:12:37.677+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:12:37.697+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:12:37.696+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:12:37.730+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.157 seconds
[2025-06-16T21:13:07.924+0000] {processor.py:157} INFO - Started process (PID=5205) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:13:07.926+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:13:07.928+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:13:07.927+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:13:07.978+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:13:08.032+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:13:08.032+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:13:08.055+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:13:08.055+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:13:08.087+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.169 seconds
[2025-06-16T21:13:38.835+0000] {processor.py:157} INFO - Started process (PID=5228) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:13:38.837+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:13:38.840+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:13:38.839+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:13:38.904+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:13:38.971+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:13:38.970+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:13:38.993+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:13:38.993+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:13:39.028+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.207 seconds
[2025-06-16T21:14:09.894+0000] {processor.py:157} INFO - Started process (PID=5243) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:14:09.897+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:14:09.899+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:14:09.899+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:14:09.979+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:14:10.033+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:14:10.032+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:14:10.058+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:14:10.057+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:14:10.089+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.210 seconds
[2025-06-16T21:14:40.749+0000] {processor.py:157} INFO - Started process (PID=5266) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:14:40.752+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:14:40.754+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:14:40.754+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:14:40.809+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:14:40.879+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:14:40.878+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:14:40.901+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:14:40.900+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:14:40.945+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.202 seconds
[2025-06-16T21:15:11.619+0000] {processor.py:157} INFO - Started process (PID=5289) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:15:11.621+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:15:11.623+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:15:11.623+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:15:11.681+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:15:11.739+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:15:11.739+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:15:11.762+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:15:11.761+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:15:11.821+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.209 seconds
[2025-06-16T21:15:42.478+0000] {processor.py:157} INFO - Started process (PID=5312) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:15:42.480+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:15:42.482+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:15:42.481+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:15:42.538+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:15:42.591+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:15:42.590+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:15:42.616+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:15:42.615+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:15:42.649+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.178 seconds
[2025-06-16T21:16:13.273+0000] {processor.py:157} INFO - Started process (PID=5335) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:16:13.275+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:16:13.278+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:16:13.277+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:16:13.333+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:16:13.384+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:16:13.383+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:16:13.406+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:16:13.405+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:16:13.441+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.175 seconds
[2025-06-16T21:16:44.100+0000] {processor.py:157} INFO - Started process (PID=5358) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:16:44.102+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:16:44.105+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:16:44.104+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:16:44.163+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:16:44.218+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:16:44.216+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:16:44.241+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:16:44.240+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:16:44.275+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.182 seconds
[2025-06-16T21:17:14.880+0000] {processor.py:157} INFO - Started process (PID=5381) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:17:14.882+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:17:14.884+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:17:14.884+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:17:14.942+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:17:14.995+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:17:14.995+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:17:15.022+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:17:15.022+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:17:15.056+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.184 seconds
[2025-06-16T21:17:45.441+0000] {processor.py:157} INFO - Started process (PID=5406) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:17:45.444+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:17:45.446+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:17:45.445+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:17:45.507+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:17:45.557+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:17:45.556+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:17:45.581+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:17:45.581+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:17:45.643+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.209 seconds
[2025-06-16T21:18:16.597+0000] {processor.py:157} INFO - Started process (PID=5427) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:18:16.627+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:18:16.645+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:18:16.644+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:18:17.698+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:18:17.923+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:18:17.917+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:18:18.059+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:18:18.058+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:18:18.219+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 1.663 seconds
[2025-06-16T21:18:48.679+0000] {processor.py:157} INFO - Started process (PID=5452) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:18:48.681+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:18:48.684+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:18:48.683+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:18:48.755+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:18:48.802+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:18:48.801+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:18:48.820+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:18:48.820+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:18:48.859+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.187 seconds
[2025-06-16T21:19:19.030+0000] {processor.py:157} INFO - Started process (PID=5475) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:19:19.032+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:19:19.034+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:19:19.033+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:19:19.086+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:19:19.153+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:19:19.153+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:19:19.175+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:19:19.175+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:19:19.217+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.193 seconds
[2025-06-16T21:19:49.492+0000] {processor.py:157} INFO - Started process (PID=5498) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:19:49.494+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:19:49.496+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:19:49.496+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:19:49.571+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:19:49.629+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:19:49.628+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:19:49.662+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:19:49.662+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:19:49.725+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.240 seconds
[2025-06-16T21:20:20.465+0000] {processor.py:157} INFO - Started process (PID=5521) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:20:20.468+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:20:20.470+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:20:20.470+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:20:20.528+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:20:20.574+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:20:20.573+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:20:20.592+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:20:20.592+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:20:20.645+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.188 seconds
[2025-06-16T21:20:50.959+0000] {processor.py:157} INFO - Started process (PID=5543) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:20:50.961+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:20:50.963+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:20:50.963+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:20:51.024+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:20:51.080+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:20:51.080+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:20:51.109+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:20:51.109+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:20:51.151+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.198 seconds
[2025-06-16T21:21:21.394+0000] {processor.py:157} INFO - Started process (PID=5559) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:21:21.397+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:21:21.399+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:21:21.398+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:21:21.459+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:21:21.516+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:21:21.515+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:21:21.546+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:21:21.546+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:21:21.603+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.214 seconds
[2025-06-16T21:21:51.929+0000] {processor.py:157} INFO - Started process (PID=5582) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:21:51.931+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:21:51.933+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:21:51.932+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:21:51.985+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:21:52.043+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:21:52.043+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:21:52.091+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:21:52.091+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:21:52.146+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.223 seconds
[2025-06-16T21:22:22.356+0000] {processor.py:157} INFO - Started process (PID=5605) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:22:22.359+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:22:22.361+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:22:22.360+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:22:22.425+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:22:22.480+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:22:22.479+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:22:22.506+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:22:22.506+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:22:22.746+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.397 seconds
[2025-06-16T21:22:53.647+0000] {processor.py:157} INFO - Started process (PID=5628) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:22:53.650+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:22:53.652+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:22:53.651+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:22:53.707+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:22:53.756+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:22:53.755+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:22:53.789+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:22:53.788+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:22:53.827+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.187 seconds
[2025-06-16T21:23:24.010+0000] {processor.py:157} INFO - Started process (PID=5651) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:23:24.013+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:23:24.016+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:23:24.015+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:23:24.090+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:23:24.155+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:23:24.154+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:23:24.381+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:23:24.380+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:23:24.439+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.440 seconds
[2025-06-16T21:23:55.175+0000] {processor.py:157} INFO - Started process (PID=5674) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:23:55.178+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:23:55.180+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:23:55.180+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:23:55.252+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:23:55.304+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:23:55.303+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:23:55.330+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:23:55.330+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:23:55.382+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.217 seconds
[2025-06-16T21:24:25.555+0000] {processor.py:157} INFO - Started process (PID=5697) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:24:25.558+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:24:25.560+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:24:25.559+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:24:25.614+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:24:25.842+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:24:25.841+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:24:25.862+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:24:25.861+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:24:25.914+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.364 seconds
[2025-06-16T21:24:56.447+0000] {processor.py:157} INFO - Started process (PID=5720) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:24:56.448+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:24:56.450+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:24:56.449+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:24:56.507+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:24:56.550+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:24:56.550+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:24:56.575+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:24:56.574+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:24:56.628+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.186 seconds
[2025-06-16T21:25:26.829+0000] {processor.py:157} INFO - Started process (PID=5743) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:25:26.832+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:25:26.835+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:25:26.834+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:25:26.914+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:25:27.138+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:25:27.137+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:25:27.155+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:25:27.154+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:25:27.201+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.382 seconds
[2025-06-16T21:25:57.590+0000] {processor.py:157} INFO - Started process (PID=5766) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:25:57.592+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:25:57.594+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:25:57.594+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:25:57.656+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:25:57.718+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:25:57.718+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:25:57.741+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:25:57.741+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:25:57.783+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.199 seconds
[2025-06-16T21:26:28.531+0000] {processor.py:157} INFO - Started process (PID=5789) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:26:28.533+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:26:28.535+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:26:28.535+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:26:28.605+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:26:28.662+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:26:28.662+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:26:28.684+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:26:28.684+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:26:28.732+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.209 seconds
[2025-06-16T21:26:59.553+0000] {processor.py:157} INFO - Started process (PID=5812) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:26:59.555+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:26:59.557+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:26:59.556+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:26:59.619+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:26:59.678+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:26:59.677+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:26:59.705+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:26:59.704+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:26:59.747+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.200 seconds
[2025-06-16T21:27:29.864+0000] {processor.py:157} INFO - Started process (PID=5827) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:27:29.868+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:27:29.870+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:27:29.869+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:27:29.927+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:27:29.978+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:27:29.978+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:27:30.011+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:27:30.010+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:27:30.054+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.198 seconds
[2025-06-16T21:28:00.261+0000] {processor.py:157} INFO - Started process (PID=5850) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:28:00.264+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:28:00.266+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:28:00.266+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:28:00.351+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:28:00.413+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:28:00.412+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:28:00.436+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:28:00.435+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:28:00.488+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.236 seconds
[2025-06-16T21:28:31.416+0000] {processor.py:157} INFO - Started process (PID=5873) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:28:31.418+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:28:31.420+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:28:31.419+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:28:31.490+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:28:31.539+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:28:31.538+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:28:31.560+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:28:31.560+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:28:31.598+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.187 seconds
[2025-06-16T21:29:02.314+0000] {processor.py:157} INFO - Started process (PID=5896) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:29:02.317+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:29:02.321+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:29:02.320+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:29:02.398+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:29:02.451+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:29:02.450+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:29:02.474+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:29:02.474+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:29:02.524+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.217 seconds
[2025-06-16T21:29:33.336+0000] {processor.py:157} INFO - Started process (PID=5919) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:29:33.339+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:29:33.340+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:29:33.340+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:29:33.393+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:29:33.444+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:29:33.443+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:29:33.465+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:29:33.465+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:29:33.503+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.173 seconds
[2025-06-16T21:30:04.537+0000] {processor.py:157} INFO - Started process (PID=5942) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:30:04.544+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:30:04.547+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:30:04.547+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:30:04.914+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:30:04.983+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:30:04.983+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:30:05.029+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:30:05.029+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:30:05.071+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.539 seconds
[2025-06-16T21:30:35.967+0000] {processor.py:157} INFO - Started process (PID=5965) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:30:35.969+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:30:35.971+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:30:35.971+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:30:36.035+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:30:36.086+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:30:36.085+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:30:36.108+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:30:36.107+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:30:36.160+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.199 seconds
[2025-06-16T21:31:06.974+0000] {processor.py:157} INFO - Started process (PID=5988) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:31:06.978+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:31:06.981+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:31:06.981+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:31:07.035+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:31:07.079+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:31:07.078+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:31:07.104+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:31:07.104+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:31:07.150+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.183 seconds
[2025-06-16T21:31:37.855+0000] {processor.py:157} INFO - Started process (PID=6011) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:31:37.857+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:31:37.860+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:31:37.859+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:31:37.930+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:31:37.974+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:31:37.974+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:31:37.995+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:31:37.995+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:31:38.033+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.184 seconds
[2025-06-16T21:32:08.655+0000] {processor.py:157} INFO - Started process (PID=6034) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:32:08.657+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:32:08.659+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:32:08.658+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:32:08.711+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:32:08.759+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:32:08.758+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:32:08.777+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:32:08.777+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:32:08.824+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.175 seconds
[2025-06-16T21:32:38.986+0000] {processor.py:157} INFO - Started process (PID=6057) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:32:38.988+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:32:38.990+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:32:38.989+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:32:39.038+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:32:39.193+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:32:39.192+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:32:39.217+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:32:39.216+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:32:39.263+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.283 seconds
[2025-06-16T21:33:09.851+0000] {processor.py:157} INFO - Started process (PID=6080) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:33:09.871+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:33:09.881+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:33:09.880+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:33:10.016+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:33:10.123+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:33:10.123+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:33:10.145+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:33:10.144+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:33:10.315+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.474 seconds
[2025-06-16T21:33:40.711+0000] {processor.py:157} INFO - Started process (PID=6103) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:33:40.720+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-16T21:33:40.726+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:33:40.725+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:33:40.857+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-16T21:33:40.930+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:33:40.929+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:33:40.960+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:33:40.960+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-16T21:33:41.024+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.323 seconds
