#!/usr/bin/env python3
"""
Video Compiler Service
Uses FFmpeg to compile images and audio into YouTube Shorts-ready videos
"""

import os
import subprocess
import json
from typing import Dict, List, Any
from moviepy.editor import ImageClip, AudioFileClip, concatenate_videoclips

class VideoCompiler:
    def __init__(self):
        """Initialize video compiler with FFmpeg and MoviePy"""
        self.temp_dir = os.path.join(os.path.dirname(__file__), '..', 'temp')
        os.makedirs(self.temp_dir, exist_ok=True)

        # Check if FFmpeg is available
        self.ffmpeg_available = self._check_ffmpeg()

        print(f"✅ Video Compiler initialized (FFmpeg: {'✓' if self.ffmpeg_available else '✗'})")
    
    def _check_ffmpeg(self) -> bool:
        """Check if FFmpeg is available on the system"""
        try:
            subprocess.run(['ffmpeg', '-version'], 
                         capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("⚠️ FFmpeg not found - using MoviePy for video compilation")
            return False
    
    def compile_video(self, image_results: List[Dict], audio_result: Dict,
                     person_name: str) -> Dict[str, Any]:
        """
        Compile images and audio into a YouTube Shorts-ready video
        Following plan.md: Create 60-second vertical video (1080x1920)
        """
        try:
            # Generate filename for reference (no file will be stored)
            timestamp = int(os.path.getmtime(__file__) * 1000) if os.path.exists(__file__) else 1000
            video_filename = f"{person_name.replace(' ', '_').lower()}_bioshort_{timestamp}.mp4"

            print(f"🎥 Compiling video for {person_name}...")
            print(f"📸 Images: {len(image_results)} (in memory)")
            print(f"🎵 Audio: {audio_result.get('filename', 'unknown')} (in memory)")

            # Debug: Print detailed info about inputs
            print(f"🔍 Debug - Image results type: {type(image_results)}")
            print(f"🔍 Debug - Audio result type: {type(audio_result)}")
            print(f"🔍 Debug - Audio result keys: {list(audio_result.keys()) if isinstance(audio_result, dict) else 'Not a dict'}")

            # Skip simplified video compilation and use memory-based fallback directly
            print("🔄 Skipping simplified video compilation as per configuration")
            result = self._compile_with_moviepy_memory(
                image_results, audio_result
            )

            if result['success']:
                print(f"✅ Video compiled: {video_filename}")
                return {
                    'success': True,
                    'filename': video_filename,
                    'duration': result.get('duration', 'unknown'),
                    'resolution': '1080x1920',
                    'format': 'MP4',
                    'video_data': result.get('video_data'),
                    'video_base64': result.get('video_base64'),
                    'size': result.get('size', 0)
                }
            else:
                return result

        except Exception as e:
            import traceback
            print(f"❌ Video compilation error: {str(e)}")
            print(f"❌ Full traceback:")
            traceback.print_exc()
            return {
                'success': False,
                'error': str(e)
            }
    
    def _compile_with_moviepy(self, image_paths: List[str], audio_path: str, 
                             output_path: str, segments: List[Dict]) -> Dict[str, Any]:
        """Compile video using MoviePy"""
        try:
            # Load audio to get duration
            audio_clip = AudioFileClip(audio_path)
            total_duration = audio_clip.duration
            
            # Calculate duration per image
            duration_per_image = total_duration / len(image_paths)
            
            print(f"🎬 Creating video: {total_duration:.1f}s total, {duration_per_image:.1f}s per image")
            
            # Create video clips from images with subtitles
            video_clips = []
            for i, image_path in enumerate(image_paths):
                try:
                    # Create image clip with proper duration
                    img_clip = ImageClip(image_path, duration=duration_per_image)

                    # Resize to 1080x1920 (YouTube Shorts format)
                    img_clip = img_clip.resize((1080, 1920))

                    # Add subtitles if we have segment text
                    if i < len(segments) and 'text' in segments[i]:
                        subtitle_text = segments[i]['text']
                        img_clip = self._add_subtitles_to_clip(img_clip, subtitle_text, duration_per_image)

                    # Add fade transitions
                    if i > 0:  # Add fade in for all clips except first
                        img_clip = img_clip.fadein(0.3)
                    if i < len(image_paths) - 1:  # Add fade out for all clips except last
                        img_clip = img_clip.fadeout(0.3)

                    video_clips.append(img_clip)

                    # Keep images temporarily for frontend preview (will be cleaned up later)
                    print(f"� Keeping image temporarily for preview: {os.path.basename(image_path)}")

                except Exception as e:
                    print(f"⚠️ Error processing image {i+1}: {str(e)}")
                    # Create a black clip as fallback
                    black_clip = ImageClip(size=(1080, 1920), color=(0, 0, 0), duration=duration_per_image)

                    # Add subtitle to black clip too
                    if i < len(segments) and 'text' in segments[i]:
                        subtitle_text = segments[i]['text']
                        black_clip = self._add_subtitles_to_clip(black_clip, subtitle_text, duration_per_image)

                    video_clips.append(black_clip)

                    # Keep problematic image for debugging (will be cleaned up later)
                    print(f"📸 Keeping problematic image for debugging: {os.path.basename(image_path)}")

            # Concatenate all video clips
            if video_clips:
                final_video = concatenate_videoclips(video_clips, method="compose")
                
                # Set audio
                final_video = final_video.set_audio(audio_clip)
                
                # Ensure video duration matches audio duration
                final_video = final_video.set_duration(total_duration)
                
                # Write video file
                print("💾 Writing video file...")
                final_video.write_videofile(
                    output_path,
                    fps=24,
                    codec='libx264',
                    audio_codec='aac',
                    temp_audiofile='temp-audio.m4a',
                    remove_temp=True,
                    verbose=False,
                    logger=None
                )
                
                # Clean up
                final_video.close()
                audio_clip.close()
                for clip in video_clips:
                    clip.close()

                # Keep audio temporarily for frontend download (will be cleaned up later)
                print(f"🎵 Keeping audio temporarily for download: {os.path.basename(audio_path)}")

                return {
                    'success': True,
                    'duration': total_duration,
                    'method': 'moviepy'
                }
            else:
                return {
                    'success': False,
                    'error': 'No valid video clips created'
                }
                
        except Exception as e:
            print(f"❌ MoviePy compilation failed: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def _create_srt_file(self, segments: List[Dict], duration_per_image: float, language: str = 'english') -> str:
        """Create SRT subtitle file from segments"""
        try:
            import tempfile

            # Create temporary SRT file
            srt_fd, srt_path = tempfile.mkstemp(suffix='.srt')

            with open(srt_path, 'w', encoding='utf-8') as srt_file:
                for i, segment in enumerate(segments):
                    # Get the appropriate text based on language
                    if language == 'hindi' and 'hindi_text' in segment:
                        subtitle_text = segment['hindi_text']
                    elif 'text' in segment:
                        subtitle_text = segment['text']
                    else:
                        subtitle_text = segment.get('scene_description', f'Scene {i+1}')

                    # Calculate timing
                    start_time = i * duration_per_image
                    end_time = (i + 1) * duration_per_image

                    # Format time for SRT (HH:MM:SS,mmm)
                    start_srt = self._seconds_to_srt_time(start_time)
                    end_srt = self._seconds_to_srt_time(end_time)

                    # Write SRT entry
                    srt_file.write(f"{i + 1}\n")
                    srt_file.write(f"{start_srt} --> {end_srt}\n")
                    srt_file.write(f"{subtitle_text}\n\n")

            os.close(srt_fd)
            print(f"✅ Created SRT file: {srt_path}")

            # Debug: Print SRT file content
            try:
                with open(srt_path, 'r', encoding='utf-8') as f:
                    srt_content = f.read()
                    print(f"📝 SRT file content preview:")
                    print(f"📝 {srt_content[:500]}...")
            except Exception as e:
                print(f"⚠️ Could not read SRT file for debugging: {str(e)}")

            return srt_path

        except Exception as e:
            print(f"❌ Failed to create SRT file: {str(e)}")
            return None

    def _seconds_to_srt_time(self, seconds: float) -> str:
        """Convert seconds to SRT time format (HH:MM:SS,mmm)"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"

    def _compile_simple_video(self, image_results: List[Dict], audio_result: Dict, person_name: str) -> Dict[str, Any]:
        """Simplified video compilation using basic MoviePy operations"""
        try:
            print("🎬 Starting simplified video compilation...")
            print(f"📸 Processing {len(image_results)} images")
            print(f"🎵 Processing audio: {audio_result.get('filename', 'unknown')}")

            # Import MoviePy components
            from moviepy.editor import ImageClip, AudioFileClip, concatenate_videoclips
            import tempfile
            import base64

            # Get audio file path
            audio_filename = audio_result.get('filename')
            if not audio_filename:
                raise ValueError("No audio filename provided")

            audio_path = os.path.join(self.temp_dir, audio_filename)
            if not os.path.exists(audio_path):
                raise ValueError(f"Audio file not found: {audio_path}")

            print(f"🎵 Loading audio from: {audio_path}")
            audio_clip = AudioFileClip(audio_path)
            total_duration = audio_clip.duration
            print(f"🎵 Audio duration: {total_duration:.1f}s")

            # Calculate duration per image
            duration_per_image = total_duration / len(image_results)
            print(f"📸 Duration per image: {duration_per_image:.1f}s")

            # Create video clips from images (simplified - no effects)
            video_clips = []
            for i, image_result in enumerate(image_results):
                try:
                    print(f"📸 Processing image {i+1}/{len(image_results)}")

                    # Get image data
                    if 'image_base64' in image_result:
                        image_data = base64.b64decode(image_result['image_base64'])
                    else:
                        raise ValueError(f"No image data in result {i+1}")

                    # Create temporary image file
                    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_img:
                        temp_img.write(image_data)
                        temp_img_path = temp_img.name

                    try:
                        # Create simple image clip (no resizing, no effects)
                        img_clip = ImageClip(temp_img_path, duration=duration_per_image)
                        video_clips.append(img_clip)
                        print(f"✅ Image {i+1} processed successfully")
                    finally:
                        # Clean up temp file
                        os.unlink(temp_img_path)

                except Exception as img_e:
                    print(f"⚠️ Error with image {i+1}: {str(img_e)}")
                    # Create black clip as fallback
                    black_clip = ImageClip(size=(640, 480), color=(0, 0, 0), duration=duration_per_image)
                    video_clips.append(black_clip)

            if not video_clips:
                raise ValueError("No video clips created")

            print(f"🎬 Concatenating {len(video_clips)} video clips...")
            final_video = concatenate_videoclips(video_clips, method="compose")

            print("🎵 Adding audio to video...")
            final_video = final_video.set_audio(audio_clip)

            # Create temporary output file
            with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_video:
                temp_video_path = temp_video.name

            print("💾 Writing video file (simplified settings)...")
            final_video.write_videofile(
                temp_video_path,
                fps=15,  # Lower FPS for faster processing
                codec='libx264',
                audio_codec='aac',
                preset='ultrafast',  # Fastest encoding
                verbose=False,
                logger=None
            )

            # Read video into memory
            with open(temp_video_path, 'rb') as video_file:
                video_data = video_file.read()

            # Clean up
            os.unlink(temp_video_path)
            final_video.close()
            audio_clip.close()
            for clip in video_clips:
                clip.close()

            # Convert to base64
            video_base64 = base64.b64encode(video_data).decode('utf-8')

            print(f"✅ Simplified video compilation completed ({len(video_data)} bytes)")

            return {
                'success': True,
                'duration': total_duration,
                'method': 'simplified_moviepy',
                'video_data': video_data,
                'video_base64': video_base64,
                'size': len(video_data)
            }

        except Exception as e:
            print(f"❌ Simplified video compilation failed: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'error': str(e)
            }







    def _compile_with_moviepy_memory(self, image_results: List[Dict], audio_result: Dict) -> Dict[str, Any]:
        """Compile video using MoviePy with in-memory data (no disk storage)"""
        try:
            import tempfile

            # Use the audio file directly from temp directory
            temp_audio_path = None
            try:
                # Check if we have a filename to locate the audio file
                if 'filename' in audio_result:
                    audio_filename = audio_result['filename']
                    temp_audio_path = os.path.join(self.temp_dir, audio_filename)

                    if not os.path.exists(temp_audio_path):
                        print(f"⚠️ Audio file not found at {temp_audio_path}, creating from memory data")
                        # Fallback: create from memory data
                        if 'audio_base64' in audio_result:
                            import base64
                            audio_data = base64.b64decode(audio_result['audio_base64'])
                        elif 'audio_data' in audio_result:
                            audio_data = audio_result['audio_data']
                        else:
                            raise ValueError(f"No audio data found in audio_result keys: {list(audio_result.keys())}")

                        with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as temp_audio:
                            temp_audio.write(audio_data)
                            temp_audio_path = temp_audio.name
                    else:
                        print(f"✅ Using existing audio file: {temp_audio_path}")
                else:
                    raise ValueError(f"No filename found in audio_result keys: {list(audio_result.keys())}")

                # Load audio to get duration
                audio_clip = AudioFileClip(temp_audio_path)
                total_duration = audio_clip.duration

                # Calculate duration per image
                duration_per_image = total_duration / len(image_results)

                print(f"🎬 Creating video: {total_duration:.1f}s total, {duration_per_image:.1f}s per image")

                import time
                start_time = time.time()

                # Create video clips from in-memory images
                video_clips = []
                for i, image_result in enumerate(image_results):
                    try:
                        # Skip invalid or placeholder image results
                        if not isinstance(image_result, dict) or not image_result.get('success', True):
                            print(f"⚠️ Skipping invalid image result for scene {i+1}")
                            # Try to reuse a previous successful clip
                            if len(video_clips) > 0:
                                import random
                                source_clip = random.choice(video_clips)
                                fallback_clip = source_clip.copy().set_duration(duration_per_image)
                                video_clips.append(fallback_clip)
                                print(f"🔄 Used previous scene clip for invalid image {i+1}")
                                continue
                            else:
                                # Create black clip as absolute fallback
                                black_clip = ImageClip(size=(1080, 1920), color=(0, 0, 0), duration=duration_per_image)
                                video_clips.append(black_clip)
                                print(f"🖤 Used black clip for invalid image {i+1}")
                                continue

                        # Get image data from base64 or binary data
                        if 'image_base64' in image_result:
                            # Decode base64 image data
                            import base64
                            image_data = base64.b64decode(image_result['image_base64'])
                        elif 'image_data' in image_result:
                            # Use binary image data directly
                            image_data = image_result['image_data']
                        else:
                            raise ValueError(f"No image data found in image_result keys: {list(image_result.keys())}")

                        # Create temporary image file from memory
                        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_img:
                            temp_img.write(image_data)
                            temp_img_path = temp_img.name

                        try:
                            # Create image clip with proper duration
                            img_clip = ImageClip(temp_img_path, duration=duration_per_image)

                            # Resize to 1080x1920 (YouTube Shorts format)
                            img_clip = img_clip.resize((1080, 1920))

                            # Subtitles disabled - using images only

                            # Skip fade transitions for faster processing
                            # if i > 0:  # Add fade in for all clips except first
                            #     img_clip = img_clip.fadein(0.3)
                            # if i < len(image_results) - 1:  # Add fade out for all clips except last
                            #     img_clip = img_clip.fadeout(0.3)

                            video_clips.append(img_clip)
                            elapsed = time.time() - start_time
                            print(f"📸 Processed image {i+1}/{len(image_results)} from memory (elapsed: {elapsed:.1f}s)")

                        finally:
                            # Delete temporary image file immediately
                            os.unlink(temp_img_path)

                    except Exception as e:
                        print(f"⚠️ Error processing image {i+1}: {str(e)}")

                        # Try to reuse a successful video clip from previous scenes
                        fallback_image = None
                        if len(video_clips) > 0:
                            # Reuse a random previous clip for variety
                            import random
                            source_clip = random.choice(video_clips)
                            print(f"🔄 Reusing random previous scene clip for scene {i+1}")
                            fallback_image = source_clip.copy().set_duration(duration_per_image)
                        else:
                            # Create a black clip as last resort (this should rarely happen now)
                            print(f"🖤 Creating black fallback for scene {i+1} (no previous clips available)")
                            fallback_image = ImageClip(size=(1080, 1920), color=(0, 0, 0), duration=duration_per_image)

                        # No subtitles - just add fallback image
                        video_clips.append(fallback_image)

                # Concatenate all video clips
                if video_clips:
                    final_video = concatenate_videoclips(video_clips, method="compose")

                    # Set audio
                    final_video = final_video.set_audio(audio_clip)

                    # Ensure video duration matches audio duration
                    final_video = final_video.set_duration(total_duration)

                    # Write video to memory instead of disk
                    print("💾 Creating video in memory...")

                    # Create temporary file for video processing
                    import tempfile
                    with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_video:
                        temp_video_path = temp_video.name

                    final_video.write_videofile(
                        temp_video_path,
                        fps=24,
                        codec='libx264',
                        audio_codec='aac',
                        temp_audiofile='temp-audio.m4a',
                        remove_temp=True,
                        verbose=False,
                        logger=None,
                        preset='ultrafast',  # Fastest encoding preset
                        threads=4  # Use multiple threads
                    )

                    # Read video into memory
                    with open(temp_video_path, 'rb') as video_file:
                        video_data = video_file.read()

                    # Delete temporary video file immediately
                    os.unlink(temp_video_path)
                    print("🗑️ Deleted temporary video file after reading to memory")

                    # Convert to base64 for serving
                    import base64
                    video_base64 = base64.b64encode(video_data).decode('utf-8')

                    # Clean up clips first
                    final_video.close()
                    audio_clip.close()
                    for clip in video_clips:
                        clip.close()

                    # Clean up temporary audio file
                    if temp_audio_path and os.path.exists(temp_audio_path):
                        try:
                            os.unlink(temp_audio_path)
                            print(f"🗑️ Cleaned up temporary audio file")
                        except Exception as cleanup_e:
                            print(f"⚠️ Failed to cleanup temp audio: {str(cleanup_e)}")

                    print(f"✅ Video compiled successfully in memory ({len(video_data)} bytes)")

                    return {
                        'success': True,
                        'duration': total_duration,
                        'method': 'moviepy_memory',
                        'video_data': video_data,
                        'video_base64': video_base64,
                        'size': len(video_data)
                    }
                else:
                    return {
                        'success': False,
                        'error': 'No valid video clips created'
                    }

            except Exception as inner_e:
                print(f"❌ Error during video compilation: {str(inner_e)}")
                return {
                    'success': False,
                    'error': str(inner_e)
                }
            finally:
                # Audio cleanup is now handled after successful video creation
                pass

        except Exception as e:
            import traceback
            print(f"❌ Memory-based MoviePy compilation failed: {str(e)}")
            print(f"❌ Full traceback:")
            traceback.print_exc()
            return {
                'success': False,
                'error': str(e)
            }

    def _cleanup_intermediate_files(self, image_filenames: List[str], audio_filename: str):
        """Final cleanup of any remaining intermediate files (most should already be deleted)"""
        try:
            print("🧹 Final cleanup check for any remaining intermediate files...")

            # Check for any remaining image files (should already be deleted)
            remaining_images = 0
            for image_filename in image_filenames:
                for directory in [self.temp_dir, self.output_dir]:
                    image_path = os.path.join(directory, image_filename)
                    if os.path.exists(image_path):
                        try:
                            os.remove(image_path)
                            print(f"🗑️ Found and deleted remaining image: {image_filename}")
                            remaining_images += 1
                        except Exception as e:
                            print(f"⚠️ Failed to delete remaining image {image_filename}: {str(e)}")

            # Check for any remaining audio files (should already be deleted)
            remaining_audio = 0
            for directory in [self.temp_dir, self.output_dir]:
                audio_path = os.path.join(directory, audio_filename)
                if os.path.exists(audio_path):
                    try:
                        os.remove(audio_path)
                        print(f"🗑️ Found and deleted remaining audio: {audio_filename}")
                        remaining_audio += 1
                    except Exception as e:
                        print(f"⚠️ Failed to delete remaining audio {audio_filename}: {str(e)}")

            # Clean up any other intermediate files in temp directory
            other_files = 0
            if os.path.exists(self.temp_dir):
                for filename in os.listdir(self.temp_dir):
                    file_path = os.path.join(self.temp_dir, filename)
                    # Keep only video files (.mp4) and subtitle files (.srt, .txt)
                    if not filename.endswith(('.mp4', '.srt', '.txt')):
                        try:
                            if os.path.isfile(file_path):
                                os.remove(file_path)
                                print(f"🗑️ Deleted other temp file: {filename}")
                                other_files += 1
                        except Exception as e:
                            print(f"⚠️ Failed to delete temp file {filename}: {str(e)}")

            if remaining_images == 0 and remaining_audio == 0 and other_files == 0:
                print("✅ No remaining intermediate files found - cleanup was successful during processing")
            else:
                print(f"✅ Final cleanup completed - removed {remaining_images} images, {remaining_audio} audio, {other_files} other files")

        except Exception as e:
            print(f"⚠️ Final cleanup failed: {str(e)}")



    def _compile_with_ffmpeg(self, image_paths: List[str], audio_path: str,
                            output_path: str) -> Dict[str, Any]:
        """Compile video using FFmpeg (alternative method)"""
        try:
            if not self.ffmpeg_available:
                return {
                    'success': False,
                    'error': 'FFmpeg not available'
                }
            
            # Create a temporary file list for FFmpeg
            filelist_path = os.path.join(self.temp_dir, 'temp_filelist.txt')
            
            # Get audio duration
            duration_cmd = [
                'ffprobe', '-v', 'quiet', '-show_entries', 'format=duration',
                '-of', 'csv=p=0', audio_path
            ]
            result = subprocess.run(duration_cmd, capture_output=True, text=True)
            total_duration = float(result.stdout.strip())
            
            duration_per_image = total_duration / len(image_paths)
            
            # Create file list for FFmpeg
            with open(filelist_path, 'w') as f:
                for image_path in image_paths:
                    f.write(f"file '{image_path}'\n")
                    f.write(f"duration {duration_per_image}\n")
                # Add last image again to ensure proper duration
                f.write(f"file '{image_paths[-1]}'\n")
            
            # FFmpeg command to create video
            ffmpeg_cmd = [
                'ffmpeg', '-y',  # Overwrite output file
                '-f', 'concat',
                '-safe', '0',
                '-i', filelist_path,
                '-i', audio_path,
                '-c:v', 'libx264',
                '-c:a', 'aac',
                '-pix_fmt', 'yuv420p',
                '-vf', 'scale=1080:1920:force_original_aspect_ratio=decrease,pad=1080:1920:(ow-iw)/2:(oh-ih)/2',
                '-shortest',
                output_path
            ]
            
            # Run FFmpeg
            result = subprocess.run(ffmpeg_cmd, capture_output=True, text=True)
            
            # Clean up temporary file
            if os.path.exists(filelist_path):
                os.remove(filelist_path)
            
            if result.returncode == 0:
                return {
                    'success': True,
                    'duration': total_duration,
                    'method': 'ffmpeg'
                }
            else:
                return {
                    'success': False,
                    'error': f'FFmpeg error: {result.stderr}'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_video_info(self, video_path: str) -> Dict[str, Any]:
        """Get information about a video file"""
        try:
            if not os.path.exists(video_path):
                return {'success': False, 'error': 'Video file not found'}
            
            # Use ffprobe to get video information
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', video_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                info = json.loads(result.stdout)
                
                video_stream = None
                audio_stream = None
                
                for stream in info['streams']:
                    if stream['codec_type'] == 'video':
                        video_stream = stream
                    elif stream['codec_type'] == 'audio':
                        audio_stream = stream
                
                return {
                    'success': True,
                    'duration': float(info['format']['duration']),
                    'size': int(info['format']['size']),
                    'video': {
                        'width': video_stream['width'] if video_stream else None,
                        'height': video_stream['height'] if video_stream else None,
                        'fps': eval(video_stream['r_frame_rate']) if video_stream else None
                    },
                    'audio': {
                        'sample_rate': audio_stream['sample_rate'] if audio_stream else None,
                        'channels': audio_stream['channels'] if audio_stream else None
                    }
                }
            else:
                return {
                    'success': False,
                    'error': f'ffprobe error: {result.stderr}'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
