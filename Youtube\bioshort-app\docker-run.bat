@echo off
REM BioShort Docker Startup Script for Windows
REM This script helps you easily run BioShort with Docker on Windows

echo 🐳 BioShort Docker Startup Script
echo ==================================

REM Check if Docker is installed
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not installed. Please install Docker Desktop first.
    echo    Visit: https://docs.docker.com/desktop/windows/
    pause
    exit /b 1
)

REM Check if Docker Compose is available
docker-compose --version >nul 2>&1
if errorlevel 1 (
    docker compose version >nul 2>&1
    if errorlevel 1 (
        echo ❌ Docker Compose is not available. Please install Docker Compose.
        pause
        exit /b 1
    ) else (
        set COMPOSE_CMD=docker compose
    )
) else (
    set COMPOSE_CMD=docker-compose
)

echo ✅ Docker found
echo ✅ Docker Compose found

REM Check if .env file exists
if not exist ".env" (
    echo ⚠️  .env file not found in project root
    echo 🔧 Creating .env file from template...
    
    (
        echo # BioShort Environment Variables
        echo # Copy this file and update with your actual API keys
        echo.
        echo # Gemini AI API Key ^(Required^)
        echo GEMINI_API_KEY=your_gemini_api_key_here
        echo.
        echo # Google Cloud Text-to-Speech ^(Optional - for better voice quality^)
        echo GOOGLE_APPLICATION_CREDENTIALS=path_to_service_account.json
        echo.
        echo # YouTube API ^(Optional - for direct uploads^)
        echo YOUTUBE_CLIENT_ID=your_youtube_client_id
        echo YOUTUBE_CLIENT_SECRET=your_youtube_client_secret
        echo.
        echo # Flask Configuration
        echo FLASK_DEBUG=false
        echo FLASK_ENV=production
    ) > .env
    
    echo 📝 .env file created. Please edit it with your API keys:
    echo    - Get Gemini API key from: https://makersuite.google.com/app/apikey
    echo    - Update GEMINI_API_KEY in .env file
    echo.
    echo 🔧 Edit .env file and run this script again
    pause
    exit /b 1
)

echo 🔍 Checking .env file...
findstr /C:"your_gemini_api_key_here" .env >nul
if not errorlevel 1 (
    echo ⚠️  Please update your API keys in .env file
    echo 🔧 Edit .env and replace placeholder values with real API keys
    pause
    exit /b 1
)

echo ✅ .env file looks good

REM Stop existing containers if running
echo 🛑 Stopping existing containers...
%COMPOSE_CMD% down >nul 2>&1

REM Build and start the containers
echo 🏗️ Building and starting containers...
%COMPOSE_CMD% up --build -d

REM Wait a moment for containers to start
echo ⏳ Waiting for containers to start...
timeout /t 5 /nobreak >nul

REM Check container status
echo 📊 Container Status:
%COMPOSE_CMD% ps

echo.
echo 🎉 BioShort is now running!
echo ==================================
echo 🌐 Frontend: http://localhost:5173
echo 🔧 Backend API: http://localhost:5000
echo 📊 Health Check: http://localhost:5000/api/health
echo.
echo 📋 Useful commands:
echo    View logs: %COMPOSE_CMD% logs
echo    Stop: %COMPOSE_CMD% down
echo    Restart: %COMPOSE_CMD% restart
echo    Rebuild: %COMPOSE_CMD% up --build
echo.
echo 🐛 If you encounter issues:
echo    1. Check logs: %COMPOSE_CMD% logs
echo    2. Restart: %COMPOSE_CMD% restart
echo    3. Rebuild: %COMPOSE_CMD% down ^&^& %COMPOSE_CMD% up --build
echo.
echo ✨ Happy video generating!
echo.
echo Press any key to open the application in your browser...
pause >nul

REM Open the application in default browser
start http://localhost:5173
