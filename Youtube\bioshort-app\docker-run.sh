#!/bin/bash

# BioShort Docker Startup Script
# This script helps you easily run BioShort with Docker

set -e

echo "🐳 BioShort Docker Startup Script"
echo "=================================="

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    echo "   Visit: https://docs.docker.com/get-docker/"
    exit 1
fi

# Check if Docker Compose is available
if command -v docker-compose &> /dev/null; then
    COMPOSE_CMD="docker-compose"
elif command -v docker &> /dev/null && docker compose version &> /dev/null; then
    COMPOSE_CMD="docker compose"
else
    echo "❌ Docker Compose is not available. Please install Docker Compose."
    exit 1
fi

echo "✅ Docker found: $(docker --version)"
echo "✅ Docker Compose found: $($COMPOSE_CMD --version)"

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️  .env file not found in project root"
    echo "🔧 Creating .env file from template..."
    
    cat > .env << 'EOF'
# BioShort Environment Variables
# Copy this file and update with your actual API keys

# Gemini AI API Key (Required)
GEMINI_API_KEY=your_gemini_api_key_here

# Google Cloud Text-to-Speech (Optional - for better voice quality)
GOOGLE_APPLICATION_CREDENTIALS=path_to_service_account.json

# YouTube API (Optional - for direct uploads)
YOUTUBE_CLIENT_ID=your_youtube_client_id
YOUTUBE_CLIENT_SECRET=your_youtube_client_secret

# Flask Configuration
FLASK_DEBUG=false
FLASK_ENV=production
EOF

    echo "📝 .env file created. Please edit it with your API keys:"
    echo "   - Get Gemini API key from: https://makersuite.google.com/app/apikey"
    echo "   - Update GEMINI_API_KEY in .env file"
    echo ""
    echo "🔧 Edit .env file and run this script again"
    exit 1
fi

echo "🔍 Checking .env file..."
if grep -q "your_gemini_api_key_here" .env; then
    echo "⚠️  Please update your API keys in .env file"
    echo "🔧 Edit .env and replace placeholder values with real API keys"
    exit 1
fi

echo "✅ .env file looks good"

# Stop existing containers if running
echo "🛑 Stopping existing containers..."
$COMPOSE_CMD down 2>/dev/null || true

# Build and start the containers
echo "🏗️ Building and starting containers..."
$COMPOSE_CMD up --build -d

# Wait a moment for containers to start
echo "⏳ Waiting for containers to start..."
sleep 5

# Check container status
echo "📊 Container Status:"
$COMPOSE_CMD ps

# Check if services are healthy
echo ""
echo "🔍 Checking service health..."

# Check backend health
echo "🔧 Backend health check..."
for i in {1..10}; do
    if curl -s http://localhost:5000/api/health > /dev/null; then
        echo "✅ Backend is healthy!"
        break
    else
        echo "⏳ Waiting for backend... (attempt $i/10)"
        sleep 2
    fi
    
    if [ $i -eq 10 ]; then
        echo "❌ Backend health check failed"
        echo "📋 Backend logs:"
        $COMPOSE_CMD logs backend
        exit 1
    fi
done

# Check frontend
echo "🎨 Frontend health check..."
for i in {1..10}; do
    if curl -s http://localhost:5173 > /dev/null; then
        echo "✅ Frontend is healthy!"
        break
    else
        echo "⏳ Waiting for frontend... (attempt $i/10)"
        sleep 2
    fi
    
    if [ $i -eq 10 ]; then
        echo "❌ Frontend health check failed"
        echo "📋 Frontend logs:"
        $COMPOSE_CMD logs frontend
        exit 1
    fi
done

echo ""
echo "🎉 BioShort is now running!"
echo "=================================="
echo "🌐 Frontend: http://localhost:5173"
echo "🔧 Backend API: http://localhost:5000"
echo "🤖 Airflow Dashboard: http://localhost:8080"
echo "   Username: airflow"
echo "   Password: airflow"
echo "📊 Health Check: http://localhost:5000/api/health"
echo ""
echo "📋 Useful commands:"
echo "   View logs: $COMPOSE_CMD logs"
echo "   Stop: $COMPOSE_CMD down"
echo "   Restart: $COMPOSE_CMD restart"
echo "   Rebuild: $COMPOSE_CMD up --build"
echo ""
echo "🐛 If you encounter issues:"
echo "   1. Check logs: $COMPOSE_CMD logs"
echo "   2. Restart: $COMPOSE_CMD restart"
echo "   3. Rebuild: $COMPOSE_CMD down && $COMPOSE_CMD up --build"
echo ""
echo "🎯 Automation runs daily at 9 AM"
echo "   Add people to queue in the frontend!"
echo ""
echo "✨ Happy video generating!"
