#!/usr/bin/env python3
"""
YouTube Uploader Service
Handles secure video uploads to YouTube using OAuth2 refresh tokens
"""

import os
import io
import json
import base64
import tempfile
from typing import Dict, Any, Optional
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from googleapiclient.http import MediaIoBaseUpload, MediaFileUpload
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from config.env import env


class YouTubeUploader:
    """
    Service for uploading videos to YouTube using stored refresh tokens
    """
    
    def __init__(self):
        """Initialize YouTube uploader with credentials from environment"""
        self.client_id = env.YOUTUBE_CLIENT_ID
        self.client_secret = env.YOUTUBE_CLIENT_SECRET
        self.refresh_token = env.YOUTUBE_REFRESH_TOKEN
        
        if not all([self.client_id, self.client_secret, self.refresh_token]):
            raise ValueError("Missing YouTube API credentials in environment variables")
        
        self.youtube_service = None
        print("🔑 YouTube uploader initialized with stored credentials")
    
    def _get_authenticated_service(self) -> Any:
        """
        Get authenticated YouTube service using refresh token
        """
        try:
            # Create credentials from refresh token
            credentials = Credentials(
                token=None,
                refresh_token=self.refresh_token,
                token_uri="https://oauth2.googleapis.com/token",
                client_id=self.client_id,
                client_secret=self.client_secret,
                scopes=['https://www.googleapis.com/auth/youtube.upload']
            )
            
            # Refresh the access token
            credentials.refresh(Request())
            
            # Build YouTube service
            youtube_service = build('youtube', 'v3', credentials=credentials)
            print("✅ YouTube service authenticated successfully")
            
            return youtube_service
            
        except Exception as e:
            print(f"❌ YouTube authentication failed: {str(e)}")
            raise Exception(f"Failed to authenticate with YouTube: {str(e)}")
    
    def upload_video(self, video_data: bytes, title: str, description: str, 
                    privacy_status: str = 'private', tags: Optional[list] = None) -> Dict[str, Any]:
        """
        Upload video to YouTube
        
        Args:
            video_data: Video file data as bytes
            title: Video title
            description: Video description
            privacy_status: 'private', 'public', 'unlisted'
            tags: List of tags for the video
            
        Returns:
            Dict with upload result
        """
        try:
            print(f"🎬 Starting YouTube upload: {title}")
            print(f"📊 Video size: {len(video_data)} bytes")
            print(f"🔒 Privacy: {privacy_status}")
            
            # Get authenticated service
            if not self.youtube_service:
                self.youtube_service = self._get_authenticated_service()
            
            # Prepare video metadata
            video_metadata = {
                'snippet': {
                    'title': title,
                    'description': description,
                    'tags': tags or [],
                    'categoryId': '22'  # People & Blogs category
                },
                'status': {
                    'privacyStatus': privacy_status,
                    'selfDeclaredMadeForKids': False
                }
            }
            
            # Create media upload object from bytes with chunked upload
            video_stream = io.BytesIO(video_data)

            # Use smaller chunk size for better progress tracking and memory efficiency
            chunk_size = 1024 * 1024  # 1MB chunks
            media = MediaIoBaseUpload(
                video_stream,
                mimetype='video/mp4',
                chunksize=chunk_size,
                resumable=True
            )
            
            # Execute upload
            print("⬆️ Uploading video to YouTube...")
            insert_request = self.youtube_service.videos().insert(
                part=','.join(video_metadata.keys()),
                body=video_metadata,
                media_body=media
            )
            
            # Execute the upload
            response = None
            while response is None:
                status, response = insert_request.next_chunk()
                if status:
                    progress = int(status.progress() * 100)
                    print(f"📈 Upload progress: {progress}%")
            
            if response:
                video_id = response['id']
                video_url = f"https://www.youtube.com/watch?v={video_id}"
                
                print(f"✅ Video uploaded successfully!")
                print(f"🔗 Video URL: {video_url}")
                print(f"🆔 Video ID: {video_id}")
                
                return {
                    'success': True,
                    'video_id': video_id,
                    'video_url': video_url,
                    'title': title,
                    'privacy_status': privacy_status,
                    'message': 'Video uploaded successfully to YouTube'
                }
            else:
                return {
                    'success': False,
                    'error': 'Upload completed but no response received'
                }
                
        except HttpError as e:
            error_details = json.loads(e.content.decode('utf-8'))
            error_message = error_details.get('error', {}).get('message', str(e))
            
            print(f"❌ YouTube API error: {error_message}")
            return {
                'success': False,
                'error': f'YouTube API error: {error_message}',
                'error_code': e.resp.status
            }
            
        except Exception as e:
            print(f"❌ Upload error: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def upload_video_from_base64(self, video_base64: str, title: str,
                                description: str, privacy_status: str = 'private',
                                tags: Optional[list] = None) -> Dict[str, Any]:
        """
        Upload video from base64 encoded data using temporary file for efficiency

        Args:
            video_base64: Base64 encoded video data
            title: Video title
            description: Video description
            privacy_status: 'private', 'public', 'unlisted'
            tags: List of tags for the video

        Returns:
            Dict with upload result
        """
        temp_file_path = None
        try:
            print(f"🔄 Decoding base64 video data...")
            # Decode base64 to bytes
            video_data = base64.b64decode(video_base64)
            video_size_mb = len(video_data) / (1024 * 1024)
            print(f"📊 Video size: {video_size_mb:.2f} MB")

            # Create temporary file to avoid memory issues with large videos
            with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_file:
                temp_file_path = temp_file.name
                temp_file.write(video_data)
                print(f"💾 Temporary file created: {temp_file_path}")

            # Upload using temporary file
            result = self.upload_video_from_file(
                file_path=temp_file_path,
                title=title,
                description=description,
                privacy_status=privacy_status,
                tags=tags
            )

            return result

        except Exception as e:
            print(f"❌ Base64 decode error: {str(e)}")
            return {
                'success': False,
                'error': f'Failed to decode video data: {str(e)}'
            }
        finally:
            # Clean up temporary file
            if temp_file_path and os.path.exists(temp_file_path):
                try:
                    os.unlink(temp_file_path)
                    print(f"🗑️ Temporary file deleted: {temp_file_path}")
                except Exception as e:
                    print(f"⚠️ Failed to delete temporary file: {str(e)}")

    def upload_video_from_file(self, file_path: str, title: str,
                              description: str, privacy_status: str = 'private',
                              tags: Optional[list] = None) -> Dict[str, Any]:
        """
        Upload video from file path (more memory efficient for large files)

        Args:
            file_path: Path to video file
            title: Video title
            description: Video description
            privacy_status: 'private', 'public', 'unlisted'
            tags: List of tags for the video

        Returns:
            Dict with upload result
        """
        try:
            if not os.path.exists(file_path):
                return {
                    'success': False,
                    'error': f'Video file not found: {file_path}'
                }

            file_size = os.path.getsize(file_path)
            file_size_mb = file_size / (1024 * 1024)

            print(f"🎬 Starting YouTube upload from file: {title}")
            print(f"📊 File size: {file_size_mb:.2f} MB")
            print(f"🔒 Privacy: {privacy_status}")

            # Validate and clean title and description
            if not title or not title.strip():
                return {
                    'success': False,
                    'error': 'Video title cannot be empty'
                }

            # Clean and validate title (YouTube has specific requirements)
            clean_title = title.strip()
            if len(clean_title) > 100:
                clean_title = clean_title[:97] + "..."
                print(f"⚠️ Title truncated to 100 characters: {clean_title}")

            # Clean description
            clean_description = description.strip() if description else ""
            if len(clean_description) > 5000:
                clean_description = clean_description[:4997] + "..."
                print(f"⚠️ Description truncated to 5000 characters")

            print(f"📝 Final title: '{clean_title}'")
            print(f"📝 Final description length: {len(clean_description)} chars")
            print(f"🏷️ Tags: {tags}")

            # Get authenticated service
            if not self.youtube_service:
                self.youtube_service = self._get_authenticated_service()

            # Prepare video metadata
            video_metadata = {
                'snippet': {
                    'title': clean_title,
                    'description': clean_description,
                    'tags': tags or [],
                    'categoryId': '22'  # People & Blogs category
                },
                'status': {
                    'privacyStatus': privacy_status,
                    'selfDeclaredMadeForKids': False
                }
            }

            print(f"📋 Video metadata prepared: {json.dumps(video_metadata, indent=2)}")

            # Create media upload object from file
            chunk_size = 1024 * 1024  # 1MB chunks for better progress tracking
            media = MediaFileUpload(
                file_path,
                mimetype='video/mp4',
                chunksize=chunk_size,
                resumable=True
            )

            # Execute upload
            print("⬆️ Uploading video to YouTube...")
            insert_request = self.youtube_service.videos().insert(
                part=','.join(video_metadata.keys()),
                body=video_metadata,
                media_body=media
            )

            # Execute the upload with progress tracking
            response = None
            while response is None:
                status, response = insert_request.next_chunk()
                if status:
                    progress = int(status.progress() * 100)
                    print(f"📈 Upload progress: {progress}%")

            if response:
                video_id = response['id']
                video_url = f"https://www.youtube.com/watch?v={video_id}"

                print(f"✅ Video uploaded successfully!")
                print(f"🔗 Video URL: {video_url}")
                print(f"🆔 Video ID: {video_id}")

                return {
                    'success': True,
                    'video_id': video_id,
                    'video_url': video_url,
                    'title': title,
                    'privacy_status': privacy_status,
                    'message': 'Video uploaded successfully to YouTube'
                }
            else:
                return {
                    'success': False,
                    'error': 'Upload completed but no response received'
                }

        except HttpError as e:
            error_details = json.loads(e.content.decode('utf-8'))
            error_message = error_details.get('error', {}).get('message', str(e))

            print(f"❌ YouTube API error: {error_message}")
            return {
                'success': False,
                'error': f'YouTube API error: {error_message}',
                'error_code': e.resp.status
            }

        except Exception as e:
            print(f"❌ Upload error: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_upload_quota_info(self) -> Dict[str, Any]:
        """
        Get information about YouTube upload quotas and limits
        """
        return {
            'daily_upload_limit': '6 videos per day for new channels',
            'file_size_limit': '256 GB or 12 hours',
            'supported_formats': ['MP4', 'MOV', 'AVI', 'WMV', 'FLV', 'WebM'],
            'recommended_format': 'MP4 with H.264 video codec and AAC audio codec'
        }
