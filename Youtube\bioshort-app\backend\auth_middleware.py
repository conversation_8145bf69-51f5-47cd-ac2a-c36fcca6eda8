"""
Simple authentication middleware
"""
from functools import wraps
from flask import request, jsonify
from config.env import env

def require_auth(f):
    """Decorator to require authentication - simplified for development"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Skip auth in development mode
        if env.FLASK_ENV == 'development':
            return f(*args, **kwargs)

        # In production, you can add simple token-based auth here
        # For now, just allow all requests
        return f(*args, **kwargs)

    return decorated_function

def optional_auth(f):
    """Decorator for optional authentication"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # No authentication required
        request.user = None
        return f(*args, **kwargs)

    return decorated_function
