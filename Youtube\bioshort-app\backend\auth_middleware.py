"""
Authentication middleware for Kinde integration
"""
import jwt
import requests
from functools import wraps
from flask import request, jsonify
from config.env import env

class KindeAuth:
    def __init__(self):
        self.domain = env.VITE_KINDE_DOMAIN.replace('VITE_', '') if env.VITE_KINDE_DOMAIN else ''
        self.client_id = env.VITE_KINDE_CLIENT_ID.replace('VITE_', '') if env.VITE_KINDE_CLIENT_ID else ''
        self.jwks_url = f"{self.domain}/.well-known/jwks" if self.domain else ''
        self._jwks_cache = None
    
    def get_jwks(self):
        """Get JSON Web Key Set from Kinde"""
        if self._jwks_cache is None:
            try:
                response = requests.get(self.jwks_url, timeout=10)
                response.raise_for_status()
                self._jwks_cache = response.json()
            except Exception as e:
                print(f"Failed to fetch JWKS: {e}")
                return None
        return self._jwks_cache
    
    def verify_token(self, token):
        """Verify JWT token from Kinde"""
        try:
            # For development, we'll use a simple approach
            # In production, you'd want to verify the JWT signature properly
            
            # Decode without verification for now (development only)
            decoded = jwt.decode(token, options={"verify_signature": False})
            
            # Check if token is from our Kinde domain
            if decoded.get('iss') != self.domain:
                return None
                
            # Check if token is for our client
            if decoded.get('aud') != self.client_id:
                return None
                
            return decoded
            
        except Exception as e:
            print(f"Token verification failed: {e}")
            return None

# Global auth instance
kinde_auth = KindeAuth()

def require_auth(f):
    """Decorator to require authentication for API endpoints"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Skip auth in development if no token provided
        if env.FLASK_ENV == 'development':
            auth_header = request.headers.get('Authorization')
            if not auth_header:
                print("⚠️ Development mode: Skipping authentication")
                return f(*args, **kwargs)
        
        # Get token from Authorization header
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'error': 'Authorization header required'}), 401
        
        token = auth_header.split(' ')[1]
        
        # Verify token
        user_data = kinde_auth.verify_token(token)
        if not user_data:
            return jsonify({'error': 'Invalid or expired token'}), 401
        
        # Add user data to request context
        request.user = user_data
        
        return f(*args, **kwargs)
    
    return decorated_function

def optional_auth(f):
    """Decorator for optional authentication (for public endpoints)"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Try to get user data if token is provided
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1]
            user_data = kinde_auth.verify_token(token)
            request.user = user_data
        else:
            request.user = None
        
        return f(*args, **kwargs)
    
    return decorated_function
