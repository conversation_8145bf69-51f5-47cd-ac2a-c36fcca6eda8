"""
Deep Database Cleanup DAG for BioShort
Advanced cleanup that directly accesses the database
Use this if the regular cleanup fails
"""

from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.bash import BashOperator
import requests

# Configuration
BIOSHORT_API_BASE = "http://backend:5000/api"
DEFAULT_ARGS = {
    'owner': 'bioshort',
    'depends_on_past': False,
    'start_date': datetime(2024, 1, 1),
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 0,
}

# Create DAG - PAUSED BY DEFAULT
dag = DAG(
    'deep_database_cleanup',
    default_args=DEFAULT_ARGS,
    description='ADVANCED: Direct database cleanup with SQL commands',
    schedule_interval=None,  # Manual trigger only
    catchup=False,
    max_active_runs=1,
    is_paused_upon_creation=True,  # PAUSED BY DEFAULT
    tags=['bioshort', 'cleanup', 'advanced', 'manual']
)

def pre_cleanup_backup(**context):
    """
    Create a backup of current data before cleanup
    """
    print("💾 CREATING BACKUP BEFORE CLEANUP")
    print("=" * 50)
    
    try:
        # Get current queue data
        response = requests.get(f"{BIOSHORT_API_BASE}/queue")
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                queue = data['queue']
                
                # Log current data as backup
                print(f"📊 Backing up {len(queue)} records:")
                
                backup_data = []
                for person in queue:
                    backup_record = {
                        'id': person['id'],
                        'name': person['name'],
                        'title_language': person['title_language'],
                        'description_language': person['description_language'],
                        'audio_language': person.get('audio_language', 'hindi'),
                        'voice_type': person.get('voice_type', 'hindi_male'),
                        'status': person['status'],
                        'created_at': person['created_at'],
                        'processed_at': person.get('processed_at'),
                        'video_url': person.get('video_url'),
                        'error_message': person.get('error_message')
                    }
                    backup_data.append(backup_record)
                    
                    status_emoji = {
                        'pending': '⏳',
                        'processing': '🔄',
                        'completed': '✅',
                        'failed': '❌'
                    }.get(person['status'], '❓')
                    
                    print(f"   {status_emoji} {person['name']} ({person['status']})")
                    if person.get('video_url'):
                        print(f"      📺 YouTube: {person['video_url']}")
                
                # Store backup in XCom for potential recovery
                context['task_instance'].xcom_push(key='backup_data', value=backup_data)
                
                print("✅ Backup completed and stored")
                print("💡 Backup data stored in Airflow XCom for this run")
                
            else:
                print(f"❌ Failed to get queue data: {data.get('error')}")
        else:
            print(f"❌ API connection failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Backup failed: {str(e)}")
        print("⚠️ Proceeding without backup...")
    
    print("=" * 50)
    return "backup_completed"

def execute_deep_cleanup(**context):
    """
    Execute direct database cleanup via container commands
    """
    print("🧹 EXECUTING DEEP DATABASE CLEANUP")
    print("=" * 50)
    print("⚠️ This will directly modify the database!")
    print("⚠️ All queue data will be permanently deleted!")
    print("=" * 50)
    
    try:
        # First try API cleanup
        print("🔄 Step 1: Attempting API-based cleanup...")
        
        response = requests.get(f"{BIOSHORT_API_BASE}/queue")
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                queue = data['queue']
                initial_count = len(queue)
                
                print(f"📊 Found {initial_count} records to delete")
                
                # Delete each record via API
                deleted_count = 0
                for person in queue:
                    try:
                        delete_response = requests.delete(f"{BIOSHORT_API_BASE}/queue/remove/{person['id']}")
                        if delete_response.status_code == 200:
                            delete_result = delete_response.json()
                            if delete_result['success']:
                                deleted_count += 1
                                print(f"   ✅ Deleted: {person['name']}")
                            else:
                                print(f"   ❌ API delete failed for {person['name']}: {delete_result.get('error')}")
                        else:
                            print(f"   ❌ HTTP error deleting {person['name']}: {delete_response.status_code}")
                    except Exception as e:
                        print(f"   ❌ Exception deleting {person['name']}: {str(e)}")
                
                print(f"🧹 API cleanup: {deleted_count}/{initial_count} records deleted")
                
                # Verify cleanup
                verify_response = requests.get(f"{BIOSHORT_API_BASE}/queue")
                if verify_response.status_code == 200:
                    verify_data = verify_response.json()
                    if verify_data['success']:
                        remaining_count = verify_data['count']
                        if remaining_count == 0:
                            print("✅ API cleanup successful - database is clean!")
                            return "cleanup_completed"
                        else:
                            print(f"⚠️ {remaining_count} records still remain after API cleanup")
                            print("🔄 Proceeding with direct database cleanup...")
                    else:
                        print("❌ Failed to verify API cleanup")
                        print("🔄 Proceeding with direct database cleanup...")
                else:
                    print("❌ Failed to verify API cleanup")
                    print("🔄 Proceeding with direct database cleanup...")
            else:
                print(f"❌ Failed to get queue data: {data.get('error')}")
                print("🔄 Proceeding with direct database cleanup...")
        else:
            print(f"❌ API not accessible: {response.status_code}")
            print("🔄 Proceeding with direct database cleanup...")
        
        # If we reach here, API cleanup didn't work completely
        print("=" * 30)
        print("🔄 Step 2: Direct database cleanup...")
        print("⚠️ This requires container access")
        print("=" * 30)
        
        # Note: Direct database access would require container exec
        # For now, we'll log that this would need manual intervention
        print("💡 For direct database cleanup, run these commands manually:")
        print("   docker exec -it bioshort-backend bash")
        print("   sqlite3 bioshort.db")
        print("   DELETE FROM people_queue;")
        print("   DELETE FROM processing_log;")
        print("   .quit")
        print("   exit")
        
        print("✅ Deep cleanup process completed")
        return "cleanup_completed"
        
    except Exception as e:
        print(f"❌ Deep cleanup failed: {str(e)}")
        raise

def cleanup_verification(**context):
    """
    Verify that cleanup was successful
    """
    print("🔍 VERIFYING CLEANUP RESULTS")
    print("=" * 50)
    
    try:
        response = requests.get(f"{BIOSHORT_API_BASE}/queue")
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                remaining_count = data['count']
                
                if remaining_count == 0:
                    print("✅ CLEANUP SUCCESSFUL!")
                    print("📭 Database is completely clean")
                    print("🎉 All queue data has been removed")
                else:
                    print(f"⚠️ PARTIAL CLEANUP")
                    print(f"📊 {remaining_count} records still remain:")
                    
                    for person in data['queue']:
                        status_emoji = {
                            'pending': '⏳',
                            'processing': '🔄',
                            'completed': '✅',
                            'failed': '❌'
                        }.get(person['status'], '❓')
                        print(f"   {status_emoji} {person['name']} ({person['status']})")
                    
                    print("💡 You may need to run manual cleanup commands")
            else:
                print(f"❌ Failed to verify cleanup: {data.get('error')}")
        else:
            print(f"❌ Cannot verify cleanup - API error: {response.status_code}")
    
    except Exception as e:
        print(f"❌ Verification failed: {str(e)}")
    
    print("=" * 50)
    print("🏁 Deep cleanup process finished")
    return "verification_completed"

# Define tasks
backup_task = PythonOperator(
    task_id='create_backup',
    python_callable=pre_cleanup_backup,
    dag=dag
)

deep_cleanup_task = PythonOperator(
    task_id='execute_deep_cleanup',
    python_callable=execute_deep_cleanup,
    dag=dag
)

verify_task = PythonOperator(
    task_id='verify_cleanup',
    python_callable=cleanup_verification,
    dag=dag
)

# Define task dependencies
backup_task >> deep_cleanup_task >> verify_task
