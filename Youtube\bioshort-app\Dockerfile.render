# Unified Dockerfile for Render deployment
# This combines frontend, backend, and Airflow with SQLite for simplicity

# Stage 1: Build Frontend
FROM node:18-alpine AS frontend-builder
WORKDIR /app/frontend

# Set build-time environment variable for Vite
ARG VITE_API_BASE_URL
ENV VITE_API_BASE_URL=$VITE_API_BASE_URL

# Copy package.json first
COPY frontend/package.json ./
# Install dependencies (this will create a new package-lock.json)
RUN npm install
COPY frontend/ ./
RUN npm run build

# Stage 2: Main Application
FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    netcat-openbsd \
    ffmpeg \
    libsm6 \
    libxext6 \
    libfontconfig1 \
    libxrender1 \
    libgl1-mesa-glx \
    supervisor \
    nginx \
    sqlite3 \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Copy backend requirements and install Python dependencies
COPY backend/requirements.txt ./backend/
RUN pip install --no-cache-dir -r backend/requirements.txt

# Install Airflow with all required dependencies
RUN pip install pendulum==2.1.2 Flask-Session==0.5.0 connexion==2.14.2
RUN pip install apache-airflow==2.7.0

# Copy backend code
COPY backend/ ./backend/

# Copy frontend build from previous stage
COPY --from=frontend-builder /app/frontend/dist ./frontend/dist

# Copy Airflow configuration
COPY airflow/ ./airflow/

# Create necessary directories and set permissions
RUN mkdir -p /app/logs /app/temp /app/output /app/data
RUN chmod 755 /app/logs /app/temp /app/output /app/data

# Copy configuration files
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf
COPY nginx.conf /etc/nginx/sites-available/default

# Set environment variables
ENV AIRFLOW_HOME=/app/airflow
ENV PYTHONPATH=/app/backend
ENV AIRFLOW__DATABASE__SQL_ALCHEMY_CONN=sqlite:////app/data/airflow.db
ENV AIRFLOW__CORE__EXECUTOR=SequentialExecutor

# Copy startup script
COPY start.sh /app/start.sh
RUN chmod +x /app/start.sh

# Expose port (Render assigns the PORT environment variable)
EXPOSE $PORT

# Start the application
CMD ["/app/start.sh"]
