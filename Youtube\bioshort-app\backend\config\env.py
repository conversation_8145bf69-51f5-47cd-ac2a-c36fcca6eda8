"""
Environment Configuration for Backend
Simple environment variable access that reads from the root .env file
"""

import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables from root .env file
root_env_path = Path(__file__).resolve().parent.parent.parent / '.env'
print(f"🔍 Loading environment from: {root_env_path}")
print(f"🔍 .env file exists: {root_env_path.exists()}")

load_dotenv(dotenv_path=root_env_path)

class Env:
    """Environment configuration class"""

    # Google API Configuration
    GOOGLE_API_KEY = os.getenv('GOOGLE_API_KEY')
    GOOGLE_APPLICATION_CREDENTIALS = os.getenv('GOOGLE_APPLICATION_CREDENTIALS', '')
    GOOGLE_CLOUD_PROJECT = os.getenv('GOOGLE_CLOUD_PROJECT', '')

    # YouTube API Configuration
    YOUTUBE_CLIENT_ID = os.getenv('YOUTUBE_CLIENT_ID', '')
    YOUTUBE_CLIENT_SECRET = os.getenv('YOUTUBE_CLIENT_SECRET', '')
    YOUTUBE_REFRESH_TOKEN = os.getenv('YOUTUBE_REFRESH_TOKEN', '')

    # Flask Configuration
    FLASK_ENV = os.getenv('FLASK_ENV', 'development')
    FLASK_DEBUG = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'

    # Backend Configuration
    BACKEND_URL = os.getenv('BACKEND_URL', 'http://localhost:5000')

    # Frontend Configuration (for CORS and integration)
    FRONTEND_URL = os.getenv('FRONTEND_URL', 'http://localhost:5173')
    VITE_API_BASE_URL = os.getenv('VITE_API_BASE_URL', 'http://localhost:5000/api')

    # Kinde Auth Configuration (if used)
    VITE_KINDE_DOMAIN = os.getenv('VITE_KINDE_DOMAIN', '')
    VITE_KINDE_CLIENT_ID = os.getenv('VITE_KINDE_CLIENT_ID', '')

# Create instance for easy access
env = Env()

# Debug: Print loaded environment variables
print(f"🔧 Environment loaded:")
print(f"  FLASK_ENV: {env.FLASK_ENV}")
print(f"  FLASK_DEBUG: {env.FLASK_DEBUG}")
print(f"  BACKEND_URL: {env.BACKEND_URL}")
print(f"  FRONTEND_URL: {env.FRONTEND_URL}")
print(f"  VITE_API_BASE_URL: {env.VITE_API_BASE_URL}")
print(f"  GOOGLE_API_KEY: {'***' if env.GOOGLE_API_KEY else 'NOT SET'}")

# Export for convenience
__all__ = ['env', 'Env']
