"""
Environment Configuration for Backend
Simple environment variable access that reads from the root .env file
"""

import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables from root .env file
root_env_path = Path(__file__).resolve().parent.parent.parent / '.env'
load_dotenv(dotenv_path=root_env_path)

class Env:
    """Environment configuration class"""

    # Google API Configuration
    GOOGLE_API_KEY = os.getenv('GOOGLE_API_KEY')
    GOOGLE_APPLICATION_CREDENTIALS = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
    GOOGLE_CLOUD_PROJECT = os.getenv('GOOGLE_CLOUD_PROJECT')

    # YouTube API Configuration
    YOUTUBE_CLIENT_ID = os.getenv('YOUTUBE_CLIENT_ID')
    YOUTUBE_CLIENT_SECRET = os.getenv('YOUTUBE_CLIENT_SECRET')
    YOUTUBE_REFRESH_TOKEN = os.getenv('YOUTUBE_REFRESH_TOKEN')

    # Flask Configuration
    FLASK_ENV = os.getenv('FLASK_ENV')
    FLASK_DEBUG = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'

    # Frontend Configuration (for CORS and integration)
    FRONTEND_URL = os.getenv('FRONTEND_URL')
    VITE_API_BASE_URL = os.getenv('VITE_API_BASE_URL')

# Create instance for easy access
env = Env()

# Export for convenience
__all__ = ['env', 'Env']
