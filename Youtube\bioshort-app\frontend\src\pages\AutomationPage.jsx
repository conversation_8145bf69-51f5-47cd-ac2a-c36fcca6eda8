import React from 'react'
import AutomationQueueSection from '../components/AutomationQueueSection'

const AutomationPage = () => {
  return (
    <div className="space-y-8">
      {/* Automation Header */}
      <header className="text-center mb-12 fade-in">
        <div className="bg-slate-800/90 backdrop-blur-xl rounded-3xl shadow-2xl border border-slate-700/50 p-8 max-w-4xl mx-auto relative overflow-hidden">
          {/* Background effects */}
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-blue-500/5"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(147,51,234,0.1),transparent_50%)]"></div>

          <div className="relative z-10">
            <div className="flex items-center justify-center gap-4 mb-4">
              <div className="text-6xl">🤖</div>
              <h1 className="text-5xl md:text-6xl font-bold">
                <span className="bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 bg-clip-text text-transparent">
                  Automation Queue
                </span>
              </h1>
            </div>
            <p className="text-xl text-slate-300 mb-6">
              Schedule and manage automated video generation for multiple people
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-sm">
              <div className="flex items-center gap-2 bg-purple-500/20 border border-purple-500/30 px-4 py-2 rounded-full backdrop-blur-sm">
                <span className="w-2 h-2 bg-purple-400 rounded-full animate-pulse shadow-lg shadow-purple-400/50"></span>
                <span className="text-purple-300 font-medium">Daily Automation</span>
              </div>
              <div className="flex items-center gap-2 bg-pink-500/20 border border-pink-500/30 px-4 py-2 rounded-full backdrop-blur-sm">
                <span className="w-2 h-2 bg-pink-400 rounded-full animate-pulse shadow-lg shadow-pink-400/50"></span>
                <span className="text-pink-300 font-medium">Queue Management</span>
              </div>
              <div className="flex items-center gap-2 bg-red-500/20 border border-red-500/30 px-4 py-2 rounded-full backdrop-blur-sm">
                <span className="w-2 h-2 bg-red-400 rounded-full animate-pulse shadow-lg shadow-red-400/50"></span>
                <span className="text-red-300 font-medium">Auto YouTube Upload</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Automation Queue Section */}
      <AutomationQueueSection />
    </div>
  )
}

export default AutomationPage
