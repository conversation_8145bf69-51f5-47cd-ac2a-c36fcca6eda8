import React, { useState, useEffect } from 'react'
import { useVideoGeneration } from '../context/VideoGenerationContext'

const YouTubeContentSection = ({ personName, language, storyData = null }) => {
  const { state, actions } = useVideoGeneration()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)
  const [copiedItem, setCopiedItem] = useState(null)
  const [currentLanguage, setCurrentLanguage] = useState(language)
  const [isTranslating, setIsTranslating] = useState(false)

  useEffect(() => {
    if (personName && language) {
      setCurrentLanguage(language)
      const youtubeContentLocal = state.results.youtubeContent
      if (youtubeContentLocal) {
        // Use existing YouTube content from context
        setIsLoading(false)
        setError(null)
      } else {
        generateYouTubeContent()
      }
    }
  }, [personName, language, state.results.youtubeContent])

  const youtubeContent = state.results.youtubeContent || null

  useEffect(() => {
    setCurrentLanguage(language)
  }, [language])

  const generateYouTubeContent = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL
      const response = await fetch(`${apiBaseUrl}/generate-youtube-content`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          person_name: personName,
          language: currentLanguage,
          story_data: storyData
        })
      })

      const result = await response.json()

      if (result.success) {
        actions.setYoutubeContent(result)
      } else {
        throw new Error(result.error || 'Failed to generate YouTube content')
      }
    } catch (error) {
      console.error('Error generating YouTube content:', error)
      setError(error.message)
    } finally {
      setIsLoading(false)
    }
  }

  const copyToClipboard = async (text, itemType) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedItem(itemType)
      setTimeout(() => setCopiedItem(null), 2000)
    } catch (error) {
      console.error('Failed to copy:', error)
    }
  }

  const refreshContent = () => {
    generateYouTubeContent()
  }

  const changeLanguage = async (newLanguage) => {
    if (newLanguage === currentLanguage) return

    setIsTranslating(true)
    setError(null)

    try {
      setCurrentLanguage(newLanguage)

      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL 
      const response = await fetch(`${apiBaseUrl}/generate-youtube-content`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          person_name: personName,
          language: newLanguage,
          story_data: storyData
        })
      })

      const result = await response.json()

      if (result.success) {
        actions.setYoutubeContent(result)
      } else {
        throw new Error(result.error || 'Failed to generate YouTube content')
      }
    } catch (error) {
      console.error('Error changing language:', error)
      setError(error.message)
      setCurrentLanguage(language) // Revert to original language
    } finally {
      setIsTranslating(false)
    }
  }

  if (!personName) return null

  return (
    <div className="glass-card p-8">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-white text-2xl font-bold flex items-center gap-2">
          📺 YouTube Content
          <span className="text-sm font-normal text-blue-300">
            ({currentLanguage === 'hindi' ? 'Hindi' : 'English'})
          </span>
        </h3>
        <div className="flex items-center gap-3">
          {/* Language Toggle */}
          {youtubeContent && (
            <div className="flex items-center gap-2">
              <span className="text-sm text-blue-300">Language:</span>
              <div className="flex bg-white/10 rounded-lg p-1">
                <button
                  onClick={() => changeLanguage('english')}
                  disabled={isTranslating}
                  className={`px-3 py-1 text-xs font-medium rounded transition-all duration-200 ${
                    currentLanguage === 'english'
                      ? 'bg-blue-500 text-white'
                      : 'text-blue-300 hover:text-white hover:bg-white/10'
                  } ${isTranslating ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                >
                  🇺🇸 EN
                </button>
                <button
                  onClick={() => changeLanguage('hindi')}
                  disabled={isTranslating}
                  className={`px-3 py-1 text-xs font-medium rounded transition-all duration-200 ${
                    currentLanguage === 'hindi'
                      ? 'bg-blue-500 text-white'
                      : 'text-blue-300 hover:text-white hover:bg-white/10'
                  } ${isTranslating ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                >
                  🇮🇳 HI
                </button>
              </div>
            </div>
          )}

          <button
            onClick={refreshContent}
            disabled={isLoading || isTranslating}
            className={`p-2 rounded-full transition-all duration-200 ${
              isLoading || isTranslating
                ? 'bg-blue-500/30 text-blue-300 animate-spin'
                : 'bg-blue-500/20 text-blue-300 hover:bg-blue-500/40 hover:text-white'
            } ${isLoading || isTranslating ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
            title="Generate new content"
          >
            {isLoading || isTranslating ? '⟳' : '🔄'}
          </button>
        </div>
      </div>

      {(isLoading || isTranslating) && (
        <div className="flex items-center justify-center py-8">
          <div className="flex items-center gap-3 text-blue-300">
            <div className="loading-spinner w-6 h-6"></div>
            <span>
              {isTranslating
                ? `Translating to ${currentLanguage === 'hindi' ? 'Hindi' : 'English'}...`
                : 'Generating YouTube content...'
              }
            </span>
          </div>
        </div>
      )}

      {error && (
        <div className="p-4 bg-red-500/20 border border-red-500/30 rounded-lg text-red-300 mb-6">
          <p>❌ Error: {error}</p>
          <button 
            onClick={refreshContent}
            className="mt-2 text-sm underline hover:no-underline"
          >
            Try again
          </button>
        </div>
      )}

      {youtubeContent && !isLoading && !isTranslating && (
        <div className="space-y-6">
          {/* Title Options */}
          <div>
            <h4 className="text-white text-lg font-semibold mb-4 flex items-center gap-2">
              🎬 Title Options
              <span className="text-xs text-blue-300">(Choose your favorite)</span>
            </h4>
            <div className="space-y-3">
              {youtubeContent.titles.map((title, index) => (
                <div key={index} className="relative group">
                  <div className="p-4 bg-white/10 rounded-lg border border-white/20 hover:border-blue-400/50 transition-all duration-200">
                    <div className="flex items-start justify-between gap-3">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="bg-blue-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                            Option {index + 1}
                          </span>
                        </div>
                        <p className="text-white leading-relaxed break-words">
                          {title}
                        </p>
                      </div>
                      <button
                        onClick={() => copyToClipboard(title, `title-${index}`)}
                        className={`p-2 rounded-lg transition-all duration-200 ${
                          copiedItem === `title-${index}`
                            ? 'bg-green-500/30 text-green-300'
                            : 'bg-blue-500/20 text-blue-300 hover:bg-blue-500/40 hover:text-white'
                        }`}
                        title="Copy title"
                      >
                        {copiedItem === `title-${index}` ? '✅' : '📋'}
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Description */}
          <div>
            <h4 className="text-white text-lg font-semibold mb-4 flex items-center gap-2">
              📝 Description
            </h4>
            <div className="relative group">
              <div className="p-4 bg-white/10 rounded-lg border border-white/20 hover:border-blue-400/50 transition-all duration-200">
                <div className="flex items-start justify-between gap-3">
                  <div className="flex-1">
                    <p className="text-white leading-relaxed whitespace-pre-line break-words">
                      {youtubeContent.description}
                    </p>
                  </div>
                  <button
                    onClick={() => copyToClipboard(youtubeContent.description, 'description')}
                    className={`p-2 rounded-lg transition-all duration-200 ${
                      copiedItem === 'description'
                        ? 'bg-green-500/30 text-green-300'
                        : 'bg-blue-500/20 text-blue-300 hover:bg-blue-500/40 hover:text-white'
                    }`}
                    title="Copy description"
                  >
                    {copiedItem === 'description' ? '✅' : '📋'}
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Copy All Button */}
          <div className="pt-4 border-t border-white/20">
            <button
              onClick={() => {
                const allContent = `TITLES:\n\n${youtubeContent.titles.map((title, i) => `${i + 1}. ${title}`).join('\n\n')}\n\nDESCRIPTION:\n\n${youtubeContent.description}`
                copyToClipboard(allContent, 'all')
              }}
              className={`w-full p-3 rounded-lg transition-all duration-200 ${
                copiedItem === 'all'
                  ? 'bg-green-500/30 text-green-300 border-green-500/30'
                  : 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-white hover:from-purple-500/30 hover:to-pink-500/30 border-purple-500/30'
              } border`}
            >
              {copiedItem === 'all' ? '✅ All Content Copied!' : '📋 Copy All Content'}
            </button>
          </div>

          {/* Generation Info */}
          <div className="text-xs text-blue-400 text-center">
            Generated using AI • {youtubeContent.source === 'gemini_ai' ? 'Powered by Gemini' : 'Fallback content'}
          </div>
        </div>
      )}
    </div>
  )
}

export default YouTubeContentSection
