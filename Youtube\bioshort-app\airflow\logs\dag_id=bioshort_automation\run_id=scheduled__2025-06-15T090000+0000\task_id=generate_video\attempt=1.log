[2025-06-16T19:12:37.384+0000] {taskinstance.py:1159} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: bioshort_automation.generate_video scheduled__2025-06-15T09:00:00+00:00 [queued]>
[2025-06-16T19:12:37.398+0000] {taskinstance.py:1159} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: bioshort_automation.generate_video scheduled__2025-06-15T09:00:00+00:00 [queued]>
[2025-06-16T19:12:37.399+0000] {taskinstance.py:1361} INFO - Starting attempt 1 of 2
[2025-06-16T19:12:37.418+0000] {taskinstance.py:1382} INFO - Executing <Task(PythonOperator): generate_video> on 2025-06-15 09:00:00+00:00
[2025-06-16T19:12:37.425+0000] {standard_task_runner.py:57} INFO - Started process 207 to run task
[2025-06-16T19:12:37.431+0000] {standard_task_runner.py:84} INFO - Running: ['***', 'tasks', 'run', 'bioshort_automation', 'generate_video', 'scheduled__2025-06-15T09:00:00+00:00', '--job-id', '16', '--raw', '--subdir', 'DAGS_FOLDER/bioshort_automation.py', '--cfg-path', '/tmp/tmp020atu31']
[2025-06-16T19:12:37.437+0000] {standard_task_runner.py:85} INFO - Job 16: Subtask generate_video
[2025-06-16T19:12:37.575+0000] {task_command.py:415} INFO - Running <TaskInstance: bioshort_automation.generate_video scheduled__2025-06-15T09:00:00+00:00 [running]> on host cdc7242b3dcf
[2025-06-16T19:12:37.705+0000] {taskinstance.py:1660} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='bioshort' AIRFLOW_CTX_DAG_ID='bioshort_automation' AIRFLOW_CTX_TASK_ID='generate_video' AIRFLOW_CTX_EXECUTION_DATE='2025-06-15T09:00:00+00:00' AIRFLOW_CTX_TRY_NUMBER='1' AIRFLOW_CTX_DAG_RUN_ID='scheduled__2025-06-15T09:00:00+00:00'
[2025-06-16T19:12:37.727+0000] {logging_mixin.py:151} INFO - ❌ Error in video generation: No person data found
[2025-06-16T19:12:37.730+0000] {taskinstance.py:1943} ERROR - Task failed with exception
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.8/site-packages/airflow/operators/python.py", line 192, in execute
    return_value = self.execute_callable()
  File "/home/<USER>/.local/lib/python3.8/site-packages/airflow/operators/python.py", line 209, in execute_callable
    return self.python_callable(*self.op_args, **self.op_kwargs)
  File "/opt/airflow/dags/bioshort_automation.py", line 70, in generate_video_for_person
    raise ValueError("No person data found")
ValueError: No person data found
[2025-06-16T19:12:37.756+0000] {taskinstance.py:1400} INFO - Marking task as UP_FOR_RETRY. dag_id=bioshort_automation, task_id=generate_video, execution_date=20250615T090000, start_date=20250616T191237, end_date=20250616T191237
[2025-06-16T19:12:37.777+0000] {standard_task_runner.py:104} ERROR - Failed to execute job 16 for task generate_video (No person data found; 207)
[2025-06-16T19:12:37.813+0000] {local_task_job_runner.py:228} INFO - Task exited with return code 1
[2025-06-16T19:12:37.849+0000] {taskinstance.py:2784} INFO - 0 downstream tasks scheduled from follow-on schedule check
