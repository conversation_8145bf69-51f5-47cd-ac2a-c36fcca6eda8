import React, { useState } from 'react'
import { env, getAllEnvVars } from '../config/env.js'

const EnvDebug = () => {
  const [showDebug, setShowDebug] = useState(false)
  const [allVars, setAllVars] = useState(null)

  const handleShowDebug = () => {
    setAllVars(getAllEnvVars())
    setShowDebug(!showDebug)
  }

  // Only show in development
  if (env.PROD) {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={handleShowDebug}
        className="bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/50 text-blue-300 hover:text-blue-200 px-3 py-2 rounded-lg text-xs font-medium transition-all duration-300"
        title="Debug Environment Variables"
      >
        🔧 ENV
      </button>

      {showDebug && (
        <div className="absolute bottom-12 right-0 w-96 max-h-96 overflow-auto bg-slate-800/95 backdrop-blur-xl border border-slate-600/50 rounded-xl p-4 shadow-2xl">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-white font-semibold text-sm">Environment Variables</h3>
            <button
              onClick={() => setShowDebug(false)}
              className="text-slate-400 hover:text-white text-xs"
            >
              ✕
            </button>
          </div>

          <div className="space-y-3">
            <div>
              <h4 className="text-cyan-300 text-xs font-medium mb-2">Current Config:</h4>
              <div className="space-y-1 text-xs">
                <div className="flex justify-between">
                  <span className="text-slate-300">API_BASE_URL:</span>
                  <span className="text-green-300 font-mono">{env.API_BASE_URL}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-300">NODE_ENV:</span>
                  <span className="text-green-300 font-mono">{env.NODE_ENV}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-300">DEBUG:</span>
                  <span className="text-green-300 font-mono">{env.DEBUG.toString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-300">DEV:</span>
                  <span className="text-green-300 font-mono">{env.DEV.toString()}</span>
                </div>
              </div>
            </div>

            {allVars && (
              <div>
                <h4 className="text-cyan-300 text-xs font-medium mb-2">All Environment Variables:</h4>
                <div className="space-y-1 text-xs max-h-48 overflow-auto">
                  {Object.entries(allVars).map(([key, value]) => (
                    <div key={key} className="flex justify-between">
                      <span className="text-slate-300 truncate">{key}:</span>
                      <span className="text-green-300 font-mono truncate ml-2" title={value}>
                        {value || 'undefined'}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="pt-2 border-t border-slate-600/50">
              <p className="text-slate-400 text-xs">
                💡 This debug panel only shows in development mode
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default EnvDebug
