# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
*.log

# Environment files
.env.local
.env.development.local
.env.test.local
.env.production.local

# Git
.git/
.gitignore

# Docker
Dockerfile
.dockerignore

# ESLint cache
.eslintcache

# Coverage reports
coverage/

# Temporary files
.tmp/
temp/
