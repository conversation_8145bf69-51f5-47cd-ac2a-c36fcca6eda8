import React from 'react'

const Header = () => {
  return (
    <header className="text-center mb-12 fade-in">
      <div className="bg-slate-800/90 backdrop-blur-xl rounded-3xl shadow-2xl border border-slate-700/50 p-8 max-w-4xl mx-auto relative overflow-hidden">
        {/* Digital grid background effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(59,130,246,0.1),transparent_50%)]"></div>

        <div className="relative z-10">
          <div className="flex items-center justify-center gap-4 mb-4">
      <span className="text-4xl md:text-5xl drop-shadow-lg">🎬</span>

            <h1 className="text-5xl md:text-6xl font-bold">
              <span className="bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 bg-clip-text text-transparent">
                BioShort
              </span>
            </h1>
          </div>
          <p className="text-xl text-slate-300 mb-6">
            Transform any famous person into an inspiring 1-minute AI video
          </p>
          <div className="flex flex-wrap justify-center gap-4 text-sm">
            <div className="flex items-center gap-2 bg-emerald-500/20 border border-emerald-500/30 px-4 py-2 rounded-full backdrop-blur-sm">
              <span className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse shadow-lg shadow-emerald-400/50"></span>
              <span className="text-emerald-300 font-medium">AI Story Generation</span>
            </div>
            <div className="flex items-center gap-2 bg-blue-500/20 border border-blue-500/30 px-4 py-2 rounded-full backdrop-blur-sm">
              <span className="w-2 h-2 bg-blue-400 rounded-full animate-pulse shadow-lg shadow-blue-400/50"></span>
              <span className="text-blue-300 font-medium">Animated Images</span>
            </div>
            <div className="flex items-center gap-2 bg-purple-500/20 border border-purple-500/30 px-4 py-2 rounded-full backdrop-blur-sm">
              <span className="w-2 h-2 bg-purple-400 rounded-full animate-pulse shadow-lg shadow-purple-400/50"></span>
              <span className="text-purple-300 font-medium">Natural Voice</span>
            </div>
            <div className="flex items-center gap-2 bg-orange-500/20 border border-orange-500/30 px-4 py-2 rounded-full backdrop-blur-sm">
              <span className="w-2 h-2 bg-orange-400 rounded-full animate-pulse shadow-lg shadow-orange-400/50"></span>
              <span className="text-orange-300 font-medium">YouTube Shorts Ready</span>
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header
