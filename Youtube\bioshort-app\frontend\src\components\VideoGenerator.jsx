import React, { useState } from 'react'
import InputForm from './InputForm'
import LoadingSection from './LoadingSection'
import ResultsSection from './ResultsSection'
import ErrorSection from './ErrorSection'
import { generateVideo } from '../services/api'

const VideoGenerator = () => {
  const [isLoading, setIsLoading] = useState(false)
  const [results, setResults] = useState(null)
  const [error, setError] = useState(null)
  const [progress, setProgress] = useState({ percentage: 0, text: '' })

  const handleGenerate = async (requestData) => {
    try {
      setIsLoading(true)
      setError(null)
      setResults(null)

      // Extract person name for display
      const personName = typeof requestData === 'string' ? requestData : requestData.person_name

      // Simulate progress updates with voice info
      const voiceInfo = typeof requestData === 'object' ? requestData.voice_type : 'default'
      const languageInfo = typeof requestData === 'object' ? requestData.language : 'english'

      setProgress({ percentage: 0, text: `Generating inspiring story for ${personName}...` })

      setTimeout(() => setProgress({ percentage: 25, text: 'Creating AI photorealistic images...' }), 2000)
      setTimeout(() => setProgress({
        percentage: 50,
        text: `Synthesizing ${languageInfo} voiceover (${voiceInfo})...`
      }), 4000)
      setTimeout(() => setProgress({ percentage: 75, text: 'Adding subtitles and compiling video...' }), 6000)

      // Call API with voice parameters
      const result = await generateVideo(requestData)
      
      if (result.success) {
        setProgress({ percentage: 100, text: 'Video generated successfully!' })
        setTimeout(() => {
          setResults(result)
          setIsLoading(false)
        }, 1000)
      } else {
        throw new Error(result.error || 'Video generation failed')
      }
      
    } catch (err) {
      console.error('Generation error:', err)
      setError(err.message)
      setIsLoading(false)
    }
  }

  const handleRetry = () => {
    setError(null)
    setResults(null)
  }

  return (
    <main className="max-w-6xl mx-auto">
      {/* Input Form */}
      <InputForm 
        onGenerate={handleGenerate} 
        isLoading={isLoading}
      />
      
      {/* Loading Section */}
      {isLoading && (
        <LoadingSection 
          progress={progress.percentage}
          text={progress.text}
        />
      )}
      
      {/* Error Section */}
      {error && (
        <ErrorSection 
          message={error}
          onRetry={handleRetry}
        />
      )}
      
      {/* Results Section */}
      {results && !isLoading && (
        <ResultsSection 
          data={results}
        />
      )}
    </main>
  )
}

export default VideoGenerator
