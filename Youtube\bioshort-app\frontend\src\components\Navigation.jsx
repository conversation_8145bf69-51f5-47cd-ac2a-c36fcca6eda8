import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import { useVideoGeneration } from '../context/VideoGenerationContext'

const Navigation = () => {
  const location = useLocation()
  const { state } = useVideoGeneration()

  const navItems = [
    {
      path: '/',
      label: 'Video Generator',
      icon: '🎬',
      badge: state.isGenerating ? `${Math.round(state.progress)}%` : null,
      badgeColor: 'bg-green-500'
    },
    { path: '/automation', label: 'Automation Queue', icon: '🤖' },
  ]

  return (
    <nav className="mb-8">
      <div className="bg-slate-800/90 backdrop-blur-xl rounded-2xl shadow-2xl border border-slate-700/50 p-4 max-w-4xl mx-auto">
        <div className="flex flex-wrap justify-center gap-4">
          {navItems.map((item) => (
            <Link
              key={item.path}
              to={item.path}
              className={`
                relative flex items-center gap-3 px-6 py-3 rounded-xl font-medium transition-all duration-300
                ${location.pathname === item.path
                  ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg shadow-blue-500/25'
                  : 'bg-slate-700/50 text-slate-300 hover:bg-slate-600/50 hover:text-white'
                }
              `}
            >
              <span className="text-xl">{item.icon}</span>
              <span>{item.label}</span>
              {item.badge && (
                <span className={`
                  absolute -top-2 -right-2 px-2 py-1 text-xs font-bold text-white rounded-full
                  ${item.badgeColor} animate-pulse shadow-lg
                `}>
                  {item.badge}
                </span>
              )}
            </Link>
          ))}
        </div>
      </div>
    </nav>
  )
}

export default Navigation
