# Frontend Dockerfile for BioShort React App
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files first for better Docker layer caching
COPY package*.json ./

# Install dependencies (including dev dependencies for Vite)
RUN npm install

# Copy application code
COPY . .

# Expose port 5173 (Vite default)
EXPOSE 5173

# Set environment variables
ENV NODE_ENV=development

# Run the development server with host binding for Docker
CMD ["npm", "run", "dev"]
