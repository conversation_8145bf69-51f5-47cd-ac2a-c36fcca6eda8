import React, { useState } from 'react'
import { API_BASE_URL } from '../services/api'
import YouTubeContentSection from './YouTubeContentSection'

const ResultsSection = ({ data }) => {
  const [selectedImage, setSelectedImage] = useState(null)

  const openImageModal = (image, index) => {
    setSelectedImage({ image, index })
  }

  const closeImageModal = () => {
    setSelectedImage(null)
  }

  const downloadVideo = () => {
    if (data.video_filename) {
      window.open(`${API_BASE_URL}/download-video/${data.video_filename}`, '_blank')
    }
  }

  const downloadAudio = () => {
    if (data.audio && data.audio.base64) {
      // Create download link from base64 data
      const audioBlob = new Blob([Uint8Array.from(atob(data.audio.base64), c => c.charCodeAt(0))], { type: 'audio/mpeg' })
      const url = URL.createObjectURL(audioBlob)
      const a = document.createElement('a')
      a.href = url
      a.download = data.audio.filename || 'audio.mp3'
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    }
  }

  return (
    <div className="space-y-8 fade-in">
      {/* Success Message */}
      <div className="glass-card p-6 text-center">
        <div className="text-4xl mb-2">🎉</div>
        <h2 className="text-2xl font-bold text-white mb-2">
          Video Generated Successfully!
        </h2>
        <p className="text-blue-200">
          Your AI-powered biography video for <span className="font-semibold text-white">{data.person_name}</span> is ready
        </p>
      </div>

      {/* Generated Images Preview */}
      {data.images && data.images.length > 0 && (
        <div className="glass-card p-8">
          <h3 className="text-white text-2xl font-bold mb-6 flex items-center gap-2">
            🎨 Generated Images
            <span className="text-sm font-normal text-blue-300">({data.images.length} scenes)</span>
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {data.images.map((image, index) => (
              <div
                key={image.filename}
                className="relative group cursor-pointer"
                onClick={() => openImageModal(image, index)}
              >
                <img
                  src={`data:image/png;base64,${image.base64}`}
                  alt={`Generated Scene ${index + 1}`}
                  className="w-full h-32 object-cover rounded-lg border-2 border-white/20 hover:border-blue-400 transition-all duration-300 transform hover:scale-105"
                />
                <div className="absolute bottom-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                  Scene {index + 1}
                </div>
                <div className="absolute inset-0 bg-blue-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg flex items-center justify-center">
                  <span className="text-white font-semibold">🔍 View</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Storage Optimization Notice */}
      <div className="glass-card p-6">
        <div className="flex items-center gap-3 text-green-400 mb-2">
          <span className="text-2xl">💾</span>
          <h3 className="text-lg font-semibold">Zero Storage Footprint</h3>
        </div>
        <p className="text-blue-200 text-sm">
          Images and audio are served directly from memory. No intermediate files are stored on disk - only the final video is saved.
        </p>
        {data.note && (
          <p className="text-blue-300 text-xs mt-2 italic">
            {data.note}
          </p>
        )}
      </div>

      {/* Story Preview */}
      {data.story && data.story.segments && (
        <div className="glass-card p-8">
          <h3 className="text-white text-2xl font-bold mb-6 flex items-center gap-2">
            📖 Generated Story
            <span className="text-sm font-normal text-blue-300">({data.story.segments.length} segments)</span>
          </h3>
          <div className="space-y-4">
            {data.story.segments.map((segment, index) => (
              <div key={index} className="p-4 bg-white/10 rounded-lg border border-white/20">
                <div className="flex items-start gap-3">
                  <span className="bg-blue-500 text-white text-xs font-bold px-2 py-1 rounded-full min-w-[2rem] text-center">
                    {index + 1}
                  </span>
                  <div className="flex-1">
                    <p className="text-white mb-2 leading-relaxed">
                      {segment.text}
                    </p>
                    <p className="text-blue-300 text-sm italic">
                      Scene: {segment.scene_description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Video Player */}
      {data.video_filename && (
        <div className="glass-card p-8">
          <h3 className="text-white text-2xl font-bold mb-6">🎥 Your Video</h3>
          <div className="aspect-w-9 aspect-h-16 bg-black rounded-lg overflow-hidden max-w-md mx-auto">
            <video
              controls
              className="w-full h-full object-cover"
              style={{ maxHeight: '600px' }}
              src={`${API_BASE_URL}/download-video/${data.video_filename}`}
            >
              Your browser does not support the video tag.
            </video>
          </div>
        </div>
      )}

      {/* YouTube Content Section */}
      <YouTubeContentSection
        personName={data.person_name}
        language={data.language || 'english'}
      />

      {/* Download Section */}
      <div className="glass-card p-8">
        <h3 className="text-white text-2xl font-bold mb-6">📥 Download</h3>
        <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
          <button
            onClick={downloadVideo}
            className="flex-1 btn-primary bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700"
          >
            📱 Download Video (MP4)
          </button>
          <button
            onClick={downloadAudio}
            className="flex-1 btn-primary bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700"
          >
            🎵 Download Audio (MP3)
          </button>
        </div>
        
        {/* Stats */}
        {data.stats && (
          <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div className="p-3 bg-white/10 rounded-lg">
              <div className="text-blue-400 text-sm">Segments</div>
              <div className="text-white font-bold">{data.stats.segments_count}</div>
            </div>
            <div className="p-3 bg-white/10 rounded-lg">
              <div className="text-blue-400 text-sm">Images</div>
              <div className="text-white font-bold">{data.stats.images_count}</div>
            </div>
            <div className="p-3 bg-white/10 rounded-lg">
              <div className="text-blue-400 text-sm">Duration</div>
              <div className="text-white font-bold">{data.stats.video_duration || 'N/A'}</div>
            </div>
            <div className="p-3 bg-white/10 rounded-lg">
              <div className="text-blue-400 text-sm">Format</div>
              <div className="text-white font-bold">MP4</div>
            </div>
          </div>
        )}
      </div>

      {/* Image Modal */}
      {selectedImage && (
        <div
          className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4"
          onClick={closeImageModal}
        >
          <div className="relative max-w-4xl max-h-full">
            <img
              src={`data:image/png;base64,${selectedImage.image.base64}`}
              alt={`Generated Image ${selectedImage.index + 1}`}
              className="max-w-full max-h-full object-contain rounded-lg"
            />
            <button
              onClick={closeImageModal}
              className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-red-600 transition-colors"
            >
              ×
            </button>
            <div className="absolute bottom-2 left-2 bg-black/70 text-white px-3 py-2 rounded">
              Generated Image {selectedImage.index + 1} - Click outside to close
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ResultsSection
