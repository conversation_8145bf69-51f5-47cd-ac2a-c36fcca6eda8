# 🎬 BioShort - AI Biography Video Generator

A comprehensive AI-powered application that automatically generates short biography videos with multi-language support, voice synthesis, and automated YouTube uploads with scheduling capabilities.

## 📋 Table of Contents

- [🎯 Overview](#-overview)
- [🏗️ Architecture](#️-architecture)
- [📁 Project Structure](#-project-structure)
- [🔧 Backend Deep Dive](#-backend-deep-dive)
- [🎨 Frontend Overview](#-frontend-overview)
- [🤖 Airflow Automation](#-airflow-automation)
- [🗄️ Database Schema](#️-database-schema)
- [🚀 Setup & Installation](#-setup--installation)
- [📊 API Documentation](#-api-documentation)
- [🔧 Configuration](#-configuration)
- [🎯 Usage Guide](#-usage-guide)
- [🛠️ Troubleshooting](#️-troubleshooting)

## 🎯 Overview

BioShort is a full-stack application that combines AI content generation, multimedia processing, and automation to create engaging biography videos. The system supports:

- **AI Story Generation**: Using Google Gemini API for creative biography content
- **Multi-language Support**: English and Hindi with independent language settings
- **Voice Synthesis**: Multiple voice options using edge-tts and pyttsx3
- **Image Generation**: AI-powered scene images via Gemini API
- **Video Compilation**: Automated video creation with subtitles and audio sync
- **YouTube Integration**: Direct upload with metadata generation
- **Automation System**: Apache Airflow for scheduled daily processing
- **Queue Management**: Web-based interface for managing automation queue

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   Airflow       │
│   (React/Vite)  │◄──►│   (Flask API)   │◄──►│  (Scheduler)    │
│   Port: 5173    │    │   Port: 5000    │    │   Port: 8080    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   User Queue    │    │   File System   │    │  PostgreSQL     │
│   (SQLite)      │    │   (Temp Files)  │    │  (Airflow DB)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Technology Stack:
- **Frontend**: React 18, Vite, TailwindCSS
- **Backend**: Python Flask, SQLite
- **Automation**: Apache Airflow 2.7.0
- **Database**: SQLite (app data), PostgreSQL (Airflow)
- **AI Services**: Google Gemini API
- **Voice**: edge-tts, pyttsx3
- **Video**: moviepy, PIL
- **Containerization**: Docker, Docker Compose

## 📁 Project Structure

```
bioshort-app/
├── 📁 backend/                 # Python Flask API server
│   ├── 📄 app.py              # Main Flask application
│   ├── 📄 database.py         # SQLite database management
│   ├── 📄 Dockerfile          # Backend container configuration
│   ├── 📄 requirements.txt    # Python dependencies
│   └── 📁 services/           # Core business logic modules
│       ├── 📄 story_generator.py      # AI story generation
│       ├── 📄 image_generator.py      # AI image generation
│       ├── 📄 voice_generator.py      # Text-to-speech synthesis
│       ├── 📄 video_compiler.py       # Video compilation
│       ├── 📄 youtube_content_generator.py  # YouTube metadata
│       └── 📄 youtube_uploader.py     # YouTube API integration
├── 📁 frontend/               # React web application
│   ├── 📄 package.json       # Node.js dependencies
│   ├── 📄 vite.config.js     # Vite build configuration
│   ├── 📄 Dockerfile         # Frontend container configuration
│   └── 📁 src/               # React source code
│       ├── 📄 App.jsx        # Main application component
│       └── 📁 components/    # React components
├── 📁 airflow/               # Apache Airflow automation
│   ├── 📁 dags/              # Airflow DAG definitions
│   │   ├── 📄 bioshort_automation.py    # Main automation workflow
│   │   ├── 📄 database_cleanup.py       # Database cleanup script
│   │   └── 📄 deep_database_cleanup.py  # Advanced cleanup script
│   ├── 📁 logs/              # Airflow execution logs (auto-generated)
│   └── 📁 plugins/           # Airflow plugins (empty)
├── 📄 docker-compose.yml     # Multi-container orchestration
├── 📄 .env                   # Environment variables (create from .env.example)
├── 📄 .gitignore            # Git ignore patterns
├── 📄 docker-run.sh         # Docker startup script
└── 📄 README.md             # This comprehensive documentation
```

## 🔧 Backend Deep Dive

### 📄 `backend/app.py` - Main Flask Application

**Purpose**: Central Flask application that orchestrates all services and provides REST API endpoints.

**Key Components**:
- **Flask App Configuration**: CORS enabled, JSON error handling
- **Service Initialization**: Instantiates all service classes
- **Environment Management**: Loads API keys and configuration
- **Health Monitoring**: Provides health check endpoints
- **Error Handling**: Comprehensive error responses with logging

**Main API Endpoints**:
```python
# Core Generation Endpoints
POST /api/generate-video          # Complete video generation pipeline
POST /api/generate-story          # Story generation only
POST /api/generate-images         # Image generation only
POST /api/generate-voice          # Voice synthesis only
POST /api/compile-video           # Video compilation only

# YouTube Integration
POST /api/generate-youtube-content # Generate titles/descriptions
POST /api/upload-to-youtube       # Upload video to YouTube
GET  /api/youtube-quota-info      # Check YouTube API quota

# Automation Queue Management
GET  /api/queue                   # Get automation queue
POST /api/queue/add               # Add person to queue
DELETE /api/queue/remove/<id>     # Remove person from queue
GET  /api/queue/next              # Get next person to process
POST /api/queue/update-status     # Update processing status

# Utility Endpoints
GET  /api/health                  # Health check
GET  /api/voices                  # Available voice options
POST /api/voice-sample            # Generate voice preview
GET  /api/generate-random-names   # AI-generated random names
```

**Service Integration Flow**:
1. **Request Validation**: Validates input parameters
2. **Service Orchestration**: Calls appropriate service methods
3. **Error Handling**: Catches and formats service errors
4. **Response Formatting**: Returns consistent JSON responses
5. **Cleanup**: Manages temporary file cleanup

### 📄 `backend/database.py` - SQLite Database Management

**Purpose**: Handles all database operations for the automation queue system.

**Database Schema**:
```sql
-- People Queue Table
CREATE TABLE people_queue (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,                    -- Person's name
    title_language TEXT DEFAULT 'english', -- YouTube title language
    description_language TEXT DEFAULT 'english', -- YouTube description language
    audio_language TEXT DEFAULT 'hindi',   -- Voice/story language
    voice_type TEXT DEFAULT 'hindi_male',  -- Specific voice type
    status TEXT DEFAULT 'pending',         -- pending/processing/completed/failed
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,           -- When processing finished
    video_url TEXT NULL,                   -- YouTube URL after upload
    error_message TEXT NULL                -- Error details if failed
);

-- Processing Log Table (for debugging)
CREATE TABLE processing_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    person_name TEXT NOT NULL,
    status TEXT NOT NULL,
    message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Key Methods**:
- `add_person_to_queue()`: Adds person with language preferences
- `get_queue()`: Retrieves all queue entries with status
- `get_next_pending_person()`: Gets next person for processing
- `update_person_status()`: Updates status and results
- `remove_person_from_queue()`: Removes person from queue

**Database Features**:
- **Duplicate Prevention**: Prevents adding same person twice
- **Status Tracking**: Tracks processing lifecycle
- **Error Logging**: Stores detailed error messages
- **Timestamp Management**: Tracks creation and processing times

### 📄 `backend/services/story_generator.py` - AI Story Generation

**Purpose**: Generates biographical stories using Google Gemini API with multi-language support.

**Key Features**:
- **AI-Powered Content**: Uses Gemini Pro for creative story generation
- **Multi-language Support**: Generates content in English or Hindi
- **Structured Output**: Returns JSON with scenes, narration, and metadata
- **Fallback Handling**: Provides backup stories if AI generation fails
- **Content Validation**: Ensures generated content meets requirements

**Core Methods**:
```python
generate_story(person_name, language='hindi')
# Returns: {
#   'success': bool,
#   'story': {
#     'scenes': [list of scene descriptions],
#     'narration': 'full narration text',
#     'title': 'generated title',
#     'duration': estimated_duration
#   }
# }
```

**Story Structure**:
- **Scene-based**: Breaks story into visual scenes
- **Narration**: Continuous narrative for voice synthesis
- **Metadata**: Title, duration, and content tags
- **Language-specific**: Adapts content style per language

### 📄 `backend/services/image_generator.py` - AI Image Generation

**Purpose**: Creates scene images using Google Gemini Vision API with prompt engineering.

**Key Features**:
- **Scene Visualization**: Converts story scenes into visual prompts
- **AI Image Generation**: Uses Gemini Vision for high-quality images
- **Prompt Engineering**: Optimized prompts for biographical content
- **Error Recovery**: Retry logic with alternative prompts
- **Image Processing**: Resizes and optimizes images for video

**Core Methods**:
```python
generate_images(story_data, person_name)
# Returns: {
#   'success': bool,
#   'images': [list of base64 encoded images],
#   'scene_count': int,
#   'failed_scenes': [list of failed scene indices]
# }
```

**Image Generation Process**:
1. **Prompt Creation**: Converts scenes to detailed visual prompts
2. **API Calls**: Sends requests to Gemini Vision API
3. **Error Handling**: Retries failed generations with modified prompts
4. **Image Processing**: Converts, resizes, and encodes images
5. **Fallback Strategy**: Uses placeholder images for failed generations

### 📄 `backend/services/voice_generator.py` - Text-to-Speech Synthesis

**Purpose**: Converts text to speech with multiple voice options and cross-platform compatibility.

**Key Features**:
- **Multi-platform TTS**: Uses edge-tts (cross-platform) and pyttsx3 (Windows)
- **Voice Options**: Hindi male/female, English male/female
- **Docker Compatibility**: Prioritizes edge-tts for containerized environments
- **Audio Processing**: Generates MP3 files with proper duration
- **Voice Samples**: Provides preview functionality

**Voice Configuration**:
```python
VOICE_MAPPING = {
    'hindi_male': 'hi-IN-MadhurNeural',
    'hindi_female': 'hi-IN-SwaraNeural',
    'english_male': 'en-US-BrianNeural',
    'english_female': 'en-US-JennyNeural'
}
```

**Core Methods**:
```python
generate_voice(script, voice_type, language, duration)
# Returns: {
#   'success': bool,
#   'audio_file': 'path/to/audio.mp3',
#   'duration': actual_duration,
#   'voice_used': 'voice_engine_name'
# }

generate_voice_sample(voice_type, sample_text)
# Returns: base64 encoded audio sample
```

**TTS Engine Priority**:
1. **edge-tts**: Primary engine (Docker compatible)
2. **pyttsx3**: Fallback engine (Windows native)
3. **Error Handling**: Graceful degradation with detailed logging

### 📄 `backend/services/video_compiler.py` - Video Compilation

**Purpose**: Combines images, audio, and subtitles into final MP4 video using moviepy.

**Key Features**:
- **Multi-media Compilation**: Combines images, audio, and text
- **Subtitle Generation**: Creates synchronized subtitles
- **Video Optimization**: Optimized for YouTube Shorts (9:16 aspect ratio)
- **Audio Synchronization**: Ensures perfect audio-video sync
- **Memory Management**: Efficient processing of large media files

**Core Methods**:
```python
compile_video(images, audio_file, story_data, person_name)
# Returns: {
#   'success': bool,
#   'video_file': 'path/to/video.mp4',
#   'duration': video_duration,
#   'size_mb': file_size,
#   'subtitle_file': 'path/to/subtitles.srt'
# }
```

**Video Specifications**:
- **Resolution**: 1080x1920 (9:16 aspect ratio)
- **Frame Rate**: 30 FPS
- **Audio**: 44.1kHz, stereo
- **Format**: MP4 with H.264 encoding
- **Duration**: Matches audio duration

**Compilation Process**:
1. **Image Processing**: Resizes and positions images
2. **Audio Integration**: Syncs audio with visual timeline
3. **Subtitle Creation**: Generates SRT file with timestamps
4. **Video Rendering**: Combines all elements into MP4
5. **Optimization**: Compresses for web delivery

### 📄 `backend/services/youtube_content_generator.py` - YouTube Metadata

**Purpose**: Generates YouTube-optimized titles and descriptions using AI.

**Key Features**:
- **SEO Optimization**: Creates YouTube-friendly titles and descriptions
- **Multi-language Support**: Generates content in English or Hindi
- **Multiple Options**: Provides several title variations
- **Hashtag Integration**: Includes relevant hashtags and emojis
- **Content Adaptation**: Adapts style based on story content

**Core Methods**:
```python
generate_youtube_content(person_name, language, story_data)
# Returns: {
#   'success': bool,
#   'titles': [list of title options],
#   'description': 'optimized description',
#   'tags': [list of relevant tags],
#   'language': 'content language'
# }
```

**Content Features**:
- **Dynamic Titles**: Generated from actual story content
- **Rich Descriptions**: Detailed, engaging descriptions
- **SEO Tags**: Relevant keywords and hashtags
- **Emoji Integration**: Platform-appropriate emoji usage
- **Language-specific**: Culturally appropriate content

### 📄 `backend/services/youtube_uploader.py` - YouTube API Integration

**Purpose**: Handles YouTube API authentication and video uploads with comprehensive error handling.

**Key Features**:
- **OAuth2 Authentication**: Secure YouTube API access
- **Video Upload**: Direct upload with metadata
- **Quota Management**: Monitors API usage limits
- **Error Recovery**: Handles upload failures gracefully
- **Privacy Controls**: Supports private/public upload options

**Core Methods**:
```python
upload_video_from_base64(video_base64, title, description, privacy_status, tags)
# Returns: {
#   'success': bool,
#   'video_id': 'YouTube video ID',
#   'video_url': 'https://youtube.com/watch?v=...',
#   'upload_status': 'upload status',
#   'privacy_status': 'private/public/unlisted'
# }

get_upload_quota_info()
# Returns quota usage and limits
```

**Upload Process**:
1. **Authentication**: Validates YouTube API credentials
2. **Video Processing**: Decodes base64 video data
3. **Metadata Setup**: Prepares title, description, tags
4. **Upload Execution**: Uploads video to YouTube
5. **Status Monitoring**: Tracks upload progress and completion

**Error Handling**:
- **Quota Exceeded**: Graceful handling of API limits
- **Authentication Errors**: Clear credential error messages
- **Upload Failures**: Retry logic with exponential backoff
- **Network Issues**: Timeout and connection error handling

## 🎨 Frontend Overview

### Technology Stack
- **React 18**: Modern React with hooks and functional components
- **Vite**: Fast build tool and development server
- **TailwindCSS**: Utility-first CSS framework
- **Modern JavaScript**: ES6+ features with async/await

### Key Components

#### `src/App.jsx` - Main Application
- **Layout Management**: Header, main content, footer structure
- **Component Orchestration**: Manages all child components
- **Global State**: Handles application-wide state management

#### `src/components/ProgressiveVideoGenerator.jsx` - Core Video Generation
- **Multi-step Process**: Story → Images → Voice → Video → YouTube
- **Real-time Progress**: Shows generation progress with visual feedback
- **Error Handling**: User-friendly error messages and retry options
- **Language Controls**: Independent language selection for different aspects

#### `src/components/AutomationQueueSection.jsx` - Queue Management
- **Queue Interface**: Add/remove people from automation queue
- **Status Monitoring**: Real-time status updates for queue items
- **Language Configuration**: Per-person language settings
- **Batch Operations**: Manage multiple queue items

#### `src/components/YouTubeUploadSection.jsx` - YouTube Integration
- **Upload Interface**: Direct YouTube upload from browser
- **Metadata Management**: Title and description editing
- **Language Toggle**: Switch content language before upload
- **Upload Progress**: Real-time upload status and progress

### UI/UX Features
- **Responsive Design**: Works on desktop and mobile devices
- **Dark Theme**: Modern dark interface with accent colors
- **Progressive Disclosure**: Shows content as it's generated
- **Loading States**: Clear feedback during processing
- **Error Recovery**: Graceful error handling with retry options

## 🤖 Airflow Automation

### DAG Structure

#### `airflow/dags/bioshort_automation.py` - Main Automation Workflow
**Purpose**: Daily automation that processes one person from the queue.

**Schedule**: Daily at 9:00 AM (`0 9 * * *`)

**Workflow Steps**:
1. **check_queue**: Checks for pending people in queue
2. **generate_video**: Generates complete video for selected person
3. **upload_to_youtube**: Uploads video with specified language settings
4. **no_processing_needed**: Handles empty queue scenario

**Key Features**:
- **Queue-based Processing**: Processes one person per day
- **Language Flexibility**: Uses individual language preferences
- **Error Handling**: Comprehensive error logging and recovery
- **Status Updates**: Updates queue status throughout process

#### `airflow/dags/database_cleanup.py` - Basic Cleanup Script
**Purpose**: Manual database cleanup via Airflow interface.

**Features**:
- **Paused by Default**: Prevents accidental execution
- **Manual Trigger Only**: No scheduled runs
- **Confirmation Steps**: Shows what will be deleted
- **API-based Cleanup**: Uses backend APIs for safe deletion

#### `airflow/dags/deep_database_cleanup.py` - Advanced Cleanup
**Purpose**: Comprehensive cleanup with backup functionality.

**Features**:
- **Pre-cleanup Backup**: Creates data backup before deletion
- **Multi-method Cleanup**: API + direct database access
- **Verification Steps**: Confirms cleanup completion
- **Recovery Information**: Stores backup for potential recovery

### Airflow Configuration
- **Executor**: LocalExecutor for single-machine deployment
- **Database**: PostgreSQL for Airflow metadata
- **Web Authentication**: Basic auth (airflow/airflow)
- **Logging**: Comprehensive task execution logs

## 🗄️ Database Schema

### SQLite Database (Application Data)
**File**: `bioshort.db` (created automatically)
**Location**: Backend container `/app/bioshort.db`

```sql
-- Automation Queue Table
CREATE TABLE people_queue (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,                    -- Person's name for biography
    title_language TEXT DEFAULT 'english', -- YouTube title language (english/hindi)
    description_language TEXT DEFAULT 'english', -- YouTube description language
    audio_language TEXT DEFAULT 'hindi',   -- Story and voice language
    voice_type TEXT DEFAULT 'hindi_male',  -- Voice type (hindi_male/hindi_female/english_male/english_female)
    status TEXT DEFAULT 'pending',         -- Status: pending/processing/completed/failed
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- When added to queue
    processed_at TIMESTAMP NULL,           -- When processing completed
    video_url TEXT NULL,                   -- YouTube URL after successful upload
    error_message TEXT NULL                -- Error details if processing failed
);

-- Processing Log Table (for debugging)
CREATE TABLE processing_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    person_name TEXT NOT NULL,             -- Person being processed
    status TEXT NOT NULL,                  -- Processing status
    message TEXT,                          -- Log message or error details
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- Log timestamp
);
```

### PostgreSQL Database (Airflow System)
**Connection**: `postgresql+psycopg2://airflow:airflow@airflow-db/airflow`
**Purpose**: Stores Airflow metadata, DAG runs, task instances, and execution logs

**Key Tables**:
- `dag_run`: DAG execution instances
- `task_instance`: Individual task executions
- `log`: Detailed execution logs
- `connection`: External system connections
- `variable`: Airflow variables and configuration

## 🚀 Setup & Installation

### Prerequisites
- **Docker**: Docker Desktop or Docker Engine
- **Docker Compose**: Version 2.0 or higher
- **Git**: For cloning the repository
- **Google Cloud Account**: For Gemini API access
- **YouTube Account**: For video uploads (optional)

### Step 1: Clone Repository
```bash
git clone <repository-url>
cd bioshort-app
```

### Step 2: Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your API keys
nano .env  # or use your preferred editor
```

### Step 3: Configure API Keys
Edit `.env` file with your credentials:
```env
# Google Gemini API
GEMINI_API_KEY=your_gemini_api_key_here

# YouTube API (optional)
YOUTUBE_CLIENT_ID=your_youtube_client_id
YOUTUBE_CLIENT_SECRET=your_youtube_client_secret
YOUTUBE_REFRESH_TOKEN=your_youtube_refresh_token

# Application Settings
FLASK_ENV=production
VITE_API_BASE_URL=http://localhost:5000/api
```

### Step 4: Start Application
```bash
# Start all services
docker-compose up -d

# Or use the startup script
./docker-run.sh
```

### Step 5: Access Applications
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:5000
- **Airflow Dashboard**: http://localhost:8080 (airflow/airflow)

### Step 6: Verify Installation
```bash
# Check all containers are running
docker ps

# Test API health
curl http://localhost:5000/api/health

# Check Airflow DAGs
# Go to http://localhost:8080 and verify DAGs are loaded
```

## 📊 API Documentation

### Core Generation Endpoints

#### `POST /api/generate-video`
**Purpose**: Complete video generation pipeline
```json
{
  "person_name": "Albert Einstein",
  "voice_type": "english_male",
  "language": "english"
}
```
**Response**: Complete video data with base64 encoding

#### `POST /api/generate-story`
**Purpose**: Generate biographical story only
```json
{
  "person_name": "Marie Curie",
  "language": "hindi"
}
```
**Response**: Story data with scenes and narration

#### `POST /api/generate-images`
**Purpose**: Generate scene images from story
```json
{
  "story_data": {...},
  "person_name": "Leonardo da Vinci"
}
```
**Response**: Array of base64 encoded images

### Queue Management Endpoints

#### `GET /api/queue`
**Purpose**: Get current automation queue
**Response**:
```json
{
  "success": true,
  "queue": [...],
  "count": 5,
  "pending": 3,
  "processing": 1,
  "completed": 1
}
```

#### `POST /api/queue/add`
**Purpose**: Add person to automation queue
```json
{
  "name": "Stephen Hawking",
  "title_language": "english",
  "description_language": "english",
  "audio_language": "hindi",
  "voice_type": "hindi_male"
}
```

#### `DELETE /api/queue/remove/<id>`
**Purpose**: Remove person from queue
**Response**: Success/failure status

### YouTube Integration Endpoints

#### `POST /api/upload-to-youtube`
**Purpose**: Upload video to YouTube
```json
{
  "video_base64": "base64_video_data",
  "title": "Video Title",
  "description": "Video Description",
  "privacy_status": "private",
  "tags": ["biography", "ai"]
}
```

#### `POST /api/generate-youtube-content`
**Purpose**: Generate YouTube metadata
```json
{
  "person_name": "Nikola Tesla",
  "language": "english",
  "story_data": {...}
}
```

## 🔧 Configuration

### Environment Variables

#### Required Variables
```env
# Google Gemini API (Required)
GEMINI_API_KEY=your_gemini_api_key

# Application Settings
FLASK_ENV=production
VITE_API_BASE_URL=http://localhost:5000/api
```

#### Optional Variables
```env
# YouTube API (for upload functionality)
YOUTUBE_CLIENT_ID=your_client_id
YOUTUBE_CLIENT_SECRET=your_client_secret
YOUTUBE_REFRESH_TOKEN=your_refresh_token

# Airflow Settings
AIRFLOW__CORE__EXECUTOR=LocalExecutor
AIRFLOW__DATABASE__SQL_ALCHEMY_CONN=postgresql+psycopg2://airflow:airflow@airflow-db/airflow
```

### Docker Configuration

#### `docker-compose.yml` Services
- **frontend**: React application (port 5173)
- **backend**: Flask API (port 5000)
- **airflow-webserver**: Airflow UI (port 8080)
- **airflow-scheduler**: Background task scheduler
- **airflow-db**: PostgreSQL database for Airflow
- **airflow-init**: One-time setup container

#### Volume Mappings
```yaml
volumes:
  - ./airflow/dags:/opt/airflow/dags     # DAG files
  - ./airflow/logs:/opt/airflow/logs     # Execution logs
  - backend-temp:/app/temp               # Temporary files
  - airflow-db-data:/var/lib/postgresql/data  # Database persistence
```

## 🎯 Usage Guide

### Manual Video Generation

#### Step 1: Access Frontend
1. Open http://localhost:5173 in your browser
2. Ensure all services are running (green status indicators)

#### Step 2: Generate Video
1. **Enter Person Name**: Type the name of a famous person
2. **Select Languages**: Choose languages for different components:
   - **Story Language**: Language for AI-generated story
   - **Voice Type**: Specific voice for narration
   - **Title Language**: Language for YouTube title
   - **Description Language**: Language for YouTube description
3. **Start Generation**: Click "Generate Video" button
4. **Monitor Progress**: Watch real-time progress through each stage:
   - Story Generation (AI writing)
   - Image Generation (AI creating visuals)
   - Voice Synthesis (Text-to-speech)
   - Video Compilation (Combining elements)

#### Step 3: Review and Download
1. **Preview Video**: Watch generated video in browser
2. **Download Options**: Download video file or audio separately
3. **YouTube Upload**: Optionally upload directly to YouTube

### Automation Queue Management

#### Adding People to Queue
1. **Navigate to Automation Section**: Scroll to "🤖 Automation Queue"
2. **Click Add Person**: Use "➕ Add Person" button
3. **Configure Settings**:
   - **Name**: Enter person's name
   - **Title Language**: English or Hindi for YouTube title
   - **Description Language**: English or Hindi for description
   - **Audio Language**: English or Hindi for story/voice
   - **Voice Type**: Specific voice (male/female per language)
4. **Add to Queue**: Confirm addition

#### Managing Queue
- **View Status**: See pending, processing, completed items
- **Remove Items**: Delete pending items before processing
- **Monitor Progress**: Track automation status
- **View Results**: Access YouTube URLs for completed videos

### Airflow Automation

#### Daily Automation
1. **Automatic Processing**: Runs daily at 9:00 AM
2. **Queue Processing**: Processes one person per day
3. **Status Updates**: Updates queue status automatically
4. **Error Handling**: Logs errors and updates status

#### Manual Triggers
1. **Access Airflow**: Go to http://localhost:8080
2. **Login**: Use airflow/airflow credentials
3. **Find DAG**: Locate `bioshort_automation` DAG
4. **Trigger**: Click play button to run immediately
5. **Monitor**: Watch task progress and logs

#### Database Cleanup
1. **Access Cleanup DAGs**: Find cleanup DAGs in Airflow
2. **Unpause DAG**: Enable the cleanup DAG you want to use
3. **Trigger Manually**: Run cleanup when needed
4. **Monitor Logs**: Verify cleanup completion

## 🛠️ Troubleshooting

### Common Issues

#### 🔴 Containers Not Starting
**Symptoms**: Docker containers fail to start or exit immediately
**Solutions**:
```bash
# Check container logs
docker-compose logs [service-name]

# Restart specific service
docker-compose restart [service-name]

# Rebuild containers
docker-compose up --build

# Check system resources
docker system df
```

#### 🔴 API Key Errors
**Symptoms**: "API key not found" or "Authentication failed"
**Solutions**:
1. **Verify .env file**: Ensure all required API keys are present
2. **Check key format**: Verify API keys are correctly formatted
3. **Restart backend**: `docker-compose restart backend`
4. **Check logs**: `docker-compose logs backend`

#### 🔴 YouTube Upload Failures
**Symptoms**: Videos generate but YouTube upload fails
**Solutions**:
1. **Check YouTube credentials**: Verify all YouTube API settings
2. **Check quota**: Ensure YouTube API quota is not exceeded
3. **Verify permissions**: Ensure YouTube API has upload permissions
4. **Check video size**: Ensure video is under YouTube size limits

#### 🔴 Voice Generation Issues
**Symptoms**: Videos generate without audio or with poor quality audio
**Solutions**:
1. **Check voice engine**: Verify edge-tts is working in container
2. **Try different voice**: Switch to alternative voice type
3. **Check audio files**: Verify audio files are being generated
4. **Container restart**: `docker-compose restart backend`

#### 🔴 Airflow DAGs Not Loading
**Symptoms**: DAGs don't appear in Airflow dashboard
**Solutions**:
1. **Check DAG syntax**: Verify Python syntax in DAG files
2. **Restart scheduler**: `docker-compose restart airflow-scheduler`
3. **Check logs**: `docker-compose logs airflow-scheduler`
4. **Verify file permissions**: Ensure DAG files are readable

### Performance Optimization

#### Memory Management
```bash
# Monitor container memory usage
docker stats

# Clean up unused resources
docker system prune

# Restart memory-intensive services
docker-compose restart backend
```

#### Storage Cleanup
```bash
# Remove old logs
rm -rf airflow/logs/*

# Clean Docker volumes
docker volume prune

# Remove temporary files
docker exec bioshort-backend rm -rf /app/temp/*
```

### Debugging Tips

#### Enable Debug Logging
1. **Backend**: Set `FLASK_ENV=development` in .env
2. **Airflow**: Check task logs in web interface
3. **Frontend**: Open browser developer tools

#### Check Service Health
```bash
# Test API endpoints
curl http://localhost:5000/api/health

# Check database connection
curl http://localhost:5000/api/queue

# Verify Airflow
curl http://localhost:8080/health
```

#### Log Analysis
```bash
# View all logs
docker-compose logs

# Follow specific service logs
docker-compose logs -f backend

# Search logs for errors
docker-compose logs | grep -i error
```

---

## 📞 Support

For issues not covered in this documentation:
1. **Check logs**: Always start with container logs
2. **Verify configuration**: Ensure all environment variables are set
3. **Test components**: Test individual services before full workflow
4. **Resource monitoring**: Check system resources (CPU, memory, disk)

---

**🎉 Congratulations! You now have a complete understanding of the BioShort application architecture, configuration, and usage. Happy video generating! 🚀**
- Docker and Docker Compose installed
- API Keys (see Configuration section)

**Easy Setup:**
```bash
# Windows
./docker-run.bat

# Linux/Mac
./docker-run.sh
```

**Manual Docker Setup:**
```bash
# Build and start containers
docker-compose up --build

# Access the application
# Frontend: http://localhost:5173
# Backend API: http://localhost:5000
```

**Docker Development Mode (with hot reload):**
```bash
docker-compose -f docker-compose.dev.yml up --build
```

### 💻 Option 2: Local Development

**Prerequisites:**
- Node.js 18+ and npm
- Python 3.8+ and pip
- FFmpeg installed on system
- Google API Key with Gemini access

**1. Clone and Setup**
```bash
git clone <repository>
cd bioshort-app
```

**2. Backend Setup**
```bash
cd backend
pip install -r requirements.txt
cp .env.example .env
# Add your GOOGLE_API_KEY to .env
python app.py
```

**3. Frontend Setup**
```bash
cd frontend
npm install
npm run dev
```

**4. Open Application**
- Frontend: http://localhost:5173
- Backend API: http://localhost:5000

## 📖 Usage

1. **Enter a famous person's name** (e.g., "Albert Einstein", "Marie Curie")
2. **Click "Generate Video"** and wait for AI processing
3. **Preview the generated video** with story segments and images
4. **Download MP4 video and MP3 audio** files
5. **Upload directly to YouTube** with custom titles and descriptions

## 🔧 Configuration

Create `.env` file in the project root (for Docker) or backend directory (for local development):
```env
# Google API Configuration (Required)
GEMINI_API_KEY=your_gemini_api_key_here

# Google Cloud Text-to-Speech (Optional - for better voice quality)
GOOGLE_APPLICATION_CREDENTIALS=path_to_service_account.json

# Flask Configuration
FLASK_ENV=development
FLASK_DEBUG=True

# YouTube API Configuration (Optional - for upload functionality)
YOUTUBE_CLIENT_ID=your_youtube_client_id
YOUTUBE_CLIENT_SECRET=your_youtube_client_secret
YOUTUBE_REFRESH_TOKEN=your_youtube_refresh_token
```

**Get API Keys:**
- **Gemini API Key**: Get from [Google AI Studio](https://makersuite.google.com/app/apikey)
- **YouTube API**: Follow the YouTube Upload Setup section below

## � YouTube Upload Setup (Optional)

To enable YouTube upload functionality:

### 1. Create YouTube API Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable YouTube Data API v3
4. Create OAuth 2.0 credentials (Desktop application)

### 2. Get OAuth Credentials
1. Download the client configuration JSON
2. Extract `client_id` and `client_secret`
3. Add them to your `.env` file

### 3. Generate Refresh Token
Run the OAuth flow once to get a refresh token:
```bash
cd backend
python -c "
from google_auth_oauthlib.flow import InstalledAppFlow
flow = InstalledAppFlow.from_client_config({
    'installed': {
        'client_id': 'YOUR_CLIENT_ID',
        'client_secret': 'YOUR_CLIENT_SECRET',
        'auth_uri': 'https://accounts.google.com/o/oauth2/auth',
        'token_uri': 'https://oauth2.googleapis.com/token'
    }
}, ['https://www.googleapis.com/auth/youtube.upload'])
credentials = flow.run_local_server(port=0)
print('Refresh Token:', credentials.refresh_token)
"
```

### 4. Test YouTube Integration
```bash
cd backend
python test_youtube_upload.py
```

## �📁 Project Structure

```
bioshort-app/
├── frontend/              # React.js frontend
│   ├── src/
│   │   ├── components/    # React components
│   │   ├── services/      # API services
│   │   └── App.jsx        # Main app component
│   ├── public/            # Static assets
│   ├── package.json       # Dependencies
│   └── Dockerfile         # Frontend Docker config
├── backend/               # Python Flask backend
│   ├── app.py            # Main Flask application
│   ├── services/         # AI service integrations
│   ├── utils/            # Helper utilities
│   ├── temp/             # Temporary file storage
│   ├── output/           # Generated video output
│   ├── requirements.txt  # Python dependencies
│   └── Dockerfile        # Backend Docker config
├── docker-compose.yml    # Docker orchestration
├── docker-compose.dev.yml # Docker development mode
├── docker-run.sh         # Docker startup script (Linux/Mac)
├── docker-run.bat        # Docker startup script (Windows)
├── README-Docker.md      # Docker documentation
├── .env                  # Environment variables
└── README.md            # This file
```

## 🎯 API Endpoints

- `GET /api/health` - Health check
- `POST /api/generate-video` - Generate video from person name
- `GET /api/download-video/<filename>` - Download generated video
- `GET /api/download-audio/<filename>` - Download generated audio
- `GET /api/preview-image/<filename>` - Preview generated images
- `POST /api/upload-to-youtube` - Upload video to YouTube
- `GET /api/youtube-quota-info` - Get YouTube upload quota information

## 🧪 Testing

Test the API directly:
```bash
curl -X POST http://localhost:5000/api/generate-video \
  -H "Content-Type: application/json" \
  -d '{"person_name": "Einstein"}'
```

## 🚀 Deployment

### 🐳 Docker Deployment
```bash
# Production deployment with Docker
docker-compose up -d --build

# Scale services if needed
docker-compose up -d --scale backend=2 --scale frontend=2
```

### ☁️ Cloud Deployment

**Frontend (Vercel)**
```bash
cd frontend
npm run build
# Deploy to Vercel
```

**Backend (Render/Railway)**
```bash
# Deploy Python Flask app to Render or Railway
# Ensure environment variables are set
```

**Full Stack (Docker on VPS)**
```bash
# Deploy both containers to VPS with Docker
scp docker-compose.yml user@server:/app/
ssh user@server "cd /app && docker-compose up -d --build"
```

## 📝 License

MIT License - Feel free to use and modify!

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Commit changes
4. Push to branch
5. Create Pull Request

---

**Made with ❤️ using AI • Gemini Pro • Google TTS • FFmpeg**


Client ID - 906925705975-r9641h32toea2djco0hdtbma1m30di4t.apps.googleusercontent.com
Client Secret - 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
Refresh Token - 1//04o0yipMeacDACgYIARAAGAQSNwF-L9IrmtxI1lqPQqAV9L5OuvKs5dQLE79T7LADQhPwFN6QwdByMi01j_zqrZB-dMmVb2vjHRM