# Docker Compose for BioShort

services:
  # Backend Service (Flask API)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: bioshort-backend
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=development
      - FLASK_DEBUG=true
    env_file:
      - .env
    volumes:
      # Mount .env file if it exists in the root directory
      - ./.env:/app/.env:ro
    networks:
      - bioshort-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend Service (React/Vite)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: bioshort-frontend
    ports:
      - "5173:5173"
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=http://localhost:5000/api
    env_file:
      - .env
    depends_on:
      - backend
    networks:
      - bioshort-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:5173"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Airflow Database (PostgreSQL)
  airflow-db:
    image: postgres:13
    container_name: bioshort-airflow-db
    environment:
      POSTGRES_USER: airflow
      POSTGRES_PASSWORD: airflow
      POSTGRES_DB: airflow
    volumes:
      - airflow-db-data:/var/lib/postgresql/data
    networks:
      - bioshort-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "airflow"]
      interval: 5s
      retries: 5

  # Airflow Webserver
  airflow-webserver:
    image: apache/airflow:2.7.0
    container_name: bioshort-airflow-webserver
    command: webserver
    ports:
      - "8080:8080"
    user: "50000:0"
    environment:
      AIRFLOW__CORE__EXECUTOR: LocalExecutor
      AIRFLOW__DATABASE__SQL_ALCHEMY_CONN: postgresql+psycopg2://airflow:airflow@airflow-db/airflow
      AIRFLOW__CORE__FERNET_KEY: ''
      AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION: 'false'
      AIRFLOW__CORE__LOAD_EXAMPLES: 'false'
      AIRFLOW__API__AUTH_BACKENDS: 'airflow.api.auth.backend.basic_auth'
      AIRFLOW__SCHEDULER__ENABLE_HEALTH_CHECK: 'true'
      AIRFLOW__WEBSERVER__EXPOSE_CONFIG: 'true'
      _AIRFLOW_WWW_USER_USERNAME: airflow
      _AIRFLOW_WWW_USER_PASSWORD: airflow
    volumes:
      - ./airflow/dags:/opt/airflow/dags
      - ./airflow/logs:/opt/airflow/logs
      - ./airflow/plugins:/opt/airflow/plugins
    networks:
      - bioshort-network
    depends_on:
      airflow-db:
        condition: service_healthy
      airflow-init:
        condition: service_completed_successfully
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "--fail", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Airflow Scheduler
  airflow-scheduler:
    image: apache/airflow:2.7.0
    container_name: bioshort-airflow-scheduler
    command: scheduler
    user: "50000:0"
    environment:
      AIRFLOW__CORE__EXECUTOR: LocalExecutor
      AIRFLOW__DATABASE__SQL_ALCHEMY_CONN: postgresql+psycopg2://airflow:airflow@airflow-db/airflow
      AIRFLOW__CORE__FERNET_KEY: ''
      AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION: 'false'
      AIRFLOW__CORE__LOAD_EXAMPLES: 'false'
      AIRFLOW__API__AUTH_BACKENDS: 'airflow.api.auth.backend.basic_auth'
      AIRFLOW__SCHEDULER__ENABLE_HEALTH_CHECK: 'true'
    volumes:
      - ./airflow/dags:/opt/airflow/dags
      - ./airflow/logs:/opt/airflow/logs
      - ./airflow/plugins:/opt/airflow/plugins
    networks:
      - bioshort-network
    depends_on:
      airflow-db:
        condition: service_healthy
      airflow-init:
        condition: service_completed_successfully
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", 'airflow jobs check --job-type SchedulerJob --hostname "$${HOSTNAME}"']
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Airflow Init (runs once to set up database)
  airflow-init:
    image: apache/airflow:2.7.0
    container_name: bioshort-airflow-init
    user: "0:0"
    entrypoint: /bin/bash
    command:
      - -c
      - |
        # Fix permissions for mounted volumes
        chown -R 50000:0 /opt/airflow/logs
        chown -R 50000:0 /opt/airflow/dags
        chown -R 50000:0 /opt/airflow/plugins
        chmod -R 755 /opt/airflow/logs
        chmod -R 755 /opt/airflow/dags
        chmod -R 755 /opt/airflow/plugins

        # Switch to airflow user for initialization
        su airflow -c "airflow db init"
        su airflow -c "airflow users create \
          --username airflow \
          --firstname Airflow \
          --lastname Admin \
          --role Admin \
          --email <EMAIL> \
          --password airflow"
    environment:
      AIRFLOW__CORE__EXECUTOR: LocalExecutor
      AIRFLOW__DATABASE__SQL_ALCHEMY_CONN: postgresql+psycopg2://airflow:airflow@airflow-db/airflow
      AIRFLOW__CORE__FERNET_KEY: ''
      AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION: 'false'
      AIRFLOW__CORE__LOAD_EXAMPLES: 'false'
      _AIRFLOW_WWW_USER_USERNAME: airflow
      _AIRFLOW_WWW_USER_PASSWORD: airflow
    volumes:
      - ./airflow/dags:/opt/airflow/dags
      - ./airflow/logs:/opt/airflow/logs
      - ./airflow/plugins:/opt/airflow/plugins
    networks:
      - bioshort-network
    depends_on:
      airflow-db:
        condition: service_healthy

networks:
  bioshort-network:
    driver: bridge

volumes:
  # Optional: Create named volumes for persistent data if needed in future
  backend-temp:
  backend-output:
  # Airflow database storage
  airflow-db-data:
