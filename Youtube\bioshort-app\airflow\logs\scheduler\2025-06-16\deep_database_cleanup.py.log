[2025-06-16T19:12:32.252+0000] {processor.py:157} INFO - Started process (PID=181) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:12:32.254+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:12:32.257+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:12:32.257+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:12:32.319+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:12:32.369+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:12:32.369+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:12:32.393+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:12:32.393+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:12:32.435+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.191 seconds
[2025-06-16T19:13:03.177+0000] {processor.py:157} INFO - Started process (PID=209) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:13:03.180+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:13:03.183+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:13:03.182+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:13:03.259+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:13:03.341+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:13:03.341+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:13:03.361+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:13:03.361+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:13:03.401+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.235 seconds
[2025-06-16T19:13:34.247+0000] {processor.py:157} INFO - Started process (PID=232) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:13:34.250+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:13:34.252+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:13:34.251+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:13:34.330+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:13:34.386+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:13:34.386+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:13:34.408+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:13:34.408+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:13:34.447+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.208 seconds
[2025-06-16T19:14:05.193+0000] {processor.py:157} INFO - Started process (PID=255) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:14:05.195+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:14:05.198+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:14:05.197+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:14:05.278+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:14:05.356+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:14:05.356+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:14:05.385+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:14:05.385+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:14:05.613+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.425 seconds
[2025-06-16T19:14:35.928+0000] {processor.py:157} INFO - Started process (PID=279) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:14:35.930+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:14:35.932+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:14:35.932+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:14:35.999+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:14:36.063+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:14:36.062+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:14:36.086+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:14:36.085+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:14:36.120+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.199 seconds
[2025-06-16T19:15:07.116+0000] {processor.py:157} INFO - Started process (PID=302) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:15:07.119+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:15:07.122+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:15:07.121+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:15:07.186+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:15:07.240+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:15:07.239+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:15:07.260+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:15:07.260+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:15:07.303+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.196 seconds
[2025-06-16T19:15:38.048+0000] {processor.py:157} INFO - Started process (PID=325) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:15:38.052+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:15:38.055+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:15:38.054+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:15:38.140+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:15:38.214+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:15:38.214+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:15:38.245+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:15:38.245+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:15:38.300+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.260 seconds
[2025-06-16T19:16:08.487+0000] {processor.py:157} INFO - Started process (PID=348) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:16:08.490+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:16:08.493+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:16:08.492+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:16:08.580+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:16:08.641+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:16:08.641+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:16:08.662+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:16:08.661+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:16:08.726+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.247 seconds
[2025-06-16T19:16:39.763+0000] {processor.py:157} INFO - Started process (PID=371) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:16:39.765+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:16:39.767+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:16:39.767+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:16:39.834+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:16:39.890+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:16:39.889+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:16:39.911+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:16:39.911+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:16:39.950+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.197 seconds
[2025-06-16T19:17:10.682+0000] {processor.py:157} INFO - Started process (PID=394) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:17:10.686+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:17:10.690+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:17:10.688+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:17:10.774+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:17:10.829+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:17:10.829+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:17:10.851+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:17:10.851+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:17:10.888+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.218 seconds
[2025-06-16T19:17:41.838+0000] {processor.py:157} INFO - Started process (PID=419) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:17:41.839+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:17:41.841+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:17:41.841+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:17:41.905+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:17:41.963+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:17:41.963+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:17:41.983+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:17:41.983+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:17:42.033+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.200 seconds
[2025-06-16T19:18:12.745+0000] {processor.py:157} INFO - Started process (PID=442) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:18:12.747+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:18:12.749+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:18:12.749+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:18:12.819+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:18:12.889+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:18:12.889+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:18:12.913+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:18:12.912+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:18:12.951+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.214 seconds
[2025-06-16T19:18:43.267+0000] {processor.py:157} INFO - Started process (PID=465) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:18:43.269+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:18:43.271+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:18:43.270+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:18:43.334+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:18:43.404+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:18:43.404+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:18:43.430+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:18:43.429+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:18:43.486+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.226 seconds
[2025-06-16T19:19:14.179+0000] {processor.py:157} INFO - Started process (PID=488) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:19:14.181+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:19:14.183+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:19:14.182+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:19:14.248+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:19:14.303+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:19:14.303+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:19:14.327+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:19:14.326+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:19:14.370+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.200 seconds
[2025-06-16T19:19:45.104+0000] {processor.py:157} INFO - Started process (PID=511) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:19:45.106+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:19:45.108+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:19:45.107+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:19:45.176+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:19:45.237+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:19:45.237+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:19:45.264+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:19:45.263+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:19:45.316+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.218 seconds
[2025-06-16T19:20:15.455+0000] {processor.py:157} INFO - Started process (PID=534) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:20:15.458+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:20:15.460+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:20:15.460+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:20:15.526+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:20:15.577+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:20:15.577+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:20:15.600+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:20:15.600+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:20:15.647+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.198 seconds
[2025-06-16T19:20:46.073+0000] {processor.py:157} INFO - Started process (PID=549) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:20:46.076+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:20:46.078+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:20:46.077+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:20:46.148+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:20:46.206+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:20:46.206+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:20:46.233+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:20:46.232+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:20:46.283+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.219 seconds
[2025-06-16T19:21:17.023+0000] {processor.py:157} INFO - Started process (PID=573) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:21:17.026+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:21:17.028+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:21:17.028+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:21:17.101+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:21:17.163+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:21:17.162+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:21:17.184+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:21:17.183+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:21:17.236+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.221 seconds
[2025-06-16T19:21:47.957+0000] {processor.py:157} INFO - Started process (PID=596) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:21:47.959+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:21:47.962+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:21:47.961+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:21:48.023+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:21:48.098+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:21:48.097+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:21:48.153+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:21:48.152+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:21:48.197+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.248 seconds
[2025-06-16T19:22:18.925+0000] {processor.py:157} INFO - Started process (PID=619) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:22:18.928+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:22:18.931+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:22:18.930+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:22:18.990+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:22:19.041+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:22:19.041+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:22:19.063+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:22:19.063+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:22:19.111+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.193 seconds
[2025-06-16T19:22:50.060+0000] {processor.py:157} INFO - Started process (PID=642) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:22:50.064+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:22:50.066+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:22:50.066+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:22:50.132+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:22:50.186+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:22:50.186+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:22:50.208+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:22:50.207+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:22:50.249+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.195 seconds
[2025-06-16T19:23:21.013+0000] {processor.py:157} INFO - Started process (PID=665) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:23:21.015+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:23:21.019+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:23:21.018+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:23:21.087+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:23:21.158+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:23:21.158+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:23:21.183+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:23:21.182+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:23:21.221+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.214 seconds
[2025-06-16T19:23:51.940+0000] {processor.py:157} INFO - Started process (PID=688) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:23:51.943+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:23:51.946+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:23:51.946+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:23:52.017+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:23:52.083+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:23:52.083+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:23:52.105+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:23:52.105+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:23:52.151+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.221 seconds
[2025-06-16T19:24:22.379+0000] {processor.py:157} INFO - Started process (PID=711) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:24:22.381+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:24:22.383+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:24:22.382+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:24:22.442+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:24:22.500+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:24:22.500+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:24:22.529+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:24:22.529+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:24:22.569+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.197 seconds
[2025-06-16T19:24:53.273+0000] {processor.py:157} INFO - Started process (PID=734) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:24:53.275+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:24:53.277+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:24:53.277+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:24:53.342+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:24:53.394+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:24:53.394+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:24:53.417+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:24:53.417+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:24:53.457+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.190 seconds
[2025-06-16T19:25:23.615+0000] {processor.py:157} INFO - Started process (PID=757) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:25:23.618+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:25:23.621+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:25:23.620+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:25:23.715+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:25:23.797+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:25:23.797+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:25:23.821+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:25:23.821+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:25:23.868+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.263 seconds
[2025-06-16T19:25:54.067+0000] {processor.py:157} INFO - Started process (PID=780) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:25:54.070+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:25:54.074+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:25:54.073+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:25:54.168+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:25:54.221+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:25:54.220+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:25:54.242+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:25:54.241+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:25:54.281+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.223 seconds
[2025-06-16T19:26:24.560+0000] {processor.py:157} INFO - Started process (PID=804) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:26:24.563+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:26:24.565+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:26:24.564+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:26:24.646+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:26:24.708+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:26:24.708+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:26:24.737+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:26:24.736+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:26:24.790+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.238 seconds
[2025-06-16T19:26:55.828+0000] {processor.py:157} INFO - Started process (PID=827) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:26:55.831+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:26:55.834+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:26:55.833+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:26:55.897+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:26:55.948+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:26:55.948+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:26:55.965+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:26:55.965+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:26:56.001+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.179 seconds
[2025-06-16T19:27:26.690+0000] {processor.py:157} INFO - Started process (PID=850) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:27:26.692+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:27:26.694+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:27:26.694+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:27:26.757+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:27:26.807+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:27:26.807+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:27:26.827+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:27:26.827+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:27:26.864+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.180 seconds
[2025-06-16T19:27:57.879+0000] {processor.py:157} INFO - Started process (PID=872) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:27:57.881+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:27:57.884+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:27:57.883+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:27:57.948+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:27:58.009+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:27:58.009+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:27:58.040+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:27:58.040+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:27:58.094+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.223 seconds
[2025-06-16T19:28:28.870+0000] {processor.py:157} INFO - Started process (PID=888) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:28:28.873+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:28:28.875+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:28:28.874+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:28:28.942+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:28:29.003+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:28:29.002+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:28:29.029+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:28:29.028+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:28:29.062+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.198 seconds
[2025-06-16T19:28:59.788+0000] {processor.py:157} INFO - Started process (PID=911) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:28:59.790+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:28:59.793+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:28:59.792+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:28:59.858+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:28:59.908+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:28:59.907+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:28:59.931+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:28:59.930+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:28:59.975+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.194 seconds
[2025-06-16T19:29:30.672+0000] {processor.py:157} INFO - Started process (PID=934) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:29:30.673+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:29:30.676+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:29:30.675+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:29:30.742+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:29:30.796+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:29:30.796+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:29:30.818+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:29:30.818+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:29:30.866+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.200 seconds
[2025-06-16T19:30:01.582+0000] {processor.py:157} INFO - Started process (PID=957) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:30:01.584+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:30:01.586+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:30:01.585+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:30:01.659+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:30:01.712+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:30:01.711+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:30:01.737+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:30:01.737+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:30:01.772+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.196 seconds
[2025-06-16T19:30:32.429+0000] {processor.py:157} INFO - Started process (PID=980) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:30:32.431+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:30:32.433+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:30:32.432+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:30:32.503+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:30:32.555+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:30:32.555+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:30:32.575+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:30:32.575+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:30:32.619+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.197 seconds
[2025-06-16T19:31:03.350+0000] {processor.py:157} INFO - Started process (PID=1003) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:31:03.353+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:31:03.355+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:31:03.355+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:31:03.420+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:31:03.472+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:31:03.472+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:31:03.496+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:31:03.496+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:31:03.529+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.185 seconds
[2025-06-16T19:31:34.280+0000] {processor.py:157} INFO - Started process (PID=1026) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:31:34.283+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:31:34.285+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:31:34.284+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:31:34.354+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:31:34.413+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:31:34.412+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:31:34.435+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:31:34.435+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:31:34.481+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.207 seconds
[2025-06-16T19:32:05.214+0000] {processor.py:157} INFO - Started process (PID=1049) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:32:05.224+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:32:05.226+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:32:05.225+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:32:05.286+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:32:05.339+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:32:05.339+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:32:05.362+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:32:05.362+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:32:05.401+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.195 seconds
[2025-06-16T19:32:35.524+0000] {processor.py:157} INFO - Started process (PID=1073) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:32:35.526+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:32:35.528+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:32:35.527+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:32:35.599+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:32:35.652+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:32:35.652+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:32:35.679+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:32:35.678+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:32:35.716+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.198 seconds
[2025-06-16T19:33:06.674+0000] {processor.py:157} INFO - Started process (PID=1096) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:33:06.677+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:33:06.679+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:33:06.679+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:33:06.739+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:33:06.793+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:33:06.793+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:33:06.813+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:33:06.813+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:33:06.845+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.178 seconds
[2025-06-16T19:33:37.515+0000] {processor.py:157} INFO - Started process (PID=1119) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:33:37.517+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:33:37.519+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:33:37.519+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:33:37.590+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:33:37.643+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:33:37.643+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:33:37.665+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:33:37.665+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:33:37.712+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.204 seconds
[2025-06-16T19:34:08.412+0000] {processor.py:157} INFO - Started process (PID=1142) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:34:08.415+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:34:08.418+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:34:08.417+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:34:08.487+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:34:08.553+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:34:08.552+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:34:08.580+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:34:08.579+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:34:08.631+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.226 seconds
[2025-06-16T19:34:39.423+0000] {processor.py:157} INFO - Started process (PID=1166) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:34:39.426+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:34:39.429+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:34:39.428+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:34:39.503+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:34:39.574+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:34:39.574+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:34:39.601+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:34:39.601+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:34:39.642+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.226 seconds
[2025-06-16T19:35:10.354+0000] {processor.py:157} INFO - Started process (PID=1189) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:35:10.356+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:35:10.358+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:35:10.357+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:35:10.429+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:35:10.482+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:35:10.482+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:35:10.503+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:35:10.503+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:35:10.549+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.203 seconds
[2025-06-16T19:35:42.348+0000] {processor.py:157} INFO - Started process (PID=1212) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:35:42.353+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:35:42.361+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:35:42.360+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:35:42.601+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:35:42.672+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:35:42.672+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:35:42.698+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:35:42.698+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:35:42.748+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.408 seconds
[2025-06-16T19:36:13.522+0000] {processor.py:157} INFO - Started process (PID=1227) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:36:13.524+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:36:13.526+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:36:13.525+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:36:13.591+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:36:13.652+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:36:13.652+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:36:13.673+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:36:13.673+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:36:13.711+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.195 seconds
[2025-06-16T19:36:44.513+0000] {processor.py:157} INFO - Started process (PID=1250) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:36:44.516+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:36:44.518+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:36:44.517+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:36:44.600+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:36:44.663+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:36:44.663+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:36:44.687+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:36:44.687+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:36:44.731+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.225 seconds
[2025-06-16T19:37:15.503+0000] {processor.py:157} INFO - Started process (PID=1273) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:37:15.505+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:37:15.507+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:37:15.507+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:37:15.577+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:37:15.638+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:37:15.638+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:37:15.660+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:37:15.659+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:37:15.714+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.217 seconds
[2025-06-16T19:37:46.032+0000] {processor.py:157} INFO - Started process (PID=1296) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:37:46.034+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:37:46.038+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:37:46.038+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:37:46.112+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:37:46.179+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:37:46.178+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:37:46.200+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:37:46.199+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:37:46.241+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.217 seconds
[2025-06-16T19:38:16.360+0000] {processor.py:157} INFO - Started process (PID=1319) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:38:16.366+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:38:16.368+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:38:16.368+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:38:16.436+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:38:16.488+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:38:16.488+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:38:16.512+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:38:16.511+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:38:16.562+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.209 seconds
[2025-06-16T19:38:46.716+0000] {processor.py:157} INFO - Started process (PID=1342) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:38:46.720+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:38:46.723+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:38:46.722+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:38:46.818+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:38:46.875+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:38:46.874+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:38:46.903+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:38:46.902+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:38:46.957+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.249 seconds
[2025-06-16T19:39:17.997+0000] {processor.py:157} INFO - Started process (PID=1366) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:39:18.001+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:39:18.005+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:39:18.003+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:39:18.089+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:39:18.157+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:39:18.157+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:39:18.184+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:39:18.184+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:39:18.232+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.244 seconds
[2025-06-16T19:39:48.975+0000] {processor.py:157} INFO - Started process (PID=1390) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:39:48.977+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:39:48.980+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:39:48.979+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:39:49.050+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:39:49.113+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:39:49.112+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:39:49.141+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:39:49.140+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:39:49.177+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.209 seconds
[2025-06-16T19:40:19.973+0000] {processor.py:157} INFO - Started process (PID=1413) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:40:19.975+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:40:19.977+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:40:19.976+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:40:20.058+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:40:20.129+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:40:20.128+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:40:20.153+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:40:20.152+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:40:20.190+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.224 seconds
[2025-06-16T19:40:50.989+0000] {processor.py:157} INFO - Started process (PID=1436) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:40:50.992+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:40:50.995+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:40:50.994+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:40:51.082+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:40:51.143+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:40:51.143+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:40:51.171+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:40:51.171+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:40:51.208+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.227 seconds
[2025-06-16T19:41:21.374+0000] {processor.py:157} INFO - Started process (PID=1459) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:41:21.377+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:41:21.379+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:41:21.378+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:41:21.479+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:41:21.530+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:41:21.530+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:41:21.556+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:41:21.555+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:41:21.599+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.232 seconds
[2025-06-16T19:41:51.717+0000] {processor.py:157} INFO - Started process (PID=1482) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:41:51.719+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:41:51.721+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:41:51.720+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:41:51.812+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:41:51.862+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:41:51.862+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:41:51.882+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:41:51.882+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:41:51.921+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.210 seconds
[2025-06-16T19:42:22.181+0000] {processor.py:157} INFO - Started process (PID=1505) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:42:22.184+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:42:22.186+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:42:22.186+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:42:22.254+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:42:22.306+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:42:22.306+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:42:22.328+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:42:22.327+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:42:22.364+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.191 seconds
[2025-06-16T19:42:52.587+0000] {processor.py:157} INFO - Started process (PID=1528) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:42:52.589+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:42:52.591+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:42:52.590+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:42:52.660+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:42:52.727+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:42:52.726+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:42:52.758+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:42:52.757+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:42:52.804+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.222 seconds
[2025-06-16T19:43:23.267+0000] {processor.py:157} INFO - Started process (PID=1543) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:43:23.270+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:43:23.273+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:43:23.272+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:43:23.346+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:43:23.411+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:43:23.410+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:43:23.437+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:43:23.437+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:43:23.479+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.221 seconds
[2025-06-16T19:43:54.295+0000] {processor.py:157} INFO - Started process (PID=1566) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:43:54.297+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:43:54.299+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:43:54.298+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:43:54.361+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:43:54.422+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:43:54.421+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:43:54.443+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:43:54.443+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:43:54.489+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.199 seconds
[2025-06-16T19:44:25.340+0000] {processor.py:157} INFO - Started process (PID=1589) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:44:25.343+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:44:25.346+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:44:25.345+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:44:25.424+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:44:25.481+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:44:25.480+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:44:25.503+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:44:25.503+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:44:25.541+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.210 seconds
[2025-06-16T19:44:55.718+0000] {processor.py:157} INFO - Started process (PID=1612) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:44:55.721+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:44:55.723+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:44:55.722+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:44:55.805+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:44:55.856+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:44:55.855+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:44:55.878+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:44:55.878+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:44:55.921+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.212 seconds
[2025-06-16T19:45:26.055+0000] {processor.py:157} INFO - Started process (PID=1635) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:45:26.057+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:45:26.060+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:45:26.059+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:45:26.131+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:45:26.179+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:45:26.179+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:45:26.199+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:45:26.199+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:45:26.234+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.186 seconds
[2025-06-16T19:45:56.481+0000] {processor.py:157} INFO - Started process (PID=1658) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:45:56.484+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:45:56.486+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:45:56.485+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:45:56.545+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:45:56.594+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:45:56.594+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:45:56.613+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:45:56.613+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:45:56.647+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.172 seconds
[2025-06-16T19:46:27.030+0000] {processor.py:157} INFO - Started process (PID=1681) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:46:27.033+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:46:27.036+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:46:27.035+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:46:27.141+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:46:27.207+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:46:27.206+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:46:27.233+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:46:27.232+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:46:27.286+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.264 seconds
[2025-06-16T19:46:58.122+0000] {processor.py:157} INFO - Started process (PID=1704) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:46:58.124+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:46:58.127+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:46:58.126+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:46:58.207+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:46:58.255+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:46:58.255+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:46:58.277+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:46:58.277+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:46:58.328+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.212 seconds
[2025-06-16T19:47:28.482+0000] {processor.py:157} INFO - Started process (PID=1727) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:47:28.485+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:47:28.487+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:47:28.487+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:47:28.558+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:47:28.608+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:47:28.608+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:47:28.632+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:47:28.631+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:47:28.685+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.211 seconds
[2025-06-16T19:47:58.950+0000] {processor.py:157} INFO - Started process (PID=1750) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:47:58.953+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:47:58.955+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:47:58.954+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:47:59.033+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:47:59.082+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:47:59.082+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:47:59.103+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:47:59.102+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:47:59.140+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.195 seconds
[2025-06-16T19:48:29.619+0000] {processor.py:157} INFO - Started process (PID=1773) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:48:29.626+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:48:29.629+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:48:29.628+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:48:29.724+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:48:29.775+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:48:29.775+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:48:29.796+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:48:29.796+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:48:29.839+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.227 seconds
[2025-06-16T19:49:00.071+0000] {processor.py:157} INFO - Started process (PID=1795) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:49:00.074+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:49:00.080+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:49:00.079+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:49:00.202+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:49:00.267+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:49:00.267+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:49:00.292+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:49:00.291+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:49:00.334+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.275 seconds
[2025-06-16T19:49:30.493+0000] {processor.py:157} INFO - Started process (PID=1811) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:49:30.495+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:49:30.498+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:49:30.497+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:49:30.580+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:49:30.640+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:49:30.640+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:49:30.661+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:49:30.661+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:49:30.695+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.210 seconds
[2025-06-16T19:50:00.838+0000] {processor.py:157} INFO - Started process (PID=1835) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:50:00.840+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:50:00.843+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:50:00.842+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:50:00.934+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:50:00.986+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:50:00.986+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:50:01.010+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:50:01.010+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:50:01.067+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.236 seconds
[2025-06-16T19:50:31.791+0000] {processor.py:157} INFO - Started process (PID=1858) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:50:31.793+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:50:31.796+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:50:31.795+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:50:31.891+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:50:31.942+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:50:31.942+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:50:31.964+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:50:31.964+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:50:32.005+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.221 seconds
[2025-06-16T19:51:02.902+0000] {processor.py:157} INFO - Started process (PID=1881) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:51:02.905+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:51:02.907+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:51:02.907+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:51:03.003+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:51:03.062+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:51:03.062+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:51:03.087+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:51:03.086+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:51:03.136+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.241 seconds
[2025-06-16T19:51:34.052+0000] {processor.py:157} INFO - Started process (PID=1904) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:51:34.055+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:51:34.057+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:51:34.057+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:51:34.137+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:51:34.183+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:51:34.183+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:51:34.211+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:51:34.210+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:51:34.268+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.223 seconds
[2025-06-16T19:52:05.226+0000] {processor.py:157} INFO - Started process (PID=1927) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:52:05.228+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:52:05.231+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:52:05.230+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:52:05.298+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:52:05.344+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:52:05.343+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:52:05.366+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:52:05.366+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:52:05.403+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.184 seconds
[2025-06-16T19:52:35.600+0000] {processor.py:157} INFO - Started process (PID=1950) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:52:35.603+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:52:35.606+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:52:35.605+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:52:35.669+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:52:35.724+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:52:35.724+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:52:35.746+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:52:35.746+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:52:35.787+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.196 seconds
[2025-06-16T19:53:06.073+0000] {processor.py:157} INFO - Started process (PID=1973) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:53:06.076+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T19:53:06.078+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:53:06.078+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:53:06.164+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T19:53:06.225+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:53:06.225+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T19:53:06.249+0000] {logging_mixin.py:151} INFO - [2025-06-16T19:53:06.248+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T19:53:06.288+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.224 seconds
[2025-06-16T20:02:35.188+0000] {processor.py:157} INFO - Started process (PID=2124) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:02:35.191+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:02:35.194+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:02:35.193+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:02:35.320+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:02:35.760+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:02:35.760+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:02:35.781+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:02:35.780+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:02:35.834+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.656 seconds
[2025-06-16T20:03:06.126+0000] {processor.py:157} INFO - Started process (PID=2147) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:03:06.130+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:03:06.132+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:03:06.131+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:03:06.198+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:03:06.258+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:03:06.258+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:03:06.280+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:03:06.280+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:03:06.313+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.194 seconds
[2025-06-16T20:03:37.084+0000] {processor.py:157} INFO - Started process (PID=2170) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:03:37.087+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:03:37.089+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:03:37.088+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:03:37.159+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:03:37.227+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:03:37.227+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:03:37.250+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:03:37.250+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:03:37.293+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.215 seconds
[2025-06-16T20:04:08.089+0000] {processor.py:157} INFO - Started process (PID=2193) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:04:08.092+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:04:08.094+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:04:08.094+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:04:08.169+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:04:08.260+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:04:08.259+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:04:08.288+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:04:08.288+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:04:08.336+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.255 seconds
[2025-06-16T20:04:39.126+0000] {processor.py:157} INFO - Started process (PID=2216) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:04:39.128+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:04:39.131+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:04:39.131+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:04:39.204+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:04:39.259+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:04:39.258+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:04:39.281+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:04:39.281+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:04:39.315+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.199 seconds
[2025-06-16T20:05:10.056+0000] {processor.py:157} INFO - Started process (PID=2239) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:05:10.058+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:05:10.060+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:05:10.060+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:05:10.116+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:05:10.171+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:05:10.171+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:05:10.199+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:05:10.199+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:05:10.247+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.196 seconds
[2025-06-16T20:05:42.062+0000] {processor.py:157} INFO - Started process (PID=2262) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:05:42.072+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:05:42.078+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:05:42.077+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:05:42.220+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:05:42.287+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:05:42.287+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:05:42.313+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:05:42.313+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:05:42.366+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.314 seconds
[2025-06-16T20:06:13.147+0000] {processor.py:157} INFO - Started process (PID=2277) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:06:13.149+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:06:13.151+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:06:13.151+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:06:13.226+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:06:13.294+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:06:13.293+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:06:13.320+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:06:13.320+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:06:13.372+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.230 seconds
[2025-06-16T20:06:44.188+0000] {processor.py:157} INFO - Started process (PID=2300) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:06:44.191+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:06:44.194+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:06:44.193+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:06:44.267+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:06:44.329+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:06:44.329+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:06:44.354+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:06:44.353+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:06:44.389+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.207 seconds
[2025-06-16T20:07:14.589+0000] {processor.py:157} INFO - Started process (PID=2323) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:07:14.593+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:07:14.596+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:07:14.595+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:07:14.687+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:07:14.764+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:07:14.763+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:07:14.797+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:07:14.797+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:07:14.850+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.269 seconds
[2025-06-16T20:07:45.013+0000] {processor.py:157} INFO - Started process (PID=2345) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:07:45.015+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:07:45.018+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:07:45.017+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:07:45.091+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:07:45.147+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:07:45.147+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:07:45.180+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:07:45.179+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:07:45.229+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.224 seconds
[2025-06-16T20:08:15.619+0000] {processor.py:157} INFO - Started process (PID=2368) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:08:15.622+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:08:15.624+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:08:15.624+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:08:15.715+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:08:15.775+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:08:15.774+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:08:15.808+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:08:15.807+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:08:15.849+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.237 seconds
[2025-06-16T20:08:46.055+0000] {processor.py:157} INFO - Started process (PID=2391) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:08:46.058+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:08:46.060+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:08:46.059+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:08:46.125+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:08:46.177+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:08:46.177+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:08:46.199+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:08:46.199+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:08:46.242+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.192 seconds
[2025-06-16T20:09:16.694+0000] {processor.py:157} INFO - Started process (PID=2414) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:09:16.698+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:09:16.702+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:09:16.701+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:09:16.802+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:09:16.870+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:09:16.869+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:09:16.905+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:09:16.904+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:09:16.967+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.282 seconds
[2025-06-16T20:09:47.119+0000] {processor.py:157} INFO - Started process (PID=2437) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:09:47.125+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:09:47.128+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:09:47.127+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:09:47.223+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:09:47.289+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:09:47.288+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:09:47.311+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:09:47.311+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:09:47.358+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.248 seconds
[2025-06-16T20:10:17.520+0000] {processor.py:157} INFO - Started process (PID=2461) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:10:17.523+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:10:17.526+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:10:17.525+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:10:17.609+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:10:17.666+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:10:17.665+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:10:17.693+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:10:17.692+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:10:17.737+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.223 seconds
[2025-06-16T20:10:47.917+0000] {processor.py:157} INFO - Started process (PID=2484) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:10:47.919+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:10:47.922+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:10:47.921+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:10:48.005+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:10:48.090+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:10:48.090+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:10:48.112+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:10:48.112+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:10:48.144+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.233 seconds
[2025-06-16T20:11:18.561+0000] {processor.py:157} INFO - Started process (PID=2507) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:11:18.563+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:11:18.565+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:11:18.564+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:11:18.626+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:11:18.683+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:11:18.683+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:11:18.716+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:11:18.716+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:11:18.764+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.208 seconds
[2025-06-16T20:11:49.218+0000] {processor.py:157} INFO - Started process (PID=2530) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:11:49.220+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:11:49.223+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:11:49.222+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:11:49.299+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:11:49.366+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:11:49.365+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:11:49.389+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:11:49.389+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:11:49.438+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.228 seconds
[2025-06-16T20:12:20.225+0000] {processor.py:157} INFO - Started process (PID=2553) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:12:20.227+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:12:20.229+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:12:20.228+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:12:20.293+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:12:20.340+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:12:20.340+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:12:20.358+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:12:20.358+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:12:20.390+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.170 seconds
[2025-06-16T20:12:51.299+0000] {processor.py:157} INFO - Started process (PID=2576) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:12:51.302+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:12:51.305+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:12:51.304+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:12:51.371+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:12:51.447+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:12:51.447+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:12:51.480+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:12:51.480+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:12:51.533+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.241 seconds
[2025-06-16T20:13:22.358+0000] {processor.py:157} INFO - Started process (PID=2591) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:13:22.360+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:13:22.362+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:13:22.362+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:13:22.446+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:13:22.513+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:13:22.513+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:13:22.534+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:13:22.533+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:13:22.571+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.221 seconds
[2025-06-16T20:13:53.344+0000] {processor.py:157} INFO - Started process (PID=2614) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:13:53.346+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:13:53.349+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:13:53.348+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:13:53.433+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:13:53.492+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:13:53.492+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:13:53.513+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:13:53.513+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:13:53.560+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.225 seconds
[2025-06-16T20:14:24.351+0000] {processor.py:157} INFO - Started process (PID=2637) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:14:24.354+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:14:24.357+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:14:24.356+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:14:24.431+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:14:24.483+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:14:24.483+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:14:24.506+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:14:24.506+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:14:24.549+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.203 seconds
[2025-06-16T20:14:54.717+0000] {processor.py:157} INFO - Started process (PID=2661) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:14:54.720+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:14:54.723+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:14:54.722+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:14:54.806+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:14:54.871+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:14:54.871+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:14:54.894+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:14:54.893+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:14:54.934+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.224 seconds
[2025-06-16T20:15:25.137+0000] {processor.py:157} INFO - Started process (PID=2684) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:15:25.142+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:15:25.145+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:15:25.144+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:15:25.215+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:15:25.270+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:15:25.270+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:15:25.295+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:15:25.294+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:15:25.333+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.210 seconds
[2025-06-16T20:15:55.528+0000] {processor.py:157} INFO - Started process (PID=2707) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:15:55.531+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:15:55.534+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:15:55.533+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:15:55.624+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:15:55.676+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:15:55.675+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:15:55.704+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:15:55.703+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:15:55.752+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.230 seconds
[2025-06-16T20:16:26.888+0000] {processor.py:157} INFO - Started process (PID=2730) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:16:26.891+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:16:26.893+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:16:26.893+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:16:26.959+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:16:27.021+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:16:27.021+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:16:27.042+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:16:27.041+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:16:27.080+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.200 seconds
[2025-06-16T20:16:57.224+0000] {processor.py:157} INFO - Started process (PID=2753) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:16:57.229+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:16:57.231+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:16:57.231+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:16:57.319+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:16:57.383+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:16:57.382+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:16:57.412+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:16:57.411+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:16:57.450+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.234 seconds
[2025-06-16T20:17:27.592+0000] {processor.py:157} INFO - Started process (PID=2776) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:17:27.595+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:17:27.596+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:17:27.596+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:17:27.674+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:17:27.733+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:17:27.733+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:17:27.758+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:17:27.758+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:17:27.812+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.225 seconds
[2025-06-16T20:17:57.995+0000] {processor.py:157} INFO - Started process (PID=2799) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:17:57.998+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:17:58.003+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:17:58.002+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:17:58.082+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:17:58.147+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:17:58.146+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:17:58.169+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:17:58.167+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:17:58.225+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.236 seconds
[2025-06-16T20:18:28.939+0000] {processor.py:157} INFO - Started process (PID=2822) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:18:28.947+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:18:28.954+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:18:28.954+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:18:29.232+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:18:29.295+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:18:29.295+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:18:29.324+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:18:29.324+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:18:29.391+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.458 seconds
[2025-06-16T20:18:59.645+0000] {processor.py:157} INFO - Started process (PID=2845) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:18:59.648+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:18:59.650+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:18:59.649+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:18:59.712+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:18:59.761+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:18:59.761+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:18:59.785+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:18:59.784+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:18:59.835+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.196 seconds
[2025-06-16T20:19:29.975+0000] {processor.py:157} INFO - Started process (PID=2860) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:19:29.978+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:19:29.981+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:19:29.980+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:19:30.070+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:19:30.130+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:19:30.130+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:19:30.153+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:19:30.153+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:19:30.192+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.224 seconds
[2025-06-16T20:20:01.056+0000] {processor.py:157} INFO - Started process (PID=2883) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:20:01.060+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:20:01.063+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:20:01.062+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:20:01.151+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:20:01.212+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:20:01.212+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:20:01.239+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:20:01.239+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:20:01.279+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.233 seconds
[2025-06-16T20:20:31.723+0000] {processor.py:157} INFO - Started process (PID=2906) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:20:31.726+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:20:31.728+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:20:31.727+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:20:31.793+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:20:31.862+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:20:31.861+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:20:31.886+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:20:31.885+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:20:31.943+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.226 seconds
[2025-06-16T20:21:02.610+0000] {processor.py:157} INFO - Started process (PID=2929) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:21:02.612+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:21:02.614+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:21:02.614+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:21:02.685+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:21:02.748+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:21:02.748+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:21:02.771+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:21:02.770+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:21:02.820+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.216 seconds
[2025-06-16T20:21:33.556+0000] {processor.py:157} INFO - Started process (PID=2952) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:21:33.559+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:21:33.562+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:21:33.561+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:21:33.640+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:21:33.700+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:21:33.699+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:21:33.721+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:21:33.720+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:21:33.757+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.208 seconds
[2025-06-16T20:22:04.147+0000] {processor.py:157} INFO - Started process (PID=2975) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:22:04.150+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:22:04.155+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:22:04.155+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:22:04.232+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:22:04.286+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:22:04.286+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:22:04.308+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:22:04.308+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:22:04.357+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.216 seconds
[2025-06-16T20:22:35.126+0000] {processor.py:157} INFO - Started process (PID=2998) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:22:35.128+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:22:35.131+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:22:35.130+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:22:35.193+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:22:35.242+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:22:35.242+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:22:35.261+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:22:35.261+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:22:35.301+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.181 seconds
[2025-06-16T20:23:06.333+0000] {processor.py:157} INFO - Started process (PID=3021) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:23:06.336+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:23:06.339+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:23:06.338+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:23:06.412+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:23:06.464+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:23:06.464+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:23:06.492+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:23:06.492+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:23:06.527+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.201 seconds
[2025-06-16T20:23:37.322+0000] {processor.py:157} INFO - Started process (PID=3044) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:23:37.324+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:23:37.326+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:23:37.326+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:23:37.387+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:23:37.438+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:23:37.437+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:23:37.458+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:23:37.458+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:23:37.706+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.390 seconds
[2025-06-16T20:24:08.522+0000] {processor.py:157} INFO - Started process (PID=3067) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:24:08.525+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:24:08.527+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:24:08.527+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:24:08.594+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:24:08.647+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:24:08.647+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:24:08.669+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:24:08.668+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:24:08.706+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.193 seconds
[2025-06-16T20:24:39.032+0000] {processor.py:157} INFO - Started process (PID=3090) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:24:39.034+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:24:39.036+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:24:39.036+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:24:39.112+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:24:39.182+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:24:39.182+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:24:39.375+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:24:39.374+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:24:39.410+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.387 seconds
[2025-06-16T20:25:09.730+0000] {processor.py:157} INFO - Started process (PID=3113) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:25:09.733+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:25:09.736+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:25:09.735+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:25:09.806+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:25:09.860+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:25:09.860+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:25:09.881+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:25:09.881+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:25:09.918+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.194 seconds
[2025-06-16T20:25:40.638+0000] {processor.py:157} INFO - Started process (PID=3136) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:25:40.641+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:25:40.644+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:25:40.643+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:25:40.703+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:25:40.944+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:25:40.944+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:25:40.964+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:25:40.964+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:25:40.999+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.370 seconds
[2025-06-16T20:26:11.177+0000] {processor.py:157} INFO - Started process (PID=3151) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:26:11.180+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:26:11.183+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:26:11.183+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:26:11.272+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:26:11.343+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:26:11.342+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:26:11.370+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:26:11.369+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:26:11.416+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.246 seconds
[2025-06-16T20:26:41.720+0000] {processor.py:157} INFO - Started process (PID=3174) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:26:41.722+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:26:41.724+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:26:41.723+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:26:41.783+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:26:41.837+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:26:41.836+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:26:41.861+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:26:41.861+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:26:41.904+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.192 seconds
[2025-06-16T20:27:13.043+0000] {processor.py:157} INFO - Started process (PID=3197) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:27:13.046+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:27:13.051+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:27:13.049+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:27:13.141+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:27:13.259+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:27:13.258+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:27:13.295+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:27:13.295+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:27:13.430+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.393 seconds
[2025-06-16T20:27:43.954+0000] {processor.py:157} INFO - Started process (PID=3220) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:27:43.955+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:27:43.957+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:27:43.957+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:27:44.024+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:27:44.082+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:27:44.082+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:27:44.103+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:27:44.102+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:27:44.138+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.190 seconds
[2025-06-16T20:28:14.820+0000] {processor.py:157} INFO - Started process (PID=3243) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:28:14.822+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:28:14.824+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:28:14.824+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:28:14.873+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:28:14.924+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:28:14.924+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:28:14.946+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:28:14.945+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:28:14.989+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.175 seconds
[2025-06-16T20:28:45.443+0000] {processor.py:157} INFO - Started process (PID=3266) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:28:45.445+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:28:45.447+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:28:45.446+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:28:45.510+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:28:45.675+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:28:45.675+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:28:45.723+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:28:45.723+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:28:45.802+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.364 seconds
[2025-06-16T20:29:16.109+0000] {processor.py:157} INFO - Started process (PID=3289) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:29:16.111+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:29:16.114+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:29:16.113+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:29:16.171+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:29:16.246+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:29:16.246+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:29:16.274+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:29:16.274+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:29:16.320+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.220 seconds
[2025-06-16T20:29:47.107+0000] {processor.py:157} INFO - Started process (PID=3312) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:29:47.109+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:29:47.114+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:29:47.113+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:29:47.163+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:29:47.222+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:29:47.221+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:29:47.244+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:29:47.243+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:29:47.279+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.181 seconds
[2025-06-16T20:30:17.460+0000] {processor.py:157} INFO - Started process (PID=3335) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:30:17.463+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:30:17.465+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:30:17.464+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:30:17.690+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:30:17.916+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:30:17.916+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:30:17.948+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:30:17.947+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:30:18.006+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.554 seconds
[2025-06-16T20:30:48.332+0000] {processor.py:157} INFO - Started process (PID=3358) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:30:48.333+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:30:48.335+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:30:48.335+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:30:48.387+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:30:48.444+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:30:48.443+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:30:48.474+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:30:48.474+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:30:48.514+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.188 seconds
[2025-06-16T20:31:19.302+0000] {processor.py:157} INFO - Started process (PID=3381) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:31:19.304+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:31:19.306+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:31:19.305+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:31:19.352+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:31:19.404+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:31:19.404+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:31:19.424+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:31:19.424+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:31:19.453+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.157 seconds
[2025-06-16T20:31:49.688+0000] {processor.py:157} INFO - Started process (PID=3404) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:31:49.691+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:31:49.693+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:31:49.692+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:31:49.844+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:31:49.893+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:31:49.893+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:31:49.915+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:31:49.915+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:31:49.950+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.269 seconds
[2025-06-16T20:32:20.109+0000] {processor.py:157} INFO - Started process (PID=3426) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:32:20.112+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:32:20.115+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:32:20.114+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:32:20.182+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:32:20.244+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:32:20.244+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:32:20.270+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:32:20.270+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:32:20.330+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.232 seconds
[2025-06-16T20:32:50.481+0000] {processor.py:157} INFO - Started process (PID=3442) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:32:50.485+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:32:50.490+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:32:50.487+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:32:50.549+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:32:50.621+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:32:50.621+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:32:50.652+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:32:50.651+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:32:50.692+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.217 seconds
[2025-06-16T20:33:23.696+0000] {processor.py:157} INFO - Started process (PID=3465) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:33:23.710+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:33:23.746+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:33:23.745+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:33:25.089+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:33:25.340+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:33:25.339+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:33:25.479+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:33:25.479+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:33:25.747+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 2.057 seconds
[2025-06-16T20:33:56.040+0000] {processor.py:157} INFO - Started process (PID=3488) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:33:56.042+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:33:56.044+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:33:56.043+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:33:56.087+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:33:56.151+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:33:56.151+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:33:56.173+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:33:56.173+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:33:56.213+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.178 seconds
[2025-06-16T20:34:26.343+0000] {processor.py:157} INFO - Started process (PID=3511) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:34:26.347+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:34:26.349+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:34:26.348+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:34:26.443+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:34:26.511+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:34:26.510+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:34:26.543+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:34:26.543+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:34:26.602+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.265 seconds
[2025-06-16T20:34:57.451+0000] {processor.py:157} INFO - Started process (PID=3534) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:34:57.454+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:34:57.457+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:34:57.456+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:34:57.583+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:34:57.648+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:34:57.648+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:34:57.673+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:34:57.673+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:34:57.715+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.271 seconds
[2025-06-16T20:35:28.476+0000] {processor.py:157} INFO - Started process (PID=3557) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:35:28.500+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:35:28.516+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:35:28.514+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:35:28.815+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:35:29.047+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:35:29.046+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:35:29.122+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:35:29.121+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:35:29.228+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.773 seconds
[2025-06-16T20:35:59.562+0000] {processor.py:157} INFO - Started process (PID=3580) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:35:59.575+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:35:59.577+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:35:59.577+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:35:59.635+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:35:59.713+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:35:59.713+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:35:59.735+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:35:59.735+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:35:59.781+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.228 seconds
[2025-06-16T20:36:30.602+0000] {processor.py:157} INFO - Started process (PID=3603) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:36:30.604+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:36:30.607+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:36:30.606+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:36:30.683+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:36:30.763+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:36:30.763+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:36:30.785+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:36:30.785+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:36:30.824+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.230 seconds
[2025-06-16T20:37:01.845+0000] {processor.py:157} INFO - Started process (PID=3626) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:37:01.847+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:37:01.849+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:37:01.848+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:37:01.933+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:37:02.033+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:37:02.033+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:37:02.065+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:37:02.065+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:37:02.116+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.279 seconds
[2025-06-16T20:37:32.306+0000] {processor.py:157} INFO - Started process (PID=3650) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:37:32.309+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:37:32.311+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:37:32.311+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:37:32.380+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:37:32.450+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:37:32.450+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:37:32.487+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:37:32.486+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:37:32.546+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.245 seconds
[2025-06-16T20:38:03.077+0000] {processor.py:157} INFO - Started process (PID=3673) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:38:03.079+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:38:03.082+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:38:03.082+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:38:03.144+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:38:03.197+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:38:03.197+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:38:03.225+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:38:03.224+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:38:03.262+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.190 seconds
[2025-06-16T20:38:33.826+0000] {processor.py:157} INFO - Started process (PID=3696) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:38:33.829+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:38:33.832+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:38:33.831+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:38:33.900+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:38:33.956+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:38:33.955+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:38:33.976+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:38:33.976+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:38:34.024+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.211 seconds
[2025-06-16T20:39:04.778+0000] {processor.py:157} INFO - Started process (PID=3719) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:39:04.786+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:39:04.788+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:39:04.788+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:39:04.882+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:39:04.933+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:39:04.933+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:39:04.953+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:39:04.952+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:39:04.994+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.223 seconds
[2025-06-16T20:39:35.202+0000] {processor.py:157} INFO - Started process (PID=3734) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:39:35.204+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:39:35.208+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:39:35.207+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:39:35.284+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:39:35.363+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:39:35.363+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:39:35.409+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:39:35.409+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:39:35.466+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.271 seconds
[2025-06-16T20:40:06.417+0000] {processor.py:157} INFO - Started process (PID=3757) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:40:06.419+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:40:06.422+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:40:06.421+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:40:06.511+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:40:06.583+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:40:06.582+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:40:06.615+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:40:06.615+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:40:06.668+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.257 seconds
[2025-06-16T20:40:38.122+0000] {processor.py:157} INFO - Started process (PID=3779) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:40:38.129+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:40:38.135+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:40:38.134+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:40:38.427+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:40:38.508+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:40:38.507+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:40:38.535+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:40:38.535+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:40:38.611+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.495 seconds
[2025-06-16T20:41:09.352+0000] {processor.py:157} INFO - Started process (PID=3802) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:41:09.355+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:41:09.358+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:41:09.358+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:41:09.537+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:41:09.688+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:41:09.688+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:41:09.782+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:41:09.782+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:41:09.921+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.578 seconds
[2025-06-16T20:41:40.236+0000] {processor.py:157} INFO - Started process (PID=3825) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:41:40.239+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:41:40.241+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:41:40.241+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:41:40.329+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:41:40.387+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:41:40.386+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:41:40.409+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:41:40.409+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:41:40.452+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.223 seconds
[2025-06-16T20:42:10.917+0000] {processor.py:157} INFO - Started process (PID=3849) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:42:10.919+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:42:10.921+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:42:10.921+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:42:10.985+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:42:11.045+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:42:11.045+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:42:11.070+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:42:11.069+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:42:11.139+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.226 seconds
[2025-06-16T20:42:41.960+0000] {processor.py:157} INFO - Started process (PID=3872) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:42:41.962+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:42:41.964+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:42:41.964+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:42:42.020+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:42:42.073+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:42:42.073+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:42:42.134+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:42:42.134+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:42:42.183+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.230 seconds
[2025-06-16T20:43:12.334+0000] {processor.py:157} INFO - Started process (PID=3895) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:43:12.338+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:43:12.343+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:43:12.342+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:43:12.419+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:43:12.482+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:43:12.482+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:43:12.505+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:43:12.505+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:43:12.553+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.227 seconds
[2025-06-16T20:43:42.783+0000] {processor.py:157} INFO - Started process (PID=3918) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:43:42.786+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:43:42.789+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:43:42.788+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:43:42.856+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:43:42.928+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:43:42.927+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:43:42.957+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:43:42.956+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:43:43.014+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.236 seconds
[2025-06-16T20:44:13.183+0000] {processor.py:157} INFO - Started process (PID=3941) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:44:13.185+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:44:13.192+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:44:13.191+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:44:13.262+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:44:13.318+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:44:13.318+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:44:13.339+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:44:13.338+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:44:13.391+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.216 seconds
[2025-06-16T20:44:43.772+0000] {processor.py:157} INFO - Started process (PID=3964) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:44:43.775+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:44:43.778+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:44:43.777+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:44:43.843+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:44:43.893+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:44:43.892+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:44:43.913+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:44:43.913+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:44:43.956+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.192 seconds
[2025-06-16T20:45:14.158+0000] {processor.py:157} INFO - Started process (PID=3987) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:45:14.160+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:45:14.163+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:45:14.162+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:45:14.218+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:45:14.273+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:45:14.272+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:45:14.296+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:45:14.296+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:45:14.339+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.191 seconds
[2025-06-16T20:45:44.492+0000] {processor.py:157} INFO - Started process (PID=4002) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:45:44.494+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:45:44.496+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:45:44.496+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:45:44.543+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:45:44.589+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:45:44.588+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:45:44.612+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:45:44.611+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:45:44.659+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.175 seconds
[2025-06-16T20:46:15.638+0000] {processor.py:157} INFO - Started process (PID=4025) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:46:15.640+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:46:15.645+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:46:15.644+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:46:15.703+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:46:15.758+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:46:15.757+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:46:15.782+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:46:15.782+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:46:15.817+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.188 seconds
[2025-06-16T20:46:46.488+0000] {processor.py:157} INFO - Started process (PID=4048) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:46:46.490+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:46:46.493+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:46:46.492+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:46:46.549+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:46:46.601+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:46:46.600+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:46:46.624+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:46:46.623+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:46:46.661+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.178 seconds
[2025-06-16T20:47:16.796+0000] {processor.py:157} INFO - Started process (PID=4071) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:47:16.799+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:47:16.801+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:47:16.801+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:47:16.878+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:47:16.947+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:47:16.947+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:47:16.975+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:47:16.974+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:47:17.020+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.235 seconds
[2025-06-16T20:47:47.320+0000] {processor.py:157} INFO - Started process (PID=4094) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:47:47.322+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:47:47.324+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:47:47.324+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:47:47.391+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:47:47.457+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:47:47.456+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:47:47.489+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:47:47.488+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:47:47.547+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.232 seconds
[2025-06-16T20:48:19.044+0000] {processor.py:157} INFO - Started process (PID=4117) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:48:19.140+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:48:19.201+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:48:19.200+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:48:22.228+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:48:22.420+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:48:22.420+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:48:22.577+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:48:22.576+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:48:22.828+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 3.859 seconds
[2025-06-16T20:48:53.051+0000] {processor.py:157} INFO - Started process (PID=4140) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:48:53.053+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:48:53.055+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:48:53.054+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:48:53.104+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:48:53.157+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:48:53.156+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:48:53.179+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:48:53.178+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:48:53.229+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.184 seconds
[2025-06-16T20:49:24.036+0000] {processor.py:157} INFO - Started process (PID=4163) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:49:24.039+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:49:24.041+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:49:24.040+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:49:24.123+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:49:24.175+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:49:24.174+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:49:24.199+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:49:24.199+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:49:24.253+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.222 seconds
[2025-06-16T20:49:55.167+0000] {processor.py:157} INFO - Started process (PID=4186) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:49:55.169+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:49:55.171+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:49:55.171+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:49:55.256+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:49:55.322+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:49:55.322+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:49:55.346+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:49:55.345+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:49:55.399+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.238 seconds
[2025-06-16T20:50:26.342+0000] {processor.py:157} INFO - Started process (PID=4210) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:50:26.345+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:50:26.349+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:50:26.347+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:50:26.453+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:50:26.574+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:50:26.574+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:50:26.626+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:50:26.625+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:50:26.690+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.372 seconds
[2025-06-16T20:50:56.955+0000] {processor.py:157} INFO - Started process (PID=4234) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:50:56.958+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:50:56.960+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:50:56.960+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:50:57.026+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:50:57.085+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:50:57.084+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:50:57.115+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:50:57.114+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:50:57.158+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.211 seconds
[2025-06-16T20:51:28.110+0000] {processor.py:157} INFO - Started process (PID=4257) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:51:28.113+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:51:28.115+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:51:28.115+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:51:28.157+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:51:28.210+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:51:28.210+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:51:28.236+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:51:28.235+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:51:28.278+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.174 seconds
[2025-06-16T20:51:58.922+0000] {processor.py:157} INFO - Started process (PID=4280) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:51:58.931+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:51:58.933+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:51:58.932+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:51:58.998+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:51:59.064+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:51:59.064+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:51:59.094+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:51:59.094+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:51:59.157+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.242 seconds
[2025-06-16T20:52:29.413+0000] {processor.py:157} INFO - Started process (PID=4303) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:52:29.416+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:52:29.418+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:52:29.417+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:52:29.492+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:52:29.550+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:52:29.550+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:52:29.587+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:52:29.587+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:52:29.631+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.229 seconds
[2025-06-16T20:53:00.630+0000] {processor.py:157} INFO - Started process (PID=4326) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:53:00.633+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:53:00.636+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:53:00.636+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:53:00.725+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:53:00.788+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:53:00.788+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:53:00.822+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:53:00.822+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:53:00.865+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.240 seconds
[2025-06-16T20:53:31.632+0000] {processor.py:157} INFO - Started process (PID=4341) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:53:31.634+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:53:31.635+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:53:31.635+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:53:31.691+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:53:31.742+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:53:31.742+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:53:31.764+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:53:31.763+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:53:31.809+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.183 seconds
[2025-06-16T20:54:02.558+0000] {processor.py:157} INFO - Started process (PID=4364) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:54:02.560+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:54:02.561+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:54:02.561+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:54:02.619+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:54:02.676+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:54:02.676+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:54:02.699+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:54:02.698+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:54:02.745+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.193 seconds
[2025-06-16T20:54:33.568+0000] {processor.py:157} INFO - Started process (PID=4387) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:54:33.570+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:54:33.575+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:54:33.575+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:54:33.629+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:54:33.686+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:54:33.686+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:54:33.709+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:54:33.708+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:54:33.750+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.188 seconds
[2025-06-16T20:55:04.449+0000] {processor.py:157} INFO - Started process (PID=4410) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:55:04.452+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:55:04.454+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:55:04.453+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:55:04.540+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:55:04.627+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:55:04.627+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:55:04.653+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:55:04.653+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:55:04.697+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.257 seconds
[2025-06-16T20:55:35.344+0000] {processor.py:157} INFO - Started process (PID=4433) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:55:35.347+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:55:35.349+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:55:35.348+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:55:35.461+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:55:35.544+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:55:35.544+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:55:35.575+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:55:35.574+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:55:35.626+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.287 seconds
[2025-06-16T20:56:06.471+0000] {processor.py:157} INFO - Started process (PID=4456) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:56:06.473+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:56:06.477+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:56:06.475+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:56:06.552+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:56:06.632+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:56:06.626+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:56:06.655+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:56:06.655+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:56:06.690+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.227 seconds
[2025-06-16T20:56:37.187+0000] {processor.py:157} INFO - Started process (PID=4479) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:56:37.190+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:56:37.193+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:56:37.193+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:56:37.271+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:56:37.339+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:56:37.338+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:56:37.362+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:56:37.362+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:56:37.411+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.230 seconds
[2025-06-16T20:57:08.154+0000] {processor.py:157} INFO - Started process (PID=4502) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:57:08.156+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:57:08.158+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:57:08.158+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:57:08.211+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:57:08.262+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:57:08.262+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:57:08.285+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:57:08.284+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:57:08.325+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.178 seconds
[2025-06-16T20:57:38.763+0000] {processor.py:157} INFO - Started process (PID=4525) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:57:38.768+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:57:38.771+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:57:38.770+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:57:38.817+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:57:38.877+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:57:38.876+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:57:38.898+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:57:38.898+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:57:38.944+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.189 seconds
[2025-06-16T20:58:09.800+0000] {processor.py:157} INFO - Started process (PID=4548) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:58:09.802+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:58:09.804+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:58:09.804+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:58:09.869+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:58:09.972+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:58:09.972+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:58:10.004+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:58:10.004+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:58:10.064+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.274 seconds
[2025-06-16T20:58:40.580+0000] {processor.py:157} INFO - Started process (PID=4571) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:58:40.583+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:58:40.585+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:58:40.585+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:58:40.651+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:58:40.707+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:58:40.707+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:58:40.729+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:58:40.728+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:58:40.766+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.196 seconds
[2025-06-16T20:59:10.951+0000] {processor.py:157} INFO - Started process (PID=4594) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:59:10.953+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:59:10.954+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:59:10.954+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:59:11.000+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:59:11.058+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:59:11.058+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:59:11.093+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:59:11.093+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:59:11.128+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.184 seconds
[2025-06-16T20:59:42.049+0000] {processor.py:157} INFO - Started process (PID=4617) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:59:42.052+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T20:59:42.054+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:59:42.053+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:59:42.104+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T20:59:42.163+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:59:42.163+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T20:59:42.192+0000] {logging_mixin.py:151} INFO - [2025-06-16T20:59:42.192+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T20:59:42.232+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.188 seconds
[2025-06-16T21:00:13.089+0000] {processor.py:157} INFO - Started process (PID=4640) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:00:13.092+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:00:13.094+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:00:13.093+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:00:13.151+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:00:13.209+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:00:13.209+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:00:13.243+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:00:13.242+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:00:13.303+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.224 seconds
[2025-06-16T21:00:43.987+0000] {processor.py:157} INFO - Started process (PID=4655) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:00:43.989+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:00:43.991+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:00:43.990+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:00:44.046+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:00:44.096+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:00:44.096+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:00:44.117+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:00:44.117+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:00:44.157+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.175 seconds
[2025-06-16T21:01:14.983+0000] {processor.py:157} INFO - Started process (PID=4678) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:01:14.985+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:01:14.987+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:01:14.987+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:01:15.036+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:01:15.090+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:01:15.090+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:01:15.112+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:01:15.112+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:01:15.149+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.173 seconds
[2025-06-16T21:01:45.299+0000] {processor.py:157} INFO - Started process (PID=4701) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:01:45.303+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:01:45.305+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:01:45.304+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:01:45.373+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:01:45.431+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:01:45.430+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:01:45.454+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:01:45.454+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:01:45.502+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.208 seconds
[2025-06-16T21:02:15.847+0000] {processor.py:157} INFO - Started process (PID=4724) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:02:15.849+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:02:15.850+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:02:15.850+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:02:15.893+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:02:15.949+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:02:15.949+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:02:15.972+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:02:15.971+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:02:16.008+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.166 seconds
[2025-06-16T21:02:46.523+0000] {processor.py:157} INFO - Started process (PID=4747) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:02:46.524+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:02:46.526+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:02:46.526+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:02:46.579+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:02:46.639+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:02:46.638+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:02:46.662+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:02:46.662+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:02:46.701+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.184 seconds
[2025-06-16T21:03:19.128+0000] {processor.py:157} INFO - Started process (PID=4770) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:03:19.178+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:03:19.208+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:03:19.207+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:03:20.806+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:03:21.277+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:03:21.276+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:03:21.480+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:03:21.480+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:03:22.061+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 3.027 seconds
[2025-06-16T21:03:52.612+0000] {processor.py:157} INFO - Started process (PID=4793) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:03:52.616+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:03:52.621+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:03:52.620+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:03:52.775+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:03:52.866+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:03:52.865+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:03:52.900+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:03:52.899+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:03:52.960+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.355 seconds
[2025-06-16T21:04:23.361+0000] {processor.py:157} INFO - Started process (PID=4816) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:04:23.364+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:04:23.366+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:04:23.366+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:04:23.455+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:04:23.543+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:04:23.542+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:04:23.575+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:04:23.575+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:04:23.642+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.287 seconds
[2025-06-16T21:04:53.854+0000] {processor.py:157} INFO - Started process (PID=4839) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:04:53.858+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:04:53.860+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:04:53.860+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:04:53.936+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:04:54.000+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:04:54.000+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:04:54.035+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:04:54.035+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:04:54.085+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.238 seconds
[2025-06-16T21:05:25.171+0000] {processor.py:157} INFO - Started process (PID=4863) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:05:25.173+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:05:25.175+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:05:25.175+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:05:25.225+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:05:25.291+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:05:25.290+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:05:25.314+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:05:25.313+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:05:25.346+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.180 seconds
[2025-06-16T21:05:55.666+0000] {processor.py:157} INFO - Started process (PID=4886) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:05:55.667+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:05:55.670+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:05:55.670+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:05:55.746+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:05:55.817+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:05:55.816+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:05:55.842+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:05:55.841+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:05:55.887+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.230 seconds
[2025-06-16T21:06:26.555+0000] {processor.py:157} INFO - Started process (PID=4909) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:06:26.557+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:06:26.559+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:06:26.559+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:06:26.611+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:06:26.674+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:06:26.674+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:06:26.701+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:06:26.700+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:06:26.748+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.205 seconds
[2025-06-16T21:06:57.432+0000] {processor.py:157} INFO - Started process (PID=4924) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:06:57.434+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:06:57.436+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:06:57.436+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:06:57.494+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:06:57.548+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:06:57.547+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:06:57.570+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:06:57.569+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:06:57.616+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.188 seconds
[2025-06-16T21:07:28.258+0000] {processor.py:157} INFO - Started process (PID=4947) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:07:28.260+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:07:28.262+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:07:28.262+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:07:28.323+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:07:28.374+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:07:28.374+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:07:28.399+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:07:28.398+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:07:28.439+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.186 seconds
[2025-06-16T21:07:59.255+0000] {processor.py:157} INFO - Started process (PID=4971) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:07:59.257+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:07:59.259+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:07:59.258+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:07:59.319+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:07:59.369+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:07:59.369+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:07:59.394+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:07:59.394+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:07:59.441+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.191 seconds
[2025-06-16T21:08:30.103+0000] {processor.py:157} INFO - Started process (PID=4994) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:08:30.104+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:08:30.107+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:08:30.106+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:08:30.170+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:08:30.285+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:08:30.284+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:08:30.331+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:08:30.330+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:08:30.386+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.291 seconds
[2025-06-16T21:09:00.524+0000] {processor.py:157} INFO - Started process (PID=5017) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:09:00.526+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:09:00.527+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:09:00.527+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:09:00.577+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:09:00.645+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:09:00.645+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:09:00.667+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:09:00.667+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:09:00.713+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.195 seconds
[2025-06-16T21:09:31.362+0000] {processor.py:157} INFO - Started process (PID=5041) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:09:31.369+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:09:31.373+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:09:31.370+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:09:31.433+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:09:31.484+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:09:31.484+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:09:31.505+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:09:31.505+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:09:31.541+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.186 seconds
[2025-06-16T21:10:02.204+0000] {processor.py:157} INFO - Started process (PID=5064) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:10:02.206+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:10:02.208+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:10:02.207+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:10:02.286+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:10:02.341+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:10:02.341+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:10:02.377+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:10:02.377+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:10:02.425+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.231 seconds
[2025-06-16T21:10:33.357+0000] {processor.py:157} INFO - Started process (PID=5087) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:10:33.360+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:10:33.362+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:10:33.362+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:10:33.438+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:10:33.505+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:10:33.505+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:10:33.532+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:10:33.532+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:10:33.582+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.230 seconds
[2025-06-16T21:11:04.414+0000] {processor.py:157} INFO - Started process (PID=5110) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:11:04.416+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:11:04.422+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:11:04.419+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:11:04.478+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:11:04.540+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:11:04.540+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:11:04.564+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:11:04.564+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:11:04.607+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.200 seconds
[2025-06-16T21:11:35.381+0000] {processor.py:157} INFO - Started process (PID=5133) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:11:35.384+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:11:35.387+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:11:35.387+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:11:35.443+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:11:35.498+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:11:35.498+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:11:35.521+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:11:35.521+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:11:35.570+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.194 seconds
[2025-06-16T21:12:06.465+0000] {processor.py:157} INFO - Started process (PID=5156) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:12:06.467+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:12:06.469+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:12:06.468+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:12:06.531+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:12:06.606+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:12:06.605+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:12:06.639+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:12:06.638+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:12:06.688+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.229 seconds
[2025-06-16T21:12:37.565+0000] {processor.py:157} INFO - Started process (PID=5179) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:12:37.567+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:12:37.569+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:12:37.568+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:12:37.614+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:12:37.666+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:12:37.666+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:12:37.686+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:12:37.685+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:12:37.716+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.157 seconds
[2025-06-16T21:13:07.907+0000] {processor.py:157} INFO - Started process (PID=5202) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:13:07.909+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:13:07.911+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:13:07.910+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:13:07.962+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:13:08.014+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:13:08.013+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:13:08.035+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:13:08.035+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:13:08.080+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.180 seconds
[2025-06-16T21:13:38.804+0000] {processor.py:157} INFO - Started process (PID=5225) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:13:38.808+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:13:38.811+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:13:38.810+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:13:38.869+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:13:38.939+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:13:38.939+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:13:38.971+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:13:38.970+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:13:39.016+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.224 seconds
[2025-06-16T21:14:09.870+0000] {processor.py:157} INFO - Started process (PID=5240) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:14:09.872+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:14:09.874+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:14:09.874+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:14:09.940+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:14:10.010+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:14:10.010+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:14:10.032+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:14:10.031+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:14:10.067+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.202 seconds
[2025-06-16T21:14:40.736+0000] {processor.py:157} INFO - Started process (PID=5263) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:14:40.738+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:14:40.739+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:14:40.739+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:14:40.791+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:14:40.852+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:14:40.852+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:14:40.879+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:14:40.878+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:14:40.917+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.187 seconds
[2025-06-16T21:15:11.602+0000] {processor.py:157} INFO - Started process (PID=5286) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:15:11.604+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:15:11.606+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:15:11.605+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:15:11.654+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:15:11.712+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:15:11.711+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:15:11.736+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:15:11.736+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:15:11.790+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.194 seconds
[2025-06-16T21:15:42.461+0000] {processor.py:157} INFO - Started process (PID=5309) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:15:42.463+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:15:42.465+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:15:42.464+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:15:42.514+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:15:42.570+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:15:42.570+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:15:42.592+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:15:42.592+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:15:42.633+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.177 seconds
[2025-06-16T21:16:13.248+0000] {processor.py:157} INFO - Started process (PID=5332) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:16:13.251+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:16:13.254+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:16:13.254+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:16:13.306+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:16:13.359+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:16:13.358+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:16:13.382+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:16:13.381+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:16:13.428+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.186 seconds
[2025-06-16T21:16:44.085+0000] {processor.py:157} INFO - Started process (PID=5355) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:16:44.087+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:16:44.090+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:16:44.089+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:16:44.133+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:16:44.188+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:16:44.188+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:16:44.212+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:16:44.211+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:16:44.256+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.177 seconds
[2025-06-16T21:17:14.859+0000] {processor.py:157} INFO - Started process (PID=5378) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:17:14.861+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:17:14.863+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:17:14.863+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:17:14.922+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:17:14.979+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:17:14.978+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:17:15.001+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:17:15.000+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:17:15.038+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.185 seconds
[2025-06-16T21:17:45.163+0000] {processor.py:157} INFO - Started process (PID=5401) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:17:45.165+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:17:45.167+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:17:45.166+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:17:45.217+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:17:45.266+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:17:45.266+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:17:45.292+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:17:45.292+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:17:45.351+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.194 seconds
[2025-06-16T21:18:16.061+0000] {processor.py:157} INFO - Started process (PID=5424) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:18:16.079+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:18:16.094+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:18:16.093+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:18:16.886+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:18:17.048+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:18:17.048+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:18:17.153+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:18:17.152+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:18:17.312+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 1.264 seconds
[2025-06-16T21:18:47.623+0000] {processor.py:157} INFO - Started process (PID=5447) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:18:47.626+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:18:47.630+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:18:47.629+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:18:47.702+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:18:47.758+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:18:47.758+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:18:47.781+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:18:47.781+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:18:47.822+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.206 seconds
[2025-06-16T21:19:17.969+0000] {processor.py:157} INFO - Started process (PID=5470) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:19:17.971+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:19:17.973+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:19:17.973+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:19:18.014+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:19:18.065+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:19:18.065+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:19:18.090+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:19:18.090+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:19:18.130+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.167 seconds
[2025-06-16T21:19:48.289+0000] {processor.py:157} INFO - Started process (PID=5493) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:19:48.291+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:19:48.294+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:19:48.293+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:19:48.355+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:19:48.398+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:19:48.398+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:19:48.422+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:19:48.422+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:19:48.463+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.181 seconds
[2025-06-16T21:20:19.296+0000] {processor.py:157} INFO - Started process (PID=5516) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:20:19.299+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:20:19.301+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:20:19.301+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:20:19.352+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:20:19.405+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:20:19.405+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:20:19.428+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:20:19.427+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:20:19.471+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.184 seconds
[2025-06-16T21:20:49.676+0000] {processor.py:157} INFO - Started process (PID=5531) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:20:49.679+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:20:49.680+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:20:49.680+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:20:49.739+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:20:49.801+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:20:49.801+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:20:49.826+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:20:49.825+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:20:49.863+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.193 seconds
[2025-06-16T21:21:20.090+0000] {processor.py:157} INFO - Started process (PID=5554) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:21:20.092+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:21:20.095+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:21:20.094+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:21:20.153+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:21:20.207+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:21:20.206+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:21:20.234+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:21:20.233+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:21:20.280+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.196 seconds
[2025-06-16T21:21:50.815+0000] {processor.py:157} INFO - Started process (PID=5577) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:21:50.817+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:21:50.820+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:21:50.819+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:21:50.876+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:21:50.939+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:21:50.939+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:21:50.961+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:21:50.960+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:21:51.013+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.204 seconds
[2025-06-16T21:22:21.824+0000] {processor.py:157} INFO - Started process (PID=5600) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:22:21.827+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:22:21.829+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:22:21.829+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:22:21.906+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:22:21.968+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:22:21.968+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:22:21.993+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:22:21.993+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:22:22.258+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.440 seconds
[2025-06-16T21:22:52.582+0000] {processor.py:157} INFO - Started process (PID=5623) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:22:52.584+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:22:52.585+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:22:52.585+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:22:52.630+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:22:52.679+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:22:52.678+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:22:52.701+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:22:52.701+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:22:52.736+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.160 seconds
[2025-06-16T21:23:23.457+0000] {processor.py:157} INFO - Started process (PID=5646) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:23:23.460+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:23:23.462+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:23:23.462+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:23:23.535+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:23:23.589+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:23:23.589+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:23:23.845+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:23:23.845+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:23:23.893+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.444 seconds
[2025-06-16T21:23:54.094+0000] {processor.py:157} INFO - Started process (PID=5669) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:23:54.097+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:23:54.099+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:23:54.098+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:23:54.154+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:23:54.212+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:23:54.212+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:23:54.236+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:23:54.235+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:23:54.289+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.204 seconds
[2025-06-16T21:24:24.534+0000] {processor.py:157} INFO - Started process (PID=5692) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:24:24.536+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:24:24.539+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:24:24.538+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:24:24.601+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:24:24.866+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:24:24.866+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:24:24.882+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:24:24.882+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:24:24.924+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.396 seconds
[2025-06-16T21:24:55.220+0000] {processor.py:157} INFO - Started process (PID=5715) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:24:55.222+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:24:55.225+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:24:55.224+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:24:55.277+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:24:55.336+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:24:55.336+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:24:55.362+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:24:55.361+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:24:55.409+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.197 seconds
[2025-06-16T21:25:25.788+0000] {processor.py:157} INFO - Started process (PID=5738) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:25:25.791+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:25:25.794+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:25:25.793+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:25:25.877+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:25:26.164+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:25:26.163+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:25:26.192+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:25:26.192+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:25:26.249+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.469 seconds
[2025-06-16T21:25:56.552+0000] {processor.py:157} INFO - Started process (PID=5761) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:25:56.554+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:25:56.557+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:25:56.556+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:25:56.617+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:25:56.674+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:25:56.673+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:25:56.702+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:25:56.702+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:25:56.745+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.200 seconds
[2025-06-16T21:26:27.290+0000] {processor.py:157} INFO - Started process (PID=5784) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:26:27.293+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:26:27.295+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:26:27.294+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:26:27.338+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:26:27.390+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:26:27.390+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:26:27.410+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:26:27.410+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:26:27.450+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.165 seconds
[2025-06-16T21:26:58.406+0000] {processor.py:157} INFO - Started process (PID=5807) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:26:58.408+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:26:58.412+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:26:58.412+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:26:58.474+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:26:58.533+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:26:58.533+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:26:58.561+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:26:58.560+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:26:58.605+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.209 seconds
[2025-06-16T21:27:28.796+0000] {processor.py:157} INFO - Started process (PID=5822) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:27:28.798+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:27:28.800+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:27:28.800+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:27:28.862+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:27:28.920+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:27:28.920+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:27:28.939+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:27:28.938+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:27:28.980+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.190 seconds
[2025-06-16T21:27:59.192+0000] {processor.py:157} INFO - Started process (PID=5845) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:27:59.194+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:27:59.196+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:27:59.196+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:27:59.255+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:27:59.320+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:27:59.319+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:27:59.347+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:27:59.346+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:27:59.410+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.224 seconds
[2025-06-16T21:28:30.158+0000] {processor.py:157} INFO - Started process (PID=5868) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:28:30.160+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:28:30.162+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:28:30.162+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:28:30.209+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:28:30.279+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:28:30.278+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:28:30.304+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:28:30.304+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:28:30.342+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.192 seconds
[2025-06-16T21:29:01.221+0000] {processor.py:157} INFO - Started process (PID=5891) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:29:01.223+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:29:01.226+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:29:01.225+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:29:01.276+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:29:01.349+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:29:01.349+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:29:01.378+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:29:01.378+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:29:01.413+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.199 seconds
[2025-06-16T21:29:32.152+0000] {processor.py:157} INFO - Started process (PID=5914) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:29:32.155+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:29:32.156+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:29:32.156+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:29:32.216+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:29:32.271+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:29:32.270+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:29:32.294+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:29:32.294+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:29:32.336+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.189 seconds
[2025-06-16T21:30:03.285+0000] {processor.py:157} INFO - Started process (PID=5937) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:30:03.287+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:30:03.288+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:30:03.288+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:30:03.342+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:30:03.393+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:30:03.393+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:30:03.417+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:30:03.416+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:30:03.457+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.177 seconds
[2025-06-16T21:30:33.827+0000] {processor.py:157} INFO - Started process (PID=5960) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:30:33.830+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:30:33.832+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:30:33.831+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:30:33.887+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:30:33.937+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:30:33.937+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:30:33.959+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:30:33.959+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:30:34.011+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.191 seconds
[2025-06-16T21:31:04.770+0000] {processor.py:157} INFO - Started process (PID=5983) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:31:04.772+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:31:04.774+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:31:04.773+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:31:04.817+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:31:04.871+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:31:04.871+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:31:04.895+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:31:04.895+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:31:04.930+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.166 seconds
[2025-06-16T21:31:35.760+0000] {processor.py:157} INFO - Started process (PID=6006) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:31:35.762+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:31:35.765+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:31:35.764+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:31:35.832+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:31:35.894+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:31:35.894+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:31:35.923+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:31:35.922+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:31:35.957+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.202 seconds
[2025-06-16T21:32:06.599+0000] {processor.py:157} INFO - Started process (PID=6029) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:32:06.601+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:32:06.603+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:32:06.602+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:32:06.660+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:32:06.708+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:32:06.707+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:32:06.728+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:32:06.728+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:32:06.775+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.183 seconds
[2025-06-16T21:32:36.937+0000] {processor.py:157} INFO - Started process (PID=6052) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:32:36.939+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:32:36.941+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:32:36.940+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:32:36.984+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:32:37.032+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:32:37.032+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:32:37.054+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:32:37.054+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:32:37.096+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.165 seconds
[2025-06-16T21:33:07.694+0000] {processor.py:157} INFO - Started process (PID=6075) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:33:07.697+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:33:07.703+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:33:07.703+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:33:07.819+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:33:07.867+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:33:07.867+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:33:07.889+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:33:07.889+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:33:07.940+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.251 seconds
[2025-06-16T21:33:38.474+0000] {processor.py:157} INFO - Started process (PID=6098) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:33:38.477+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-16T21:33:38.480+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:33:38.479+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:33:38.582+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-16T21:33:38.664+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:33:38.664+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-16T21:33:38.699+0000] {logging_mixin.py:151} INFO - [2025-06-16T21:33:38.699+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-16T21:33:38.751+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.286 seconds
