"""
BioShort Automated Video Generation DAG
Runs daily to process one person from the queue and upload to YouTube
"""

from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.bash import BashOperator
import requests
import json
import time

# Configuration
BIOSHORT_API_BASE = "http://backend:5000/api"  # Use Docker service name
DEFAULT_ARGS = {
    'owner': 'bioshort',
    'depends_on_past': False,
    'start_date': datetime(2024, 1, 1),
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}

# Create DAG
dag = DAG(
    'bioshort_automation',
    default_args=DEFAULT_ARGS,
    description='Automated BioShort video generation and YouTube upload',
    schedule_interval='0 9 * * *',  # Run daily at 9 AM
    catchup=False,
    max_active_runs=1,
    tags=['bioshort', 'automation', 'youtube']
)

def check_queue_and_get_next_person(**context):
    """Check if there are people in queue and get the next one to process"""
    try:
        print("🔍 Checking queue for next person to process...")
        
        response = requests.get(f"{BIOSHORT_API_BASE}/queue/next")
        response.raise_for_status()
        
        data = response.json()
        
        if data['success'] and data.get('person'):
            person = data['person']
            print(f"✅ Found person to process: {person['name']}")
            print(f"   Title Language: {person['title_language']}")
            print(f"   Description Language: {person['description_language']}")
            
            # Store person data for next tasks
            context['task_instance'].xcom_push(key='person_data', value=person)
            return 'generate_video'
        else:
            print("ℹ️ No pending people in queue. Skipping processing.")
            return 'no_processing_needed'
            
    except Exception as e:
        print(f"❌ Error checking queue: {str(e)}")
        raise

def generate_video_for_person(**context):
    """Generate video for the selected person"""
    try:
        # Get person data from previous task
        person = context['task_instance'].xcom_pull(key='person_data')
        if not person:
            raise ValueError("No person data found")
        
        person_id = person['id']
        person_name = person['name']
        audio_language = person.get('audio_language', 'hindi')
        voice_type = person.get('voice_type', 'hindi_male')

        print(f"🎬 Starting video generation for: {person_name}")
        print(f"   Audio Language: {audio_language}")
        print(f"   Voice Type: {voice_type}")

        # Update status to processing
        requests.post(f"{BIOSHORT_API_BASE}/queue/update-status", json={
            'person_id': person_id,
            'status': 'processing'
        })

        # Generate video with specified audio settings
        video_data = {
            'person_name': person_name,
            'voice_type': voice_type,
            'language': audio_language  # Use audio language for story generation
        }
        
        print(f"📡 Sending video generation request...")
        response = requests.post(f"{BIOSHORT_API_BASE}/generate-video", json=video_data, timeout=600)  # 10 minute timeout
        response.raise_for_status()
        
        result = response.json()
        
        if result['success']:
            print(f"✅ Video generated successfully for {person_name}")
            
            # Store video data for YouTube upload
            context['task_instance'].xcom_push(key='video_data', value={
                'person': person,
                'video': result['video'],
                'story': result['story']
            })
            
            return 'upload_to_youtube'
        else:
            error_msg = result.get('error', 'Unknown error')
            print(f"❌ Video generation failed: {error_msg}")
            
            # Update status to failed
            requests.post(f"{BIOSHORT_API_BASE}/queue/update-status", json={
                'person_id': person_id,
                'status': 'failed',
                'error_message': error_msg
            })
            
            raise Exception(f"Video generation failed: {error_msg}")
            
    except Exception as e:
        print(f"❌ Error in video generation: {str(e)}")
        raise

def upload_to_youtube(**context):
    """Upload the generated video to YouTube"""
    try:
        # Get video data from previous task
        video_data = context['task_instance'].xcom_pull(key='video_data')
        if not video_data:
            raise ValueError("No video data found")
        
        person = video_data['person']
        video = video_data['video']
        story = video_data['story']
        
        person_id = person['id']
        person_name = person['name']
        title_language = person['title_language']
        description_language = person['description_language']
        
        print(f"📺 Starting YouTube upload for: {person_name}")
        print(f"   Title Language: {title_language}")
        print(f"   Description Language: {description_language}")
        
        # Generate YouTube content in the specified languages
        youtube_content_data = {
            'person_name': person_name,
            'language': title_language,  # Use title language for YouTube content
            'story_data': story
        }
        
        print(f"🎯 Generating YouTube content...")
        response = requests.post(f"{BIOSHORT_API_BASE}/generate-youtube-content", json=youtube_content_data)
        response.raise_for_status()
        
        youtube_result = response.json()
        
        if not youtube_result['success']:
            raise Exception(f"YouTube content generation failed: {youtube_result.get('error')}")
        
        # Prepare upload data
        title = youtube_result['titles'][0] if youtube_result['titles'] else f"{person_name} - AI Biography"
        description = youtube_result['description']
        
        # If description language is different from title language, regenerate description
        if description_language != title_language:
            print(f"🔄 Regenerating description in {description_language}...")
            desc_content_data = {
                'person_name': person_name,
                'language': description_language,
                'story_data': story
            }
            
            desc_response = requests.post(f"{BIOSHORT_API_BASE}/generate-youtube-content", json=desc_content_data)
            desc_response.raise_for_status()
            desc_result = desc_response.json()
            
            if desc_result['success']:
                description = desc_result['description']
        
        upload_data = {
            'video_base64': video['base64'],
            'title': title,
            'description': description,
            'privacy_status': 'private',  # Always upload as private initially
            'tags': ['biography', 'ai', 'inspiration', person_name.lower().replace(' ', '')]
        }
        
        print(f"📤 Uploading to YouTube...")
        print(f"   Title: {title[:50]}...")
        print(f"   Privacy: private")
        
        response = requests.post(f"{BIOSHORT_API_BASE}/upload-to-youtube", json=upload_data, timeout=900)  # 15 minute timeout
        response.raise_for_status()
        
        upload_result = response.json()
        
        if upload_result['success']:
            video_url = upload_result['video_url']
            print(f"✅ Successfully uploaded to YouTube: {video_url}")
            
            # Update status to completed
            requests.post(f"{BIOSHORT_API_BASE}/queue/update-status", json={
                'person_id': person_id,
                'status': 'completed',
                'video_url': video_url
            })
            
            print(f"🎉 Automation completed for {person_name}")
            
        else:
            error_msg = upload_result.get('error', 'Unknown upload error')
            print(f"❌ YouTube upload failed: {error_msg}")
            
            # Update status to failed
            requests.post(f"{BIOSHORT_API_BASE}/queue/update-status", json={
                'person_id': person_id,
                'status': 'failed',
                'error_message': f"Upload failed: {error_msg}"
            })
            
            raise Exception(f"YouTube upload failed: {error_msg}")
            
    except Exception as e:
        print(f"❌ Error in YouTube upload: {str(e)}")
        raise

def no_processing_needed(**context):
    """Task that runs when no processing is needed"""
    print("ℹ️ No people in queue. Automation task completed with no action.")
    return "No processing needed"

# Define tasks
check_queue_task = PythonOperator(
    task_id='check_queue',
    python_callable=check_queue_and_get_next_person,
    dag=dag
)

generate_video_task = PythonOperator(
    task_id='generate_video',
    python_callable=generate_video_for_person,
    dag=dag
)

upload_youtube_task = PythonOperator(
    task_id='upload_to_youtube',
    python_callable=upload_to_youtube,
    dag=dag
)

no_processing_task = PythonOperator(
    task_id='no_processing_needed',
    python_callable=no_processing_needed,
    dag=dag
)

# Define task dependencies
check_queue_task >> [generate_video_task, no_processing_task]
generate_video_task >> upload_youtube_task
