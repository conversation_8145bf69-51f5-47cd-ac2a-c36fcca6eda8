import { useKindeAuth } from '@kinde-oss/kinde-auth-react';

// API base URL
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

// Create authenticated fetch function
export const useAuthenticatedFetch = () => {
  const { getToken } = useKindeAuth();

  const authenticatedFetch = async (url, options = {}) => {
    try {
      // Get access token
      const token = await getToken();
      
      // Prepare headers
      const headers = {
        'Content-Type': 'application/json',
        ...options.headers,
      };

      // Add authorization header if token exists
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }

      // Make request
      const response = await fetch(`${API_BASE_URL}${url}`, {
        ...options,
        headers,
      });

      return response;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  };

  return authenticatedFetch;
};

// Simple fetch for public endpoints (health check, etc.)
export const publicFetch = async (url, options = {}) => {
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  const response = await fetch(`${API_BASE_URL}${url}`, {
    ...options,
    headers,
  });

  return response;
};

export { API_BASE_URL };
