"""
Database Cleanup DAG for BioShort
Manually triggered DAG to clean all database data
"""

from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.python import PythonOperator
import requests
import sqlite3

# Configuration
BIOSHORT_API_BASE = "http://backend:5000/api"  # Use Docker service name
DEFAULT_ARGS = {
    'owner': 'bioshort',
    'depends_on_past': False,
    'start_date': datetime(2024, 1, 1),
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 0,  # No retries for cleanup operations
}

# Create DAG - PAUSED BY DEFAULT
dag = DAG(
    'database_cleanup',
    default_args=DEFAULT_ARGS,
    description='Manual database cleanup - clears all BioShort queue data',
    schedule_interval=None,  # Manual trigger only
    catchup=False,
    max_active_runs=1,
    is_paused_upon_creation=True,  # PAUSED BY DEFAULT
    tags=['bioshort', 'cleanup', 'manual']
)

def confirm_cleanup_intent(**context):
    """
    Confirmation step - logs what will be cleaned
    """
    print("🚨 DATABASE CLEANUP CONFIRMATION")
    print("=" * 50)
    print("This operation will PERMANENTLY DELETE:")
    print("✅ All people from automation queue")
    print("✅ All processing history")
    print("✅ All completed/failed records")
    print("✅ All YouTube URLs stored")
    print("✅ All error messages")
    print("=" * 50)
    print("⚠️  This action CANNOT be undone!")
    print("⚠️  Make sure you want to proceed!")
    print("=" * 50)
    
    # Get current queue status first
    try:
        response = requests.get(f"{BIOSHORT_API_BASE}/queue")
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                queue_count = data['count']
                print(f"📊 Current queue contains: {queue_count} people")
                
                if queue_count > 0:
                    print("📋 People in queue:")
                    for person in data['queue']:
                        status_emoji = {
                            'pending': '⏳',
                            'processing': '🔄', 
                            'completed': '✅',
                            'failed': '❌'
                        }.get(person['status'], '❓')
                        print(f"   {status_emoji} {person['name']} ({person['status']})")
                else:
                    print("📭 Queue is already empty")
            else:
                print(f"❌ Failed to get queue status: {data.get('error')}")
        else:
            print(f"❌ Failed to connect to API: {response.status_code}")
    except Exception as e:
        print(f"❌ Error checking queue status: {str(e)}")
    
    print("=" * 50)
    print("✅ Confirmation complete - proceeding to cleanup...")
    return "cleanup_confirmed"

def cleanup_bioshort_database(**context):
    """
    Clean all data from BioShort SQLite database
    """
    try:
        print("🧹 Starting BioShort database cleanup...")
        
        # Get current counts before cleanup
        response = requests.get(f"{BIOSHORT_API_BASE}/queue")
        initial_count = 0
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                initial_count = data['count']
        
        print(f"📊 Found {initial_count} records to clean")
        
        # Clear the queue via API (safer than direct DB access)
        if initial_count > 0:
            # Get all people IDs
            queue_data = data['queue']
            deleted_count = 0
            
            for person in queue_data:
                person_id = person['id']
                person_name = person['name']
                
                print(f"🗑️ Deleting: {person_name} (ID: {person_id})")
                
                # Delete via API
                delete_response = requests.delete(f"{BIOSHORT_API_BASE}/queue/remove/{person_id}")
                
                if delete_response.status_code == 200:
                    delete_result = delete_response.json()
                    if delete_result['success']:
                        deleted_count += 1
                        print(f"   ✅ Deleted successfully")
                    else:
                        print(f"   ❌ Failed to delete: {delete_result.get('error')}")
                else:
                    print(f"   ❌ API error: {delete_response.status_code}")
            
            print(f"🧹 Deleted {deleted_count} out of {initial_count} records")
        else:
            print("📭 No records to delete - database already clean")
        
        # Verify cleanup
        verify_response = requests.get(f"{BIOSHORT_API_BASE}/queue")
        if verify_response.status_code == 200:
            verify_data = verify_response.json()
            if verify_data['success']:
                remaining_count = verify_data['count']
                if remaining_count == 0:
                    print("✅ Database cleanup completed successfully!")
                    print("📭 Queue is now empty")
                else:
                    print(f"⚠️ Warning: {remaining_count} records still remain")
            else:
                print(f"❌ Failed to verify cleanup: {verify_data.get('error')}")
        
        return "cleanup_completed"
        
    except Exception as e:
        print(f"❌ Error during database cleanup: {str(e)}")
        raise

def cleanup_summary(**context):
    """
    Provide cleanup summary and next steps
    """
    print("🎉 DATABASE CLEANUP SUMMARY")
    print("=" * 50)
    print("✅ BioShort queue database has been cleared")
    print("✅ All automation queue data removed")
    print("✅ All processing history cleared")
    print("✅ All YouTube URLs removed")
    print("=" * 50)
    print("📋 What was NOT affected:")
    print("   • Airflow execution history (this DAG run)")
    print("   • Docker containers and images")
    print("   • Your .env configuration")
    print("   • YouTube videos (still online)")
    print("=" * 50)
    print("🚀 Next steps:")
    print("   • Add new people to automation queue")
    print("   • Resume normal automation operations")
    print("   • Check queue status in frontend")
    print("=" * 50)
    print("✨ Cleanup completed successfully!")
    
    return "cleanup_finished"

# Define tasks
confirm_task = PythonOperator(
    task_id='confirm_cleanup',
    python_callable=confirm_cleanup_intent,
    dag=dag
)

cleanup_task = PythonOperator(
    task_id='cleanup_database',
    python_callable=cleanup_bioshort_database,
    dag=dag
)

summary_task = PythonOperator(
    task_id='cleanup_summary',
    python_callable=cleanup_summary,
    dag=dag
)

# Define task dependencies
confirm_task >> cleanup_task >> summary_task
