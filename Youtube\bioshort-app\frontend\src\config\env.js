// Environment Configuration
// Centralized environment variable management for the frontend

/**
 * Get environment variable with fallback
 * @param {string} key - Environment variable key
 * @param {string} fallback - Fallback value if env var is not found
 * @returns {string} Environment variable value or fallback
 */
const getEnvVar = (key, fallback = '') => {
  // Try to get from Vite's import.meta.env first
  if (typeof import.meta !== 'undefined' && import.meta.env) {
    const value = import.meta.env[key]
    if (value !== undefined && value !== null && value !== '') {
      return value
    }
  }
  
  // Try to get from process.env (for Node.js environments)
  if (typeof process !== 'undefined' && process.env) {
    const value = process.env[key]
    if (value !== undefined && value !== null && value !== '') {
      return value
    }
  }
  
  // Try to get from window environment (if set by server)
  if (typeof window !== 'undefined' && window.env) {
    const value = window.env[key]
    if (value !== undefined && value !== null && value !== '') {
      return value
    }
  }
  
  return fallback
}

/**
 * Environment configuration object
 */
export const env = {
  // API Configuration
  API_BASE_URL: getEnvVar('VITE_API_BASE_URL', 'http://localhost:5000/api'),
  
  // Development flags
  NODE_ENV: getEnvVar('NODE_ENV', 'development'),
  DEV: getEnvVar('NODE_ENV', 'development') === 'development',
  PROD: getEnvVar('NODE_ENV', 'development') === 'production',
  
  // Debug flags
  DEBUG: getEnvVar('VITE_DEBUG', 'false') === 'true',
  VERBOSE_LOGGING: getEnvVar('VITE_VERBOSE_LOGGING', 'false') === 'true',
  
  // Feature flags
  ENABLE_AUTH: getEnvVar('VITE_ENABLE_AUTH', 'true') === 'true',
  ENABLE_YOUTUBE_UPLOAD: getEnvVar('VITE_ENABLE_YOUTUBE_UPLOAD', 'true') === 'true',
  ENABLE_AUTOMATION: getEnvVar('VITE_ENABLE_AUTOMATION', 'true') === 'true',
}

/**
 * Validate required environment variables
 */
export const validateEnv = () => {
  const errors = []
  
  if (!env.API_BASE_URL) {
    errors.push('VITE_API_BASE_URL is required')
  }
  
  if (errors.length > 0) {
    console.error('❌ Environment validation failed:')
    errors.forEach(error => console.error(`  - ${error}`))
    return false
  }
  
  console.log('✅ Environment validation passed')
  return true
}

/**
 * Log environment configuration (for debugging)
 */
export const logEnvConfig = () => {
  if (env.DEBUG || env.DEV) {
    console.log('🔧 Environment Configuration:')
    console.log('  API_BASE_URL:', env.API_BASE_URL)
    console.log('  NODE_ENV:', env.NODE_ENV)
    console.log('  DEBUG:', env.DEBUG)
    console.log('  VERBOSE_LOGGING:', env.VERBOSE_LOGGING)
    console.log('  ENABLE_AUTH:', env.ENABLE_AUTH)
    console.log('  ENABLE_YOUTUBE_UPLOAD:', env.ENABLE_YOUTUBE_UPLOAD)
    console.log('  ENABLE_AUTOMATION:', env.ENABLE_AUTOMATION)
  }
}

/**
 * Get all environment variables for debugging
 */
export const getAllEnvVars = () => {
  const allVars = {}
  
  // Get from import.meta.env
  if (typeof import.meta !== 'undefined' && import.meta.env) {
    Object.keys(import.meta.env).forEach(key => {
      allVars[key] = import.meta.env[key]
    })
  }
  
  // Get from process.env (Node.js)
  if (typeof process !== 'undefined' && process.env) {
    Object.keys(process.env).forEach(key => {
      if (key.startsWith('VITE_') || key.startsWith('NODE_')) {
        allVars[key] = process.env[key]
      }
    })
  }
  
  // Get from window.env
  if (typeof window !== 'undefined' && window.env) {
    Object.keys(window.env).forEach(key => {
      allVars[key] = window.env[key]
    })
  }
  
  return allVars
}

// Initialize and validate on import
validateEnv()
logEnvConfig()

// Export default
export default env
