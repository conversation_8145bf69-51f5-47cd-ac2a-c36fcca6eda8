// Environment Configuration
// Simple environment variable access for the frontend

// Debug: Log all available environment variables
console.log('🔍 All import.meta.env:', import.meta.env)
console.log('🔍 VITE_API_BASE_URL from import.meta.env:', import.meta.env.VITE_API_BASE_URL)
console.log('🔍 VITE_FRONTEND_URL from import.meta.env:', import.meta.env.VITE_FRONTEND_URL)

// Direct access to environment variables from Vite
export const env = {
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api',
  FRONTEND_URL: import.meta.env.VITE_FRONTEND_URL || 'http://localhost:5173'
}

// Debug: Log final env object
console.log('🔧 Final env object:', env)

// Export default
export default env
