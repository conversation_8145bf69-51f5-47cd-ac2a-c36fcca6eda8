#!/bin/bash

# Startup script for Render deployment

set -e

echo "🚀 Starting BioShort Application on Render..."

# Set the port from Render's environment variable
export PORT=${PORT:-10000}

# Initialize Airflow database if it doesn't exist
if [ ! -f "/app/data/airflow.db" ]; then
    echo "📊 Initializing Airflow database..."
    cd /app
    airflow db init

    # Create Airflow admin user
    airflow users create \
        --username airflow \
        --firstname Airflow \
        --lastname Admin \
        --role Admin \
        --email <EMAIL> \
        --password airflow || true
fi

# Update nginx configuration with the correct port
sed -i "s/listen 80;/listen $PORT;/" /etc/nginx/sites-available/default

# Update supervisor configuration for the correct port
cat > /etc/supervisor/conf.d/supervisord.conf << EOF
[supervisord]
nodaemon=true
user=root
logfile=/app/logs/supervisord.log
pidfile=/app/logs/supervisord.pid

[program:nginx]
command=nginx -g "daemon off;"
autostart=true
autorestart=true
stderr_logfile=/app/logs/nginx.err.log
stdout_logfile=/app/logs/nginx.out.log

[program:backend]
command=python app.py
directory=/app/backend
environment=FLASK_APP=app.py,FLASK_ENV=production,PORT=5000
autostart=true
autorestart=true
stderr_logfile=/app/logs/backend.err.log
stdout_logfile=/app/logs/backend.out.log

[program:airflow-webserver]
command=airflow webserver --port 8080
directory=/app
environment=AIRFLOW_HOME=/app/airflow
autostart=true
autorestart=true
stderr_logfile=/app/logs/airflow-webserver.err.log
stdout_logfile=/app/logs/airflow-webserver.out.log

[program:airflow-scheduler]
command=airflow scheduler
directory=/app
environment=AIRFLOW_HOME=/app/airflow
autostart=true
autorestart=true
stderr_logfile=/app/logs/airflow-scheduler.err.log
stdout_logfile=/app/logs/airflow-scheduler.out.log
EOF

echo "✅ Configuration updated for port $PORT"

# Start supervisor to manage all services
echo "🎯 Starting all services..."
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf
