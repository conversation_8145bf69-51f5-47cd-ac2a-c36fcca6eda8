#!/bin/bash

# Startup script for Render deployment

set -e

echo "🚀 Starting BioShort Application on Render..."

# Set the port from Render's environment variable
export PORT=${PORT:-10000}

# Set Airflow environment variables
export AIRFLOW_HOME=/app/airflow
export PYTHONPATH=/app/backend:/app
export AIRFLOW__DATABASE__SQL_ALCHEMY_CONN=sqlite:////app/data/airflow.db
export AIRFLOW__CORE__EXECUTOR=SequentialExecutor
export AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION=True
export AIRFLOW__CORE__LOAD_EXAMPLES=False
export AIRFLOW__WEBSERVER__EXPOSE_CONFIG=True

# Initialize Airflow database if it doesn't exist
if [ ! -f "/app/data/airflow.db" ]; then
    echo "📊 Initializing Airflow database..."
    cd /app
    airflow db init

    # Create Airflow admin user
    echo "👤 Creating Airflow admin user..."
    airflow users create \
        --username admin \
        --firstname Admin \
        --lastname User \
        --role Admin \
        --email <EMAIL> \
        --password admin || true
fi

# Update nginx configuration with the correct port
sed -i "s/listen 80;/listen $PORT;/" /etc/nginx/sites-available/default

# Update supervisor configuration for the correct port
cat > /etc/supervisor/conf.d/supervisord.conf << EOF
[supervisord]
nodaemon=true
user=root
logfile=/app/logs/supervisord.log
pidfile=/app/logs/supervisord.pid

[program:nginx]
command=nginx -g "daemon off;"
autostart=true
autorestart=true
stderr_logfile=/app/logs/nginx.err.log
stdout_logfile=/app/logs/nginx.out.log

[program:backend]
command=python app.py
directory=/app/backend
environment=FLASK_APP=app.py,FLASK_ENV=production,PORT=5000
autostart=true
autorestart=true
stderr_logfile=/app/logs/backend.err.log
stdout_logfile=/app/logs/backend.out.log

[program:airflow-webserver]
command=airflow webserver --port 8080 --hostname 0.0.0.0
directory=/app
environment=AIRFLOW_HOME=/app/airflow,PYTHONPATH=/app/backend:/app,AIRFLOW__DATABASE__SQL_ALCHEMY_CONN=sqlite:////app/data/airflow.db,AIRFLOW__CORE__EXECUTOR=SequentialExecutor,AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION=True,AIRFLOW__CORE__LOAD_EXAMPLES=False,AIRFLOW__WEBSERVER__EXPOSE_CONFIG=True
autostart=true
autorestart=true
stderr_logfile=/app/logs/airflow-webserver.err.log
stdout_logfile=/app/logs/airflow-webserver.out.log

[program:airflow-scheduler]
command=airflow scheduler
directory=/app
environment=AIRFLOW_HOME=/app/airflow,PYTHONPATH=/app/backend:/app,AIRFLOW__DATABASE__SQL_ALCHEMY_CONN=sqlite:////app/data/airflow.db,AIRFLOW__CORE__EXECUTOR=SequentialExecutor,AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION=True,AIRFLOW__CORE__LOAD_EXAMPLES=False
autostart=true
autorestart=true
stderr_logfile=/app/logs/airflow-scheduler.err.log
stdout_logfile=/app/logs/airflow-scheduler.out.log
EOF

echo "✅ Configuration updated for port $PORT"

# Test Airflow installation
echo "🧪 Testing Airflow installation..."
airflow version || echo "⚠️ Airflow version check failed"

# List Airflow commands
echo "📋 Available Airflow commands:"
airflow --help | head -20 || echo "⚠️ Airflow help failed"

# Check if DAGs directory exists
echo "📁 Checking DAGs directory..."
ls -la /app/airflow/dags/ || echo "⚠️ DAGs directory not found"

# Start supervisor to manage all services
echo "🎯 Starting all services with supervisor..."
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf
