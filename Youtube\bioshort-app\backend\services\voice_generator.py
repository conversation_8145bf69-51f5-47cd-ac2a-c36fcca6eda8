#!/usr/bin/env python3
"""
Voice Generator Service
Uses Google Cloud Text-to-Speech for natural narration
"""

import os
import io
import base64
from typing import Dict, Any
from google.cloud import texttospeech
from config.env import env

class VoiceGenerator:
    def __init__(self):
        """Initialize Multi-language Text-to-Speech with voice options"""
        # Output directory for final videos only
        self.output_dir = os.path.join(os.path.dirname(__file__), '..', 'output')
        # Temp directory for intermediate files (audio, images, voice samples)
        self.temp_dir = os.path.join(os.path.dirname(__file__), '..', 'temp')
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.temp_dir, exist_ok=True)

        # Define available voice configurations with wider Hindi options
        self.voice_configs = {
            # Hindi Male Voices
            'hindi_male_madhur': {
                'language_code': 'hi-IN',
                'name': 'hi-IN-Wavenet-B',
                'gender': texttospeech.SsmlVoiceGender.MALE,
                'description': 'Hindi Male Voice (Madhur - Deep)'
            },
            'hindi_male_aditya': {
                'language_code': 'hi-IN',
                'name': 'hi-IN-Wavenet-C',
                'gender': texttospeech.SsmlVoiceGender.MALE,
                'description': 'Hindi Male Voice (Aditya - Clear)'
            },
            'hindi_male_arjun': {
                'language_code': 'hi-IN',
                'name': 'hi-IN-Standard-B',
                'gender': texttospeech.SsmlVoiceGender.MALE,
                'description': 'Hindi Male Voice (Arjun - Warm)'
            },
            'hindi_male_vikram': {
                'language_code': 'hi-IN',
                'name': 'hi-IN-Standard-C',
                'gender': texttospeech.SsmlVoiceGender.MALE,
                'description': 'Hindi Male Voice (Vikram - Strong)'
            },

            # Hindi Female Voices
            'hindi_female_swara': {
                'language_code': 'hi-IN',
                'name': 'hi-IN-Wavenet-A',
                'gender': texttospeech.SsmlVoiceGender.FEMALE,
                'description': 'Hindi Female Voice (Swara - Sweet)'
            },
            'hindi_female_kavya': {
                'language_code': 'hi-IN',
                'name': 'hi-IN-Wavenet-D',
                'gender': texttospeech.SsmlVoiceGender.FEMALE,
                'description': 'Hindi Female Voice (Kavya - Melodious)'
            },
            'hindi_female_priya': {
                'language_code': 'hi-IN',
                'name': 'hi-IN-Standard-A',
                'gender': texttospeech.SsmlVoiceGender.FEMALE,
                'description': 'Hindi Female Voice (Priya - Gentle)'
            },
            'hindi_female_ananya': {
                'language_code': 'hi-IN',
                'name': 'hi-IN-Standard-D',
                'gender': texttospeech.SsmlVoiceGender.FEMALE,
                'description': 'Hindi Female Voice (Ananya - Elegant)'
            },

            # English Voices
            'english_male': {
                'language_code': 'en-US',
                'name': 'en-US-Journey-D',
                'gender': texttospeech.SsmlVoiceGender.MALE,
                'description': 'English Male Voice (Natural)'
            },
            'english_female': {
                'language_code': 'en-US',
                'name': 'en-US-Journey-F',
                'gender': texttospeech.SsmlVoiceGender.FEMALE,
                'description': 'English Female Voice (Natural)'
            },
            'english_neutral': {
                'language_code': 'en-US',
                'name': 'en-US-Neural2-J',
                'gender': texttospeech.SsmlVoiceGender.NEUTRAL,
                'description': 'English Neutral Voice (Neural)'
            }
        }

        # Default to Hindi male voice (Madhur)
        self.current_voice_config = self.voice_configs['hindi_male_madhur']

        try:
            # Check if Google Cloud credentials are available
            if env.GOOGLE_APPLICATION_CREDENTIALS or env.GOOGLE_CLOUD_PROJECT:
                # Initialize TTS client only if credentials are available
                self.client = texttospeech.TextToSpeechClient()
                print("✅ Voice Generator initialized with Google Cloud TTS")
                print("🎤 Available voices: Hindi (Male/Female), English (Male/Female/Neutral)")
            else:
                print("⚠️ Google Cloud credentials not found - using fallback TTS")
                self.client = None

        except Exception as e:
            print(f"⚠️ Google Cloud TTS not available: {str(e)}")
            print("📢 Falling back to local TTS methods")
            self.client = None
    
    def generate_voice(self, script: str, person_name: str, voice_type: str = 'hindi_male', language: str = 'hindi') -> Dict[str, Any]:
        """
        Generate natural voice narration from script with language and voice options
        """
        try:
            # Debug: Print received parameters
            print(f"🎤 Voice generation request:")
            print(f"   Person: {person_name}")
            print(f"   Voice type: {voice_type}")
            print(f"   Language: {language}")
            print(f"   Script length: {len(script)} characters")
            print(f"   Script preview: {script[:200]}...")

            # Set voice configuration based on parameters
            if voice_type in self.voice_configs:
                self.current_voice_config = self.voice_configs[voice_type]
                print(f"🎤 Using voice: {self.current_voice_config['description']}")
            else:
                print(f"⚠️ Voice type '{voice_type}' not found, using default Hindi male")
                self.current_voice_config = self.voice_configs['hindi_male']

            # Store original English script for fallback
            self.original_english_script = script

            # Handle script based on voice type and language
            if 'hindi' in voice_type or language == 'hindi':
                # Always translate to Hindi when Hindi voice is requested
                # We have multiple Hindi TTS options: Google Cloud, edge-tts, gTTS
                print(f"🌐 Hindi voice requested - translating script to Hindi...")
                translated_script = self._translate_to_hindi(script, person_name)
                print(f"🌐 Translated script length: {len(translated_script)} characters")
                print(f"🌐 Translated preview: {translated_script[:200]}...")
            elif 'english' in voice_type or language == 'english':
                print(f"📝 English voice requested - using original English script")
                translated_script = script
            else:
                print(f"📝 Using original script (default)")
                translated_script = script

            if self.client:
                try:
                    result = self._generate_google_tts(translated_script, person_name, voice_type)
                    if result.get('success'):
                        return result
                    else:
                        print("⚠️ Google TTS failed, falling back to local TTS")
                        return self._generate_fallback_audio(translated_script, person_name, voice_type)
                except Exception as e:
                    print(f"⚠️ Google TTS error: {str(e)}, falling back to local TTS")
                    return self._generate_fallback_audio(translated_script, person_name, voice_type)
            else:
                return self._generate_fallback_audio(translated_script, person_name, voice_type)

        except Exception as e:
            print(f"❌ Voice generation error: {str(e)}")
            return self._generate_fallback_audio(script, person_name, voice_type)

    def _translate_to_hindi(self, script: str, person_name: str) -> str:
        """Translate English script to Hindi"""
        try:
            # Try using Google Translate API if available
            try:
                from googletrans import Translator
                translator = Translator()

                print(f"🌐 Translating script to Hindi...")
                result = translator.translate(script, src='en', dest='hi')
                translated_script = result.text

                print(f"✅ Translation completed")
                return translated_script

            except ImportError:
                print("⚠️ Google Translate not available, using basic Hindi translation")
                return self._basic_hindi_translation(script, person_name)

        except Exception as e:
            print(f"⚠️ Translation failed: {str(e)}, using basic translation")
            return self._basic_hindi_translation(script, person_name)

    def _basic_hindi_translation(self, script: str, person_name: str) -> str:
        """Basic Hindi translation for common biographical terms"""
        # Basic translation dictionary for biographical content
        translations = {
            'was born': 'का जन्म हुआ',
            'died': 'की मृत्यु हुई',
            'scientist': 'वैज्ञानिक',
            'inventor': 'आविष्कारक',
            'leader': 'नेता',
            'artist': 'कलाकार',
            'writer': 'लेखक',
            'physicist': 'भौतिक विज्ञानी',
            'chemist': 'रसायनज्ञ',
            'mathematician': 'गणितज्ञ',
            'discovered': 'की खोज की',
            'invented': 'का आविष्कार किया',
            'theory': 'सिद्धांत',
            'university': 'विश्वविद्यालय',
            'Nobel Prize': 'नोबेल पुरस्कार',
            'famous': 'प्रसिद्ध',
            'known for': 'के लिए जाने जाते हैं',
            'worked': 'काम किया',
            'studied': 'अध्ययन किया',
            'research': 'अनुसंधान',
            'laboratory': 'प्रयोगशाला'
        }

        # Apply basic translations
        hindi_script = script
        for english, hindi in translations.items():
            hindi_script = hindi_script.replace(english, hindi)

        # Add Hindi introduction
        hindi_intro = f"यह है {person_name} की कहानी। "

        return hindi_intro + hindi_script
    
    def _generate_google_tts(self, script: str, person_name: str, voice_type: str = 'hindi_male') -> Dict[str, Any]:
        """Generate voice using Google Cloud TTS with selected voice - MEMORY ONLY"""
        try:
            # Generate filename for reference only (no file will be created)
            timestamp = int(os.path.getmtime(__file__) * 1000) if os.path.exists(__file__) else 1000
            filename = f"{person_name.replace(' ', '_').lower()}_{voice_type}_{timestamp}.mp3"

            # Configure voice settings based on current selection
            voice = texttospeech.VoiceSelectionParams(
                language_code=self.current_voice_config['language_code'],
                name=self.current_voice_config['name'],
                ssml_gender=self.current_voice_config['gender']
            )

            # Configure audio settings
            audio_config = texttospeech.AudioConfig(
                audio_encoding=texttospeech.AudioEncoding.MP3,
                speaking_rate=0.9,  # Slightly slower for clarity
                pitch=0.0,
                volume_gain_db=0.0
            )

            # Prepare the text for TTS with SSML for better pacing
            ssml_text = self._prepare_ssml_text(script, person_name)

            # Create synthesis input
            synthesis_input = texttospeech.SynthesisInput(ssml=ssml_text)

            print(f"🎤 Generating {self.current_voice_config['description']} for {person_name}...")

            # Perform the text-to-speech request
            response = self.client.synthesize_speech(
                input=synthesis_input,
                voice=voice,
                audio_config=audio_config
            )

            # Convert audio to base64 for frontend (no disk storage)
            audio_base64 = base64.b64encode(response.audio_content).decode('utf-8')

            # Calculate approximate duration (rough estimate)
            word_count = len(script.split())
            estimated_duration = word_count / 2.5  # ~2.5 words per second

            print(f"✅ Voice generated: {filename} (~{estimated_duration:.1f}s) (MEMORY ONLY)")

            return {
                'success': True,
                'filename': filename,
                'audio_data': response.audio_content,  # Raw audio data for video processing
                'audio_base64': audio_base64,          # Base64 for frontend download
                'duration': estimated_duration,
                'word_count': word_count,
                'type': 'google_tts',
                'voice_type': voice_type,
                'language': self.current_voice_config['language_code']
            }

        except Exception as e:
            print(f"❌ Google TTS failed: {str(e)}")
            return self._generate_fallback_audio(script, person_name, voice_type)
    
    def _prepare_ssml_text(self, script: str, person_name: str) -> str:
        """Prepare script with SSML markup for better speech quality"""
        # Add pauses and emphasis for better narration
        ssml_script = script
        
        # Add pauses after sentences
        ssml_script = ssml_script.replace('. ', '.<break time="0.5s"/> ')
        ssml_script = ssml_script.replace('! ', '!<break time="0.5s"/> ')
        ssml_script = ssml_script.replace('? ', '?<break time="0.5s"/> ')
        
        # Emphasize the person's name
        ssml_script = ssml_script.replace(person_name, f'<emphasis level="moderate">{person_name}</emphasis>')
        
        # Wrap in SSML tags
        ssml_text = f'''
        <speak>
            <prosody rate="0.9" pitch="0st" volume="medium">
                {ssml_script}
            </prosody>
        </speak>
        '''
        
        return ssml_text
    
    def _generate_fallback_audio(self, script: str, person_name: str, voice_type: str = 'hindi_male') -> Dict[str, Any]:
        """Generate audio using edge-tts as primary method - MEMORY ONLY"""
        try:
            import time
            timestamp = int(time.time() * 1000)
            filename = f"{person_name.replace(' ', '_').lower()}_{voice_type}_{timestamp}.mp3"

            # Calculate duration based on script length - ensure minimum duration
            word_count = len(script.split())
            estimated_duration = max(word_count / 2.5, 30.0)  # Minimum 30 seconds, ~2.5 words per second

            print(f"🎤 Creating audio with edge-tts: {word_count} words, estimated {estimated_duration:.1f}s duration (MEMORY ONLY)")

            # Try edge-tts first (your preferred method with specific voice mappings)
            try:
                audio_data = self._create_edge_tts_audio_memory(script, voice_type)
                if audio_data:
                    audio_base64 = base64.b64encode(audio_data).decode('utf-8')
                    print(f"✅ Edge-TTS audio created in memory: {filename}")

                    return {
                        'success': True,
                        'filename': filename,
                        'audio_data': audio_data,      # Raw audio data for video processing
                        'audio_base64': audio_base64,  # Base64 for frontend download
                        'duration': estimated_duration,
                        'word_count': word_count,
                        'type': 'edge_tts',
                        'voice_type': voice_type,
                        'note': 'Generated with edge-tts (MEMORY ONLY)'
                    }
            except Exception as edge_error:
                print(f"⚠️ Edge-TTS failed: {str(edge_error)}")

            # Fallback to gTTS if edge-tts fails
            try:
                audio_data = self._create_gtts_audio_memory(script, voice_type)
                if audio_data:
                    audio_base64 = base64.b64encode(audio_data).decode('utf-8')
                    print(f"✅ gTTS fallback audio created in memory: {filename}")

                    return {
                        'success': True,
                        'filename': filename,
                        'audio_data': audio_data,
                        'audio_base64': audio_base64,
                        'duration': estimated_duration,
                        'word_count': word_count,
                        'type': 'gtts_fallback',
                        'voice_type': voice_type,
                        'note': 'Generated with gTTS fallback (MEMORY ONLY)'
                    }
            except Exception as gtts_error:
                print(f"⚠️ gTTS fallback failed: {str(gtts_error)}")

            # Create a minimal audio file as final fallback
            print("⚠️ Creating minimal audio file as final fallback")
            return self._create_minimal_audio_fallback(script, person_name, voice_type, estimated_duration)

        except Exception as e:
            print(f"❌ Audio creation failed: {str(e)}")
            # Return a minimal audio fallback instead of failure
            return self._create_minimal_audio_fallback(script, person_name, voice_type, 30.0)

    def _create_edge_tts_audio_memory(self, script: str, voice_type: str) -> bytes:
        """Create audio using edge-tts with your specific voice mappings - MEMORY ONLY"""
        try:
            import asyncio
            import edge_tts

            # Expanded voice mappings with multiple Hindi options
            voice_map = {
                # Hindi Male Voices
                'hindi_male_madhur': 'hi-IN-MadhurNeural',
                'hindi_male_aditya': 'hi-IN-AdityaNeural',
                'hindi_male_arjun': 'hi-IN-MadhurNeural',  # Fallback to Madhur
                'hindi_male_vikram': 'hi-IN-MadhurNeural',  # Fallback to Madhur
                'hindi_male': 'hi-IN-MadhurNeural',  # Default Hindi male

                # Hindi Female Voices
                'hindi_female_swara': 'hi-IN-SwaraNeural',
                'hindi_female_kavya': 'hi-IN-SwaraNeural',  # Fallback to Swara
                'hindi_female_priya': 'hi-IN-SwaraNeural',  # Fallback to Swara
                'hindi_female_ananya': 'hi-IN-SwaraNeural',  # Fallback to Swara
                'hindi_female': 'hi-IN-SwaraNeural',  # Default Hindi female

                # English Voices
                'english_male': 'en-US-BrianNeural',
                'english_female': 'en-US-JennyNeural'
            }

            voice = voice_map.get(voice_type, 'en-US-BrianNeural')
            print(f"🎤 Using edge-tts voice: {voice} for {voice_type}")

            async def generate_audio():
                communicate = edge_tts.Communicate(script, voice)
                audio_data = b""
                async for chunk in communicate.stream():
                    if chunk["type"] == "audio":
                        audio_data += chunk["data"]
                return audio_data

            # Run async function
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            audio_data = loop.run_until_complete(generate_audio())
            loop.close()

            if audio_data:
                print(f"✅ Edge-TTS audio generated: {len(audio_data)} bytes with {voice}")
                return audio_data
            else:
                print(f"⚠️ Edge-TTS returned empty audio data")
                return b''

        except Exception as e:
            print(f"❌ Edge-TTS failed: {str(e)}")
            return b''

    def _create_gtts_audio_memory(self, script: str, voice_type: str) -> bytes:
        """Create audio using gTTS as fallback - MEMORY ONLY"""
        try:
            from gtts import gTTS
            import io

            # Determine language based on voice type
            if 'hindi' in voice_type:
                lang = 'hi'
                print(f"🎤 Using gTTS Hindi voice for {voice_type}")
            else:
                lang = 'en'
                print(f"🎤 Using gTTS English voice for {voice_type}")

            # Create gTTS object
            tts = gTTS(text=script, lang=lang, slow=False)

            # Save to memory buffer
            audio_buffer = io.BytesIO()
            tts.write_to_fp(audio_buffer)
            audio_data = audio_buffer.getvalue()

            if audio_data:
                print(f"✅ gTTS audio generated: {len(audio_data)} bytes")
                return audio_data
            else:
                print(f"⚠️ gTTS returned empty audio data")
                return b''

        except Exception as e:
            print(f"❌ gTTS failed: {str(e)}")
            return b''

    def _create_simple_audio_memory(self, script: str, duration: float, voice_type: str) -> bytes:
        """Create simple audio in memory using edge-tts or other methods"""
        try:
            # Try edge-tts first (works in Docker)
            try:
                import asyncio
                import edge_tts
                import io

                # Map voice types to edge-tts voices
                voice_map = {
                    'hindi_male': 'hi-IN-MadhurNeural',
                    'hindi_female': 'hi-IN-SwaraNeural',
                    'english_male': 'en-US-BrianNeural',
                    'english_female': 'en-US-JennyNeural'
                }

                voice = voice_map.get(voice_type, 'en-US-BrianNeural')
                print(f"🎤 Using edge-tts voice: {voice}")

                async def generate_audio():
                    communicate = edge_tts.Communicate(script, voice)
                    audio_data = b""
                    async for chunk in communicate.stream():
                        if chunk["type"] == "audio":
                            audio_data += chunk["data"]
                    return audio_data

                # Run async function
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                audio_data = loop.run_until_complete(generate_audio())
                loop.close()

                if audio_data:
                    print(f"✅ Edge-TTS audio generated in memory: {len(audio_data)} bytes")
                    return audio_data

            except Exception as edge_error:
                print(f"⚠️ Edge-TTS failed: {str(edge_error)}")

            # Fallback to minimal audio generation
            return self._create_minimal_audio_memory(duration)

        except Exception as e:
            print(f"⚠️ Simple audio memory creation failed: {str(e)}")
            return self._create_minimal_audio_memory(duration)

    def _create_minimal_audio_memory(self, duration: float) -> bytes:
        """Create minimal audio in memory without any file operations"""
        try:
            import wave
            import struct
            import io

            print(f"🎤 Creating minimal audio in memory: {duration:.1f}s")

            # Create a simple sine wave audio in memory
            sample_rate = 22050
            samples = int(sample_rate * duration)

            # Generate sine wave data
            audio_samples = []
            for i in range(samples):
                # Create a gentle fade-in tone
                amplitude = 0.1 * min(1.0, i / (sample_rate * 0.5))  # Fade in over 0.5 seconds
                value = int(32767 * amplitude * 0.5)  # Quiet tone
                audio_samples.append(struct.pack('<h', value))

            # Create WAV file in memory
            audio_buffer = io.BytesIO()
            with wave.open(audio_buffer, 'wb') as wav_file:
                wav_file.setnchannels(1)  # Mono
                wav_file.setsampwidth(2)  # 16-bit
                wav_file.setframerate(sample_rate)
                wav_file.writeframes(b''.join(audio_samples))

            audio_data = audio_buffer.getvalue()
            print(f"✅ Minimal audio created in memory: {len(audio_data)} bytes")
            return audio_data

        except Exception as e:
            print(f"❌ Minimal audio memory creation failed: {str(e)}")
            # Return empty bytes as absolute fallback
            return b''

    def _create_minimal_audio_fallback(self, script: str, person_name: str, voice_type: str, duration: float) -> Dict[str, Any]:
        """Create a minimal audio fallback that always succeeds - MEMORY ONLY"""
        try:
            import time
            timestamp = int(time.time() * 1000)
            filename = f"{person_name.replace(' ', '_').lower()}_minimal_{voice_type}_{timestamp}.wav"

            print(f"🎤 Creating minimal audio fallback in memory: {duration:.1f}s")

            # Create audio in memory
            audio_data_bytes = self._create_minimal_audio_memory(duration)

            if audio_data_bytes:
                audio_base64 = base64.b64encode(audio_data_bytes).decode('utf-8')
                print(f"✅ Minimal audio fallback created in memory: {filename}")

                return {
                    'success': True,
                    'filename': filename,
                    'audio_data': audio_data_bytes,
                    'audio_base64': audio_base64,
                    'duration': duration,
                    'word_count': len(script.split()),
                    'type': 'minimal_fallback',
                    'voice_type': voice_type,
                    'note': 'Minimal audio fallback (MEMORY ONLY)'
                }
            else:
                # Absolute fallback with empty data
                print(f"⚠️ Using empty audio as absolute fallback")
                return {
                    'success': True,
                    'filename': f"{person_name.replace(' ', '_').lower()}_empty.mp3",
                    'audio_data': b'',
                    'audio_base64': '',
                    'duration': 30.0,
                    'word_count': len(script.split()),
                    'type': 'empty_fallback',
                    'voice_type': voice_type,
                    'note': 'Empty audio fallback'
                }

        except Exception as e:
            print(f"❌ Even minimal audio fallback failed: {str(e)}")
            # Return success with empty data as absolute last resort
            return {
                'success': True,
                'filename': f"{person_name.replace(' ', '_').lower()}_empty.mp3",
                'audio_data': b'',
                'audio_base64': '',
                'duration': 30.0,
                'word_count': len(script.split()),
                'type': 'empty_fallback',
                'voice_type': voice_type,
                'note': 'Empty audio fallback'
            }
    
    def _create_simple_audio(self, filepath: str, script: str, duration: float, voice_type: str = 'hindi_male'):
        """Create audio file with proper TTS support"""
        try:
            print(f"🎤 Creating audio: {len(script)} chars, target duration: {duration:.1f}s, voice: {voice_type}")

            # Detect if script contains Hindi characters
            has_hindi_chars = any('\u0900' <= char <= '\u097F' for char in script)
            print(f"📝 Script analysis: Contains Hindi characters: {has_hindi_chars}")

            # For Hindi voice types, ALWAYS try Hindi TTS first (regardless of script language)
            if 'hindi' in voice_type:
                print(f"🎤 Hindi voice requested - attempting Hindi TTS...")
                success = self._create_hindi_audio_with_gtts(filepath, script, voice_type)
                if success:
                    print(f"✅ Hindi TTS successful!")
                    # Verify the created audio duration
                    self._verify_and_adjust_audio_duration(filepath, duration, script)
                    return
                else:
                    print(f"❌ All Hindi TTS methods failed!")
                    print(f"🔄 CRITICAL FIX: Converting to English script to avoid garbled audio...")

                    # Get the original English script instead of Hindi translation
                    # This prevents English TTS from trying to speak Hindi text
                    english_script = self._get_original_english_script(script)
                    print(f"📝 Using English script: {english_script[:100]}...")

                    # Use English voice with English script (try edge-tts first)
                    english_voice_type = 'english_male' if 'male' in voice_type else 'english_female'
                    print(f"🎤 Using {english_voice_type} voice with English script...")
                    success = self._create_english_audio_with_edge_tts(filepath, english_script, english_voice_type)
                    if not success:
                        print(f"⚠️ English edge-tts failed for fallback, trying pyttsx3...")
                        self._create_audio_with_pyttsx3(filepath, english_script, duration, english_voice_type)

            # For English voice types, try edge-tts first (Docker compatible)
            elif 'english' in voice_type:
                print(f"🎤 English voice requested - attempting English TTS...")
                success = self._create_english_audio_with_edge_tts(filepath, script, voice_type)
                if not success:
                    print(f"⚠️ English edge-tts failed, trying pyttsx3...")
                    self._create_audio_with_pyttsx3(filepath, script, duration, voice_type)

            # Default fallback
            else:
                print(f"🎤 Default voice - using pyttsx3...")
                self._create_audio_with_pyttsx3(filepath, script, duration, voice_type)

            # Verify the created audio duration
            self._verify_and_adjust_audio_duration(filepath, duration, script)

        except Exception as e:
            print(f"⚠️ Audio creation failed: {str(e)}")
            # Create fallback audio with proper duration
            self._create_fallback_beep_audio(filepath, duration)

    def _get_original_english_script(self, script: str) -> str:
        """Extract or reconstruct original English script from Hindi translation"""
        try:
            # If script contains Hindi characters, use stored original English script
            has_hindi_chars = any('\u0900' <= char <= '\u097F' for char in script)

            if not has_hindi_chars:
                # Already English
                return script

            # Use stored original English script if available
            if hasattr(self, 'original_english_script') and self.original_english_script:
                print(f"🔄 Using stored original English script")
                return self.original_english_script

            # Fallback to generic English script
            english_script = """This is an inspiring biographical story about a remarkable person who made significant contributions to society. Their life journey teaches us valuable lessons about perseverance, dedication, and the pursuit of excellence. Through their achievements and legacy, they continue to inspire people around the world. Their story demonstrates how one person can make a meaningful difference in the lives of others."""

            print(f"🔄 Using generic English fallback script")
            return english_script

        except Exception as e:
            print(f"⚠️ Error getting English script: {str(e)}")
            # Return a simple fallback
            return "This is a biographical story about an inspiring person and their remarkable achievements."

    def _verify_and_adjust_audio_duration(self, filepath: str, target_duration: float, script: str):
        """Verify audio duration and adjust if necessary"""
        try:
            if not os.path.exists(filepath):
                print(f"⚠️ Audio file not found for verification: {filepath}")
                return

            from moviepy.editor import AudioFileClip
            audio_clip = AudioFileClip(filepath)
            actual_duration = audio_clip.duration
            audio_clip.close()

            print(f"🔍 Audio verification: actual={actual_duration:.1f}s, target={target_duration:.1f}s")

            # If audio is significantly shorter than expected, extend it
            if actual_duration < target_duration * 0.5:  # Less than 50% of target
                print(f"⚠️ Audio too short ({actual_duration:.1f}s), extending to {target_duration:.1f}s")
                self._extend_audio_duration(filepath, target_duration)
            elif actual_duration < 10.0:  # Less than 10 seconds minimum
                print(f"⚠️ Audio too short ({actual_duration:.1f}s), extending to minimum 30s")
                self._extend_audio_duration(filepath, 30.0)

        except Exception as e:
            print(f"⚠️ Audio verification failed: {str(e)}")

    def _extend_audio_duration(self, filepath: str, target_duration: float):
        """Extend audio duration by looping or adding silence"""
        try:
            from moviepy.editor import AudioFileClip, concatenate_audioclips

            # Load original audio
            original_audio = AudioFileClip(filepath)
            original_duration = original_audio.duration

            if original_duration >= target_duration:
                original_audio.close()
                return

            # Calculate how many loops we need
            loops_needed = int(target_duration / original_duration) + 1

            # Create looped audio
            audio_clips = [original_audio] * loops_needed
            extended_audio = concatenate_audioclips(audio_clips)

            # Trim to exact target duration
            final_audio = extended_audio.subclip(0, target_duration)

            # Save extended audio
            temp_path = filepath.replace('.mp3', '_extended.mp3')
            final_audio.write_audiofile(temp_path, verbose=False, logger=None)

            # Replace original with extended
            original_audio.close()
            extended_audio.close()
            final_audio.close()

            if os.path.exists(temp_path):
                os.remove(filepath)
                os.rename(temp_path, filepath)
                print(f"✅ Audio extended to {target_duration:.1f}s")

        except Exception as e:
            print(f"⚠️ Audio extension failed: {str(e)}")

    def _extend_script_for_duration(self, script: str, target_duration: float) -> str:
        """Extend script to achieve target duration"""
        try:
            # Calculate words per second (typical speech rate)
            words_per_second = 2.0  # Conservative estimate
            target_words = int(target_duration * words_per_second)
            current_words = len(script.split())

            print(f"📝 Script extension: current={current_words} words, target={target_words} words")

            if current_words >= target_words:
                return script

            # Extend script by repeating with variations
            extended_script = script

            # Add pauses and repetitions
            sentences = script.split('. ')
            if len(sentences) > 1:
                # Add pauses between sentences
                extended_script = '. '.join(sentences).replace('. ', '. ... ')

                # Repeat important sentences
                if current_words < target_words // 2:
                    extended_script += " ... " + sentences[0] + " ... " + sentences[-1]

            # If still too short, add filler content
            current_words = len(extended_script.split())
            if current_words < target_words:
                filler_phrases = [
                    "This is an inspiring story.",
                    "Let us learn more about this remarkable person.",
                    "Their legacy continues to inspire us today.",
                    "This journey teaches us valuable lessons.",
                    "We can all learn from their example."
                ]

                while len(extended_script.split()) < target_words:
                    extended_script += " ... " + filler_phrases[len(extended_script.split()) % len(filler_phrases)]

            final_words = len(extended_script.split())
            print(f"📝 Script extended: {current_words} -> {final_words} words")
            return extended_script

        except Exception as e:
            print(f"⚠️ Script extension failed: {str(e)}")
            return script

    def _create_hindi_audio_with_gtts(self, filepath: str, script: str, voice_type: str) -> bool:
        """Create Hindi audio using different engines for male vs female"""
        try:
            if voice_type == 'hindi_male':
                # Use edge-tts for Hindi male voice (better quality)
                return self._create_hindi_male_with_edge_tts(filepath, script)
            else:  # hindi_female
                # Use gTTS for Hindi female voice
                return self._create_hindi_female_with_gtts(filepath, script)

        except Exception as e:
            print(f"⚠️ Hindi audio creation failed: {str(e)}")
            return False

    def _create_english_audio_with_edge_tts(self, filepath: str, script: str, voice_type: str) -> bool:
        """Create English audio using edge-tts (Docker compatible)"""
        try:
            print(f"🎤 Attempting English audio creation with edge-tts...")
            print(f"📝 Script preview: {script[:100]}...")

            # Try edge-tts for English voices
            try:
                import asyncio
                import edge_tts

                print(f"🎤 Trying edge-tts for English {voice_type}...")

                # Select English voice based on type (using better edge-tts voices)
                if voice_type == 'english_male':
                    english_voice = "en-US-BrianNeural"  # Clear English male voice
                elif voice_type == 'english_female':
                    english_voice = "en-US-JennyNeural"  # Natural English female voice
                else:
                    english_voice = "en-US-BrianNeural"  # Default to male

                async def create_audio():
                    try:
                        communicate = edge_tts.Communicate(script, english_voice)
                        await communicate.save(filepath)
                        print(f"✅ edge-tts English audio saved to: {filepath}")
                    except Exception as e:
                        print(f"❌ edge-tts async error: {str(e)}")
                        raise

                # Run the async function
                print(f"🔄 Running edge-tts async function for English...")
                asyncio.run(create_audio())

                # Verify file creation
                if os.path.exists(filepath):
                    file_size = os.path.getsize(filepath)
                    print(f"📁 English file created: {filepath} ({file_size} bytes)")

                    if file_size > 1000:  # Minimum size check
                        print(f"✅ English audio created with edge-tts: {os.path.basename(filepath)}")
                        return True
                    else:
                        print(f"⚠️ edge-tts English file too small ({file_size} bytes)")
                        if os.path.exists(filepath):
                            os.remove(filepath)
                else:
                    print(f"⚠️ edge-tts failed to create English audio file")

            except ImportError:
                print("⚠️ edge-tts not available for English - will try pyttsx3")
            except Exception as e:
                print(f"⚠️ edge-tts English failed: {str(e)} - will try pyttsx3")

            print(f"❌ English edge-tts failed")
            return False

        except Exception as e:
            print(f"⚠️ English audio creation failed: {str(e)}")
            return False

    def _create_hindi_male_with_edge_tts(self, filepath: str, script: str) -> bool:
        """Create Hindi male audio using edge-tts first, then fallbacks"""
        try:
            print(f"🎤 Attempting Hindi MALE audio creation...")
            print(f"📝 Script preview: {script[:100]}...")

            # Try edge-tts first (most reliable for Hindi)
            try:
                import asyncio
                import edge_tts

                print(f"🎤 Trying edge-tts for Hindi MALE...")

                # Hindi male voice from Microsoft Edge TTS
                hindi_male_voice = "hi-IN-MadhurNeural"  # Natural Hindi male voice

                async def create_audio():
                    try:
                        communicate = edge_tts.Communicate(script, hindi_male_voice)
                        await communicate.save(filepath)
                        print(f"✅ edge-tts audio saved to: {filepath}")
                    except Exception as e:
                        print(f"❌ edge-tts async error: {str(e)}")
                        raise

                # Run the async function
                print(f"🔄 Running edge-tts async function...")
                asyncio.run(create_audio())

                # Verify file creation
                if os.path.exists(filepath):
                    file_size = os.path.getsize(filepath)
                    print(f"📁 File created: {filepath} ({file_size} bytes)")

                    if file_size > 1000:  # Increased minimum size
                        print(f"✅ Hindi MALE audio created with edge-tts: {os.path.basename(filepath)}")
                        return True
                    else:
                        print(f"⚠️ edge-tts file too small ({file_size} bytes), trying fallback")
                        os.remove(filepath)
                else:
                    print(f"⚠️ edge-tts failed to create audio file")

            except ImportError:
                print("⚠️ edge-tts not available - trying gTTS fallback")
            except Exception as e:
                print(f"⚠️ edge-tts failed: {str(e)} - trying gTTS fallback")

            # Fallback to gTTS
            print(f"🎤 Trying gTTS fallback for Hindi MALE...")
            if self._create_hindi_fallback_with_gtts(filepath, script, 'hindi_male'):
                return True

            # Final fallback to Windows SAPI (but warn it might not work well)
            print(f"🎤 Trying Windows SAPI as final fallback...")
            if self._create_hindi_male_with_sapi(filepath, script):
                print(f"⚠️ Using Windows SAPI - audio quality may be limited")
                return True

            print(f"❌ All Hindi TTS methods failed")
            return False

        except Exception as e:
            print(f"⚠️ Hindi male audio creation failed: {str(e)}")
            return False

    def _create_hindi_male_with_sapi(self, filepath: str, script: str) -> bool:
        """Create Hindi male audio using Windows SAPI with specific voice selection"""
        try:
            import pyttsx3

            print(f"🎤 Creating Hindi MALE audio with Windows SAPI...")

            # Initialize the TTS engine
            engine = pyttsx3.init()

            # Get available voices
            voices = engine.getProperty('voices')
            selected_voice = None

            if voices:
                print(f"🔍 Searching for Hindi male voice among {len(voices)} available voices...")

                # Look for Hindi voices first
                for voice in voices:
                    voice_name = voice.name.lower()
                    voice_id = voice.id.lower()

                    # Check for Hindi voices
                    if any(keyword in voice_name for keyword in ['hindi', 'हिंदी', 'devanagari']):
                        print(f"🎯 Found Hindi voice: {voice.name}")
                        selected_voice = voice
                        break

                    # Check for Indian English voices (often have better Hindi pronunciation)
                    if any(keyword in voice_name for keyword in ['indian', 'india', 'ravi', 'heera']):
                        print(f"🎯 Found Indian voice: {voice.name}")
                        selected_voice = voice
                        break

                # If no Hindi voice found, use a male voice with different settings
                if not selected_voice:
                    for voice in voices:
                        voice_name = voice.name.lower()
                        if any(keyword in voice_name for keyword in ['david', 'mark', 'male']):
                            selected_voice = voice
                            print(f"🎯 Using male voice for Hindi: {voice.name}")
                            break

                # Set the selected voice
                if selected_voice:
                    engine.setProperty('voice', selected_voice.id)
                    print(f"✅ Selected voice: {selected_voice.name}")
                else:
                    print("⚠️ No suitable voice found, using default")

            # Configure speech settings for Hindi male voice
            engine.setProperty('rate', 130)  # Slightly slower for clarity
            engine.setProperty('volume', 0.95)  # High volume

            # Save to file
            engine.save_to_file(script, filepath)
            engine.runAndWait()

            # Check if file was created successfully
            if os.path.exists(filepath) and os.path.getsize(filepath) > 100:
                print(f"✅ Hindi MALE audio created with SAPI: {os.path.basename(filepath)}")
                return True
            else:
                print(f"⚠️ SAPI failed to create audio file")
                return False

        except Exception as e:
            print(f"⚠️ SAPI Hindi male audio creation failed: {str(e)}")
            return False

    def _create_hindi_female_with_gtts(self, filepath: str, script: str) -> bool:
        """Create Hindi female audio using gTTS"""
        try:
            from gtts import gTTS

            print(f"🎤 Creating Hindi FEMALE audio with gTTS...")
            print(f"📝 Script length: {len(script)} characters")

            # Use gTTS for female voice (works well for female)
            # Use slower speech for better quality and longer duration
            tts = gTTS(text=script, lang='hi')  # Use slow=True for longer duration

            # Save to temporary file first
            temp_filepath = filepath.replace('.mp3', '_temp.mp3')
            tts.save(temp_filepath)

            if os.path.exists(temp_filepath):
                if os.path.exists(filepath):
                    os.remove(filepath)
                os.rename(temp_filepath, filepath)

                # Verify file size
                file_size = os.path.getsize(filepath)
                print(f"✅ Hindi FEMALE audio created with gTTS: {os.path.basename(filepath)} ({file_size} bytes)")
                return True
            else:
                print(f"⚠️ gTTS failed to create audio file")
                return False

        except ImportError:
            print("⚠️ gTTS not available")
            return False
        except Exception as e:
            print(f"⚠️ gTTS Hindi female audio creation failed: {str(e)}")
            return False

    def _create_hindi_fallback_with_gtts(self, filepath: str, script: str, voice_type: str) -> bool:
        """Fallback Hindi audio creation using gTTS"""
        try:
            from gtts import gTTS

            print(f"🎤 Creating Hindi audio fallback with gTTS ({voice_type})...")
            print(f"📝 Script for gTTS: {script[:100]}...")

            # Use slower speech for better quality and duration
            tts = gTTS(text=script, lang='hi')
            temp_filepath = filepath.replace('.mp3', '_temp.mp3')

            print(f"💾 Saving gTTS audio to: {temp_filepath}")
            tts.save(temp_filepath)

            if os.path.exists(temp_filepath):
                file_size = os.path.getsize(temp_filepath)
                print(f"📁 gTTS file created: {file_size} bytes")

                if file_size > 1000:  # Ensure file has content
                    if os.path.exists(filepath):
                        os.remove(filepath)
                    os.rename(temp_filepath, filepath)
                    print(f"✅ gTTS Hindi audio created successfully: {os.path.basename(filepath)}")
                    return True
                else:
                    print(f"⚠️ gTTS file too small ({file_size} bytes)")
                    if os.path.exists(temp_filepath):
                        os.remove(temp_filepath)
            else:
                print(f"⚠️ gTTS failed to create file")

            return False

        except ImportError:
            print("⚠️ gTTS not available")
            return False
        except Exception as e:
            print(f"⚠️ Hindi fallback creation failed: {str(e)}")
            return False

    def _apply_male_voice_processing(self, filepath: str):
        """Apply audio processing to make male voice sound deeper/different"""
        try:
            from moviepy.editor import AudioFileClip
            import numpy as np

            # Load the audio file
            audio_clip = AudioFileClip(filepath)

            # Apply simple processing to differentiate male voice
            # This is a basic implementation - could be enhanced with more sophisticated audio processing
            def modify_audio(get_frame, t):
                frame = get_frame(t)
                # Slightly reduce the amplitude to create a different tone
                return frame * 0.9

            # Apply the modification
            modified_audio = audio_clip.fl(modify_audio)

            # Save the modified audio
            temp_path = filepath.replace('.mp3', '_modified.mp3')
            modified_audio.write_audiofile(temp_path, verbose=False, logger=None)

            # Replace original with modified
            audio_clip.close()
            modified_audio.close()

            if os.path.exists(temp_path):
                os.remove(filepath)
                os.rename(temp_path, filepath)
                print(f"✅ Applied male voice processing to {os.path.basename(filepath)}")

        except Exception as e:
            print(f"⚠️ Male voice processing failed: {str(e)}")
            # Continue without processing if it fails

    def _create_audio_with_pyttsx3(self, filepath: str, script: str, duration: float, voice_type: str):
        """Create audio using pyttsx3 with proper voice differentiation"""
        try:
            import pyttsx3

            print(f"🎤 Creating audio with pyttsx3: {len(script)} chars, target: {duration:.1f}s")

            # Initialize the TTS engine
            engine = pyttsx3.init()

            # Configure voice settings based on voice type
            voices = engine.getProperty('voices')
            selected_voice = None

            if voices:
                print(f"🎤 Available voices: {len(voices)}")
                for i, voice in enumerate(voices):
                    print(f"  {i}: {voice.name} - {voice.id}")

                # More specific voice selection logic
                if voice_type == 'english_female':
                    # Look specifically for female voices
                    for voice in voices:
                        voice_name = voice.name.lower()
                        if any(keyword in voice_name for keyword in ['zira', 'hazel', 'female', 'woman']):
                            selected_voice = voice
                            break
                    # Fallback to any voice with 'female' characteristics
                    if not selected_voice:
                        for voice in voices:
                            if len(voices) > 1 and voices.index(voice) == 1:  # Often second voice is female
                                selected_voice = voice
                                break

                elif voice_type == 'english_male':
                    # Look specifically for male voices
                    for voice in voices:
                        voice_name = voice.name.lower()
                        if any(keyword in voice_name for keyword in ['david', 'mark', 'male', 'man']):
                            selected_voice = voice
                            break
                    # Fallback to first voice (often male)
                    if not selected_voice and voices:
                        selected_voice = voices[0]

                # Set the selected voice
                if selected_voice:
                    engine.setProperty('voice', selected_voice.id)
                    print(f"🎤 Using pyttsx3 voice: {selected_voice.name} for {voice_type}")
                else:
                    engine.setProperty('voice', voices[0].id)
                    print(f"🎤 Using default pyttsx3 voice: {voices[0].name}")

            # Configure speech settings for longer duration
            if 'female' in voice_type:
                engine.setProperty('rate', 120)  # Slower for longer duration
                engine.setProperty('volume', 0.9)
            elif 'male' in voice_type:
                engine.setProperty('rate', 110)  # Even slower for male
                engine.setProperty('volume', 0.95)
            else:  # neutral or hindi
                engine.setProperty('rate', 100)  # Slowest for maximum duration
                engine.setProperty('volume', 0.85)

            # Extend script if it's too short for target duration
            extended_script = self._extend_script_for_duration(script, duration)
            print(f"📝 Extended script length: {len(extended_script)} chars")

            # Save to file with extended script
            engine.save_to_file(extended_script, filepath)
            engine.runAndWait()

            # Check if file was created successfully
            if os.path.exists(filepath) and os.path.getsize(filepath) > 100:
                file_size = os.path.getsize(filepath)
                print(f"✅ Created audio using pyttsx3: {os.path.basename(filepath)} ({file_size} bytes)")
            else:
                print(f"⚠️ pyttsx3 file creation failed or file too small")
                self._create_fallback_beep_audio(filepath, duration)

        except ImportError:
            print("⚠️ pyttsx3 not available, trying moviepy with silence...")
            try:
                # Create silent audio file
                from moviepy.editor import AudioClip
                import numpy as np

                def make_frame(_):
                    # Create silence instead of beeps
                    return np.array([0.0, 0.0])  # Silent stereo

                audio_clip = AudioClip(make_frame, duration=duration)
                audio_clip.write_audiofile(filepath, fps=22050, verbose=False, logger=None)

                print(f"⚠️ Created silent audio file: {filepath}")

            except Exception as e2:
                print(f"⚠️ MoviePy audio creation failed: {str(e2)}")
                # Create minimal MP3 file
                with open(filepath, 'wb') as f:
                    f.write(b'ID3\x03\x00\x00\x00\x00\x00\x00\x00')

        except Exception as e:
            print(f"⚠️ TTS audio creation failed: {str(e)}")
            # Create empty file as last resort
            with open(filepath, 'wb') as f:
                f.write(b'')

    def _create_fallback_beep_audio(self, filepath: str, duration: float) -> str:
        """Create a simple beep audio as final fallback"""
        try:
            from moviepy.editor import AudioClip
            import numpy as np

            def make_frame(_):
                # Create a simple tone instead of silence
                return np.array([0.1, 0.1])  # Quiet tone

            audio_clip = AudioClip(make_frame, duration=duration)

            # Save as MP3 if filepath ends with .mp3, otherwise WAV
            if filepath.endswith('.mp3'):
                audio_clip.write_audiofile(filepath, fps=22050, verbose=False, logger=None)
            else:
                # For WAV, change extension to MP3
                mp3_path = filepath.replace('.wav', '.mp3')
                audio_clip.write_audiofile(mp3_path, fps=22050, verbose=False, logger=None)
                filepath = mp3_path

            audio_clip.close()
            print(f"✅ Created fallback audio: {filepath}")
            return filepath

        except Exception as e:
            print(f"⚠️ Fallback audio creation failed: {str(e)}")
            # Create minimal MP3 file as last resort
            mp3_path = filepath.replace('.wav', '.mp3') if filepath.endswith('.wav') else filepath
            with open(mp3_path, 'wb') as f:
                f.write(b'ID3\x03\x00\x00\x00\x00\x00\x00\x00')
            return mp3_path

    def _create_simple_mp3_audio(self, filepath: str, script: str, duration: float, voice_type: str):
        """Create a simple MP3 audio file"""
        try:
            from moviepy.editor import AudioClip
            import numpy as np

            # Create a simple tone audio
            def make_frame(_):
                return np.array([0.1, 0.1])  # Simple stereo tone

            audio_clip = AudioClip(make_frame, duration=duration)
            audio_clip.write_audiofile(filepath, fps=22050, verbose=False, logger=None)
            audio_clip.close()
            audio_clip.close()

            print(f"✅ Created simple MP3 audio: {filepath}")

        except Exception as e:
            print(f"⚠️ Simple MP3 creation failed: {str(e)}")
            # Create minimal MP3 file as last resort
            with open(filepath, 'wb') as f:
                # Write minimal MP3 header
                f.write(b'ID3\x03\x00\x00\x00\x00\x00\x00\x00')

    def get_available_voices(self) -> Dict[str, Any]:
        """Get list of available voices from Google Cloud TTS"""
        if not self.client:
            return {
                'success': False,
                'error': 'Google Cloud TTS not available'
            }
        
        try:
            voices = self.client.list_voices()
            
            english_voices = []
            for voice in voices.voices:
                if voice.language_codes[0].startswith('en'):
                    english_voices.append({
                        'name': voice.name,
                        'language': voice.language_codes[0],
                        'gender': voice.ssml_gender.name
                    })
            
            return {
                'success': True,
                'voices': english_voices[:10]  # Return first 10 English voices
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def generate_voice_sample(self, text: str, voice_type: str = 'hindi_male', language: str = 'hindi') -> Dict[str, Any]:
        """Generate a short voice sample for preview (served from memory, no file storage)"""
        try:
            import time
            timestamp = int(time.time() * 1000)
            filename = f"voice_sample_{voice_type}_{timestamp}.mp3"

            print(f"🎤 Generating voice sample: {voice_type} ({language}) (in memory)")

            # Try Google Cloud TTS first if available
            if hasattr(self, 'client') and self.client:
                try:
                    # Set voice configuration for sample
                    self._set_voice_config(voice_type, language)

                    # Configure voice settings
                    voice = texttospeech.VoiceSelectionParams(
                        language_code=self.current_voice_config['language_code'],
                        name=self.current_voice_config['name'],
                        ssml_gender=self.current_voice_config['gender']
                    )

                    # Configure audio settings
                    audio_config = texttospeech.AudioConfig(
                        audio_encoding=texttospeech.AudioEncoding.MP3,
                        speaking_rate=0.9,
                        pitch=0.0,
                        volume_gain_db=0.0
                    )

                    # Create synthesis input
                    synthesis_input = texttospeech.SynthesisInput(text=text)

                    # Perform the text-to-speech request
                    response = self.client.synthesize_speech(
                        input=synthesis_input,
                        voice=voice,
                        audio_config=audio_config
                    )

                    # Convert to base64 for serving from memory
                    audio_base64 = base64.b64encode(response.audio_content).decode('utf-8')

                    print(f"✅ Voice sample generated with Google TTS: {filename} (in memory)")

                    return {
                        'success': True,
                        'filename': filename,
                        'audio_data': response.audio_content,
                        'audio_base64': audio_base64,
                        'duration': 3.0,
                        'voice_type': voice_type,
                        'language': language,
                        'type': 'google_tts_sample'
                    }

                except Exception as e:
                    print(f"⚠️ Google TTS sample failed: {str(e)}, trying fallback...")

            # Use edge-tts for voice samples (your preferred method)
            print(f"🎤 Creating voice sample with edge-tts...")

            try:
                # Create voice sample using edge-tts with your specific voice mappings
                sample_audio_data = self._create_edge_tts_audio_memory(text, voice_type)

                if sample_audio_data:
                    audio_base64 = base64.b64encode(sample_audio_data).decode('utf-8')
                    print(f"✅ Edge-TTS voice sample created: {filename} (in memory)")

                    return {
                        'success': True,
                        'filename': filename,
                        'audio_data': sample_audio_data,
                        'audio_base64': audio_base64,
                        'duration': 3.0,
                        'voice_type': voice_type,
                        'language': language,
                        'type': 'edge_tts_sample'
                    }
                else:
                    print(f"⚠️ Edge-TTS sample failed, trying gTTS fallback...")
                    # Fallback to gTTS for samples
                    sample_audio_data = self._create_gtts_audio_memory(text, voice_type)

                    if sample_audio_data:
                        audio_base64 = base64.b64encode(sample_audio_data).decode('utf-8')
                        print(f"✅ gTTS voice sample created: {filename} (in memory)")

                        return {
                            'success': True,
                            'filename': filename,
                            'audio_data': sample_audio_data,
                            'audio_base64': audio_base64,
                            'duration': 3.0,
                            'voice_type': voice_type,
                            'language': language,
                            'type': 'gtts_sample'
                        }
                    else:
                        print(f"⚠️ gTTS sample also failed, creating minimal sample...")
                        # Create minimal audio sample as final fallback
                        sample_audio_data = self._create_memory_audio_sample(text, voice_type, language)
                        audio_base64 = base64.b64encode(sample_audio_data).decode('utf-8')

                        print(f"✅ Minimal voice sample created: {filename} (in memory)")

                        return {
                            'success': True,
                            'filename': filename,
                            'audio_data': sample_audio_data,
                            'audio_base64': audio_base64,
                            'duration': 3.0,
                            'voice_type': voice_type,
                            'language': language,
                            'type': 'memory_sample'
                        }
            except Exception as fallback_error:
                print(f"❌ Fallback audio creation failed: {str(fallback_error)}")
                # Return a minimal response for testing
                minimal_audio = self._create_minimal_mp3()
                audio_base64 = base64.b64encode(minimal_audio).decode('utf-8')

                return {
                    'success': True,
                    'filename': filename,
                    'audio_data': minimal_audio,
                    'audio_base64': audio_base64,
                    'duration': 1.0,
                    'voice_type': voice_type,
                    'language': language,
                    'type': 'minimal_sample'
                }

        except Exception as e:
            print(f"❌ Voice sample generation failed: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def _create_memory_audio_sample(self, text: str, voice_type: str, language: str) -> bytes:
        """Create audio sample using edge-tts first, then fallbacks"""
        try:
            # Try edge-tts first (your preferred method with specific voice mappings)
            print(f"🎤 Trying edge-tts for sample: {voice_type}")
            edge_audio = self._create_edge_tts_audio_memory(text, voice_type)
            if edge_audio:
                print(f"✅ Edge-TTS sample created: {len(edge_audio)} bytes")
                return edge_audio

            # Fallback to gTTS
            print(f"🎤 Edge-TTS failed, trying gTTS for sample: {voice_type}")
            gtts_audio = self._create_gtts_audio_memory(text, voice_type)
            if gtts_audio:
                print(f"✅ gTTS sample created: {len(gtts_audio)} bytes")
                return gtts_audio

            # Final fallback: create using file-based methods
            print(f"🎤 Both edge-tts and gTTS failed, using file-based fallback")
            import tempfile
            import os

            # Create a temporary file for audio generation
            with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as temp_file:
                temp_path = temp_file.name

            try:
                # Try to create audio using available methods
                success = False

                # Try pyttsx3 as fallback
                try:
                    self._create_audio_with_pyttsx3(temp_path, text, 3.0, voice_type)
                    success = os.path.exists(temp_path) and os.path.getsize(temp_path) > 100
                except Exception as e:
                    print(f"⚠️ pyttsx3 failed: {str(e)}")

                # If all else fails, create a simple beep
                if not success:
                    self._create_fallback_beep_audio(temp_path, 3.0)
                    success = os.path.exists(temp_path)

                # Read the audio file into memory
                if success and os.path.exists(temp_path):
                    with open(temp_path, 'rb') as f:
                        audio_data = f.read()
                    print(f"📁 Created file-based audio sample: {len(audio_data)} bytes")
                    return audio_data
                else:
                    print("⚠️ All audio generation methods failed, creating minimal audio")
                    # Return a minimal valid MP3 file (silence)
                    return self._create_minimal_mp3()

            finally:
                # Clean up temporary file
                if os.path.exists(temp_path):
                    try:
                        os.unlink(temp_path)
                    except:
                        pass

        except Exception as e:
            print(f"❌ Memory audio sample creation failed: {str(e)}")
            return self._create_minimal_mp3()

    def _create_minimal_mp3(self) -> bytes:
        """Create a minimal valid MP3 file with silence"""
        # This is a very basic MP3 file with silence (about 1 second)
        # MP3 header + minimal audio frames
        mp3_data = bytes([
            # MP3 Header
            0xFF, 0xFB, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
            # Minimal audio frames (silence)
        ]) + (b'\x00' * 500)  # 500 bytes of silence

        print(f"📁 Created minimal MP3: {len(mp3_data)} bytes")
        return mp3_data
