import { useState, useEffect } from 'react'

const ImageModal = ({ isOpen, onClose, image, imageIndex, totalImages, onNavigate }) => {
  console.log('ImageModal render:', { isOpen, image: !!image, imageIndex, totalImages })

  useEffect(() => {
    const handleKeyDown = (e) => {
      if (!isOpen) return
      
      switch (e.key) {
        case 'Escape':
          onClose()
          break
        case 'ArrowLeft':
          if (imageIndex > 0) {
            onNavigate(imageIndex - 1)
          }
          break
        case 'ArrowRight':
          if (imageIndex < totalImages - 1) {
            onNavigate(imageIndex + 1)
          }
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isOpen, imageIndex, totalImages, onClose, onNavigate])

  if (!isOpen) {
    console.log('Modal not open, isOpen:', isOpen)
    return null
  }

  if (!image) {
    console.log('No image provided to modal, image:', image, 'imageIndex:', imageIndex)
    return null
  }

  console.log('Modal rendering with image:', image.filename)

  const downloadImage = () => {
    try {
      const imageBlob = new Blob([Uint8Array.from(atob(image.base64), c => c.charCodeAt(0))], { type: 'image/png' })
      const url = URL.createObjectURL(imageBlob)
      const a = document.createElement('a')
      a.href = url
      a.download = image.filename || `scene_${imageIndex + 1}.png`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Failed to download image:', error)
    }
  }

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/80 backdrop-blur-sm">
      {/* Modal Content */}
      <div className="relative max-w-6xl max-h-[90vh] mx-4 bg-gray-900 rounded-xl overflow-hidden shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between p-4 bg-gray-800 border-b border-gray-700">
          <div className="flex items-center space-x-4">
            <h3 className="text-lg font-semibold text-white">
              Scene {imageIndex + 1} of {totalImages}
            </h3>
            <span className="text-sm text-gray-400">
              {image.filename}
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Download Button */}
            <button
              onClick={downloadImage}
              className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
              title="Download Image"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </button>
            
            {/* Close Button */}
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
              title="Close (Esc)"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Image Container */}
        <div className="relative">
          <img
            src={`data:image/png;base64,${image.base64 || image.image_base64}`}
            alt={`Generated Scene ${imageIndex + 1}`}
            className="max-w-full max-h-[70vh] object-contain mx-auto block"
            onError={(e) => console.log('Image load error:', e, 'Image data:', image)}
          />
          
          {/* Navigation Arrows */}
          {imageIndex > 0 && (
            <button
              onClick={() => onNavigate(imageIndex - 1)}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 p-3 bg-black/50 hover:bg-black/70 text-white rounded-full transition-all duration-200 hover:scale-110"
              title="Previous Image (←)"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
          )}
          
          {imageIndex < totalImages - 1 && (
            <button
              onClick={() => onNavigate(imageIndex + 1)}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 p-3 bg-black/50 hover:bg-black/70 text-white rounded-full transition-all duration-200 hover:scale-110"
              title="Next Image (→)"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          )}
        </div>

        {/* Footer with Image Info */}
        <div className="p-4 bg-gray-800 border-t border-gray-700">
          <div className="flex items-center justify-between text-sm text-gray-400">
            <div className="flex items-center space-x-4">
              <span>Click image or use arrow keys to navigate</span>
              <span className="text-gray-500">•</span>
              <span>Press Esc to close</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-blue-400">💡</span>
              <span>Right-click to save image</span>
            </div>
          </div>
        </div>
      </div>

      {/* Background Click to Close */}
      <div 
        className="absolute inset-0 -z-10" 
        onClick={onClose}
      />
    </div>
  )
}

export default ImageModal
