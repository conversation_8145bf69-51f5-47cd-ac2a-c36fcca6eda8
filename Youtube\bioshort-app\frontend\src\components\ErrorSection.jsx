import React from 'react'

const ErrorSection = ({ message, onRetry }) => {
  return (
    <section className="glass-card p-8 mb-8 border-red-500/50 bg-red-500/10 fade-in">
      <div className="max-w-md mx-auto text-center">
        {/* Error Icon */}
        <div className="text-6xl mb-4">❌</div>
        
        {/* Error Title */}
        <h3 className="text-red-200 text-xl font-bold mb-4">
          Oops! Something went wrong
        </h3>
        
        {/* Error Message */}
        <p className="text-red-100 mb-6 bg-red-500/20 p-4 rounded-lg border border-red-400/30">
          {message}
        </p>
        
        {/* Retry Button */}
        <button
          onClick={onRetry}
          className="btn-primary bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700"
        >
          🔄 Try Again
        </button>
        
        {/* Troubleshooting Tips */}
        <div className="mt-8 p-4 bg-blue-500/20 rounded-lg border border-blue-400/30 text-left">
          <h4 className="text-blue-200 font-semibold mb-2">💡 Troubleshooting Tips:</h4>
          <ul className="text-blue-300 text-sm space-y-1">
            <li>• Make sure you entered a valid famous person's name</li>
            <li>• Check your internet connection</li>
            <li>• Try a different person if the issue persists</li>
            <li>• Refresh the page and try again</li>
          </ul>
        </div>
        
        {/* Common Issues */}
        <div className="mt-4 p-4 bg-yellow-500/20 rounded-lg border border-yellow-400/30 text-left">
          <h4 className="text-yellow-200 font-semibold mb-2">⚠️ Common Issues:</h4>
          <ul className="text-yellow-300 text-sm space-y-1">
            <li>• Server might be starting up (wait 30 seconds)</li>
            <li>• API rate limits (try again in a few minutes)</li>
            <li>• Very obscure names might not work well</li>
          </ul>
        </div>
      </div>
    </section>
  )
}

export default ErrorSection
