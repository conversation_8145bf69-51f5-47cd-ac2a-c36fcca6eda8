import React, { createContext, useContext, useReducer, useEffect } from 'react'

// Initial state
const initialState = {
  isGenerating: false,
  currentStep: '',
  progress: 0,
  personName: '',
  results: {
    story: null,
    images: [],
    audio: null,
    video: null,
    youtubeContent: null
  },
  error: null,
  startTime: null,
  estimatedTime: null
}

// Action types
const actionTypes = {
  START_GENERATION: 'START_GENERATION',
  UPDATE_PROGRESS: 'UPDATE_PROGRESS',
  SET_STORY: 'SET_STORY',
  ADD_IMAGE: 'ADD_IMAGE',
  SET_AUDIO: 'SET_AUDIO',
  SET_VIDEO: 'SET_VIDEO',
  SET_YOUTUBE_CONTENT: 'SET_YOUTUBE_CONTENT',
  SET_ERROR: 'SET_ERROR',
  CANCEL_GENERATION: 'CANCEL_GENERATION',
  RESET_GENERATION: 'RESET_GENERATION',
  RESTORE_STATE: 'RESTORE_STATE'
}

// Reducer function
const videoGenerationReducer = (state, action) => {
  switch (action.type) {
    case actionTypes.START_GENERATION:
      return {
        ...state,
        isGenerating: true,
        currentStep: 'Starting generation...',
        progress: 0,
        personName: action.payload.personName,
        error: null,
        startTime: Date.now(),
        estimatedTime: 60000 // 1 minute estimate
      }

    case actionTypes.UPDATE_PROGRESS:
      return {
        ...state,
        currentStep: action.payload.step,
        progress: action.payload.progress
      }

    case actionTypes.SET_STORY:
      return {
        ...state,
        results: {
          ...state.results,
          story: action.payload
        }
      }

    case actionTypes.ADD_IMAGE:
      return {
        ...state,
        results: {
          ...state.results,
          images: [...state.results.images, action.payload]
        }
      }

    case actionTypes.SET_AUDIO:
      return {
        ...state,
        results: {
          ...state.results,
          audio: action.payload
        }
      }

    case actionTypes.SET_VIDEO:
      return {
        ...state,
        results: {
          ...state.results,
          video: action.payload
        },
        isGenerating: false,
        currentStep: 'Generation complete!',
        progress: 100
      }

    case actionTypes.SET_YOUTUBE_CONTENT:
      return {
        ...state,
        results: {
          ...state.results,
          youtubeContent: action.payload
        }
      }

    case actionTypes.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        isGenerating: false
      }

    case actionTypes.CANCEL_GENERATION:
      return {
        ...state,
        isGenerating: false,
        currentStep: 'Generation cancelled',
        error: 'Generation was cancelled by user'
      }

    case actionTypes.RESET_GENERATION:
      return initialState

    case actionTypes.RESTORE_STATE:
      return {
        ...state,
        ...action.payload
      }

    default:
      return state
  }
}

// Create context
const VideoGenerationContext = createContext()

// Provider component
export const VideoGenerationProvider = ({ children }) => {
  const [state, dispatch] = useReducer(videoGenerationReducer, initialState)

  // Persist state to localStorage (excluding large video/audio data)
  useEffect(() => {
    if (state.isGenerating || state.results.video) {
      try {
        // Create a lightweight version without large binary data
        const lightState = {
          ...state,
          results: {
            ...state.results,
            // Don't store large binary data in localStorage
            video: state.results.video ? { filename: state.results.video.filename } : null,
            audio: state.results.audio ? { filename: state.results.audio.filename } : null
          }
        }
        localStorage.setItem('videoGenerationState', JSON.stringify(lightState))
      } catch (error) {
        console.warn('⚠️ Could not save state to localStorage:', error.message)
        // Clear localStorage if it's full
        if (error.name === 'QuotaExceededError') {
          localStorage.removeItem('videoGenerationState')
        }
      }
    }
  }, [state])

  // Prevent page refresh during video generation
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (state.isGenerating) {
        e.preventDefault()
        e.returnValue = 'Video generation is in progress. Are you sure you want to leave?'
        return e.returnValue
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => window.removeEventListener('beforeunload', handleBeforeUnload)
  }, [state.isGenerating])

  // Restore state from localStorage on mount
  useEffect(() => {
    const savedState = localStorage.getItem('videoGenerationState')
    if (savedState) {
      try {
        const parsedState = JSON.parse(savedState)
        // Only restore if there was an active generation (not completed videos)
        if (parsedState.isGenerating) {
          console.log('🔄 Restoring generation state from localStorage')
          dispatch({ type: actionTypes.RESTORE_STATE, payload: parsedState })
        } else {
          console.log('ℹ️ Found completed generation in localStorage, clearing it')
          localStorage.removeItem('videoGenerationState')
        }
      } catch (error) {
        console.error('Error restoring video generation state:', error)
        localStorage.removeItem('videoGenerationState')
      }
    }
  }, [])

  // Action creators
  const actions = {
    startGeneration: (personName) => {
      dispatch({ type: actionTypes.START_GENERATION, payload: { personName } })
    },

    updateProgress: (step, progress) => {
      dispatch({ type: actionTypes.UPDATE_PROGRESS, payload: { step, progress } })
    },

    setStory: (story) => {
      dispatch({ type: actionTypes.SET_STORY, payload: story })
    },

    addImage: (image) => {
      dispatch({ type: actionTypes.ADD_IMAGE, payload: image })
    },

    setAudio: (audio) => {
      dispatch({ type: actionTypes.SET_AUDIO, payload: audio })
    },

    setVideo: (video) => {
      dispatch({ type: actionTypes.SET_VIDEO, payload: video })
    },

    setYoutubeContent: (content) => {
      dispatch({ type: actionTypes.SET_YOUTUBE_CONTENT, payload: content })
    },

    setError: (error) => {
      dispatch({ type: actionTypes.SET_ERROR, payload: error })
    },

    cancelGeneration: () => {
      dispatch({ type: actionTypes.CANCEL_GENERATION })
    },

    resetGeneration: () => {
      localStorage.removeItem('videoGenerationState')
      dispatch({ type: actionTypes.RESET_GENERATION })
    }
  }

  return (
    <VideoGenerationContext.Provider value={{ state, actions }}>
      {children}
    </VideoGenerationContext.Provider>
  )
}

// Custom hook to use the context
export const useVideoGeneration = () => {
  const context = useContext(VideoGenerationContext)
  if (!context) {
    throw new Error('useVideoGeneration must be used within a VideoGenerationProvider')
  }
  return context
}

export default VideoGenerationContext
