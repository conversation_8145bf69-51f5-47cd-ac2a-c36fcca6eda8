name: Keep Render Service Alive

on:
  schedule:
    # Run every 12 minutes to prevent Render free tier from sleeping
    - cron: '*/12 * * * *'
  workflow_dispatch: # Allow manual triggering

jobs:
  keep-alive:
    runs-on: ubuntu-latest
    steps:
      - name: Ping Keep-Alive Endpoint
        run: |
          # Replace with your actual Render URL when deployed
          curl -f https://your-app.onrender.com/api/keep-alive || echo "Service might be sleeping, will wake up on next request"
          
      - name: Ping Health Check
        run: |
          # Also ping health check endpoint
          curl -f https://your-app.onrender.com/api/health || echo "Health check failed, service might be starting"
