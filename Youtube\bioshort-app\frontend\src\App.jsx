import React from 'react'
import { <PERSON>rowserRouter as Router, Routes, Route } from 'react-router-dom'
import AuthWrapper from './components/AuthWrapper'
import Navigation from './components/Navigation'
import VideoGeneratorPage from './pages/VideoGeneratorPage'
import AutomationPage from './pages/AutomationPage'
import Footer from './components/Footer'
import { VideoGenerationProvider } from './context/VideoGenerationContext'

function App() {
  return (
    <VideoGenerationProvider>
      <Router>
        <AuthWrapper>
          <div className="min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-indigo-900">
            <div className="container mx-auto px-4 py-8 space-y-8">
              <Navigation />
              <Routes>
                <Route path="/" element={<VideoGeneratorPage />} />
                <Route path="/automation" element={<AutomationPage />} />
              </Routes>
              <Footer />
            </div>
          </div>
        </AuthWrapper>
      </Router>
    </VideoGenerationProvider>
  )
}

export default App
