// API Configuration
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL

// API Service Functions
export const api = {
  // Health check
  async health() {
    const response = await fetch(`${API_BASE_URL}/health`)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    return response.json()
  },

  // Generate video
  async generateVideo(requestData, signal = null) {
    // Handle both string (legacy) and object (new) formats
    const payload = typeof requestData === 'string'
      ? { person_name: requestData }
      : requestData

    const fetchOptions = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload)
    }

    // Add abort signal if provided
    if (signal) {
      fetchOptions.signal = signal
    }

    console.log('🌐 Making video generation request to:', `${API_BASE_URL}/generate-video`)
    console.log('📦 Request payload:', payload)

    const response = await fetch(`${API_BASE_URL}/generate-video`, fetchOptions)

    console.log('📡 Response status:', response.status)
    console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()))

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ HTTP error response:', errorText)
      throw new Error(`HTTP error! status: ${response.status} - ${errorText}`)
    }

    const contentType = response.headers.get('content-type')
    if (!contentType || !contentType.includes('application/json')) {
      const responseText = await response.text()
      console.error('❌ Non-JSON response:', responseText)
      throw new Error('Response is not JSON')
    }

    const result = await response.json()
    console.log('✅ Video generation API response:', result)
    return result
  },

  // Download video
  downloadVideo(filename) {
    window.open(`${API_BASE_URL}/download-video/${filename}`, '_blank')
  },

  // Download audio
  downloadAudio(filename) {
    window.open(`${API_BASE_URL}/download-audio/${filename}`, '_blank')
  },

  // Preview image
  getImageUrl(filename) {
    return `${API_BASE_URL}/preview-image/${filename}`
  },

  // Get available voices
  async getVoices() {
    const response = await fetch(`${API_BASE_URL}/voices`)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    return response.json()
  },

  // Upload video to YouTube
  async uploadToYouTube(uploadData) {
    const response = await fetch(`${API_BASE_URL}/upload-to-youtube`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(uploadData)
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const contentType = response.headers.get('content-type')
    if (!contentType || !contentType.includes('application/json')) {
      throw new Error('Response is not JSON')
    }

    return response.json()
  },

  // Get YouTube quota information
  async getYouTubeQuotaInfo() {
    const response = await fetch(`${API_BASE_URL}/youtube-quota-info`)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    return response.json()
  }
}

// Main function for video generation with error handling
export const generateVideo = async (requestData, signal = null) => {
  try {
    const personName = typeof requestData === 'string' ? requestData : requestData.person_name
    const voiceType = typeof requestData === 'object' ? requestData.voice_type : 'hindi_male'
    const language = typeof requestData === 'object' ? requestData.language : 'hindi'

    console.log('🚀 Starting video generation for:', personName)
    console.log('🎤 Voice settings:', { voiceType, language })
    console.log('🌐 API URL:', `${API_BASE_URL}/generate-video`)

    const result = await api.generateVideo(requestData, signal)
    
    console.log('✅ Video generation successful:', result)
    return result

  } catch (error) {
    console.error('❌ Video generation failed:', error)
    
    // Handle different types of errors
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      throw new Error('Unable to connect to server. Please ensure the backend is running.')
    } else if (error.message.includes('HTTP error')) {
      throw new Error(`Server error: ${error.message}`)
    } else if (error.message.includes('JSON')) {
      throw new Error('Invalid response from server. Please try again.')
    } else {
      throw new Error(error.message || 'An unexpected error occurred')
    }
  }
}

// Health check function
export const checkAPIHealth = async () => {
  try {
    console.log('🔍 Checking API health...')
    const result = await api.health()
    console.log('✅ API is healthy:', result)
    return { success: true, data: result }
  } catch (error) {
    console.error('❌ API health check failed:', error)
    return { 
      success: false, 
      error: error.message || 'API health check failed' 
    }
  }
}

// Utility function to validate person name
export const validatePersonName = (name) => {
  if (!name || typeof name !== 'string') {
    return { valid: false, error: 'Name is required' }
  }
  
  const trimmedName = name.trim()
  
  if (trimmedName.length === 0) {
    return { valid: false, error: 'Name cannot be empty' }
  }
  
  if (trimmedName.length < 2) {
    return { valid: false, error: 'Name must be at least 2 characters long' }
  }
  
  if (trimmedName.length > 100) {
    return { valid: false, error: 'Name is too long (max 100 characters)' }
  }
  
  // Check for basic name pattern (letters, spaces, common punctuation)
  const namePattern = /^[a-zA-Z\s\-\.\']+$/
  if (!namePattern.test(trimmedName)) {
    return { valid: false, error: 'Name contains invalid characters' }
  }
  
  return { valid: true, name: trimmedName }
}

// Export default API object
export default api
