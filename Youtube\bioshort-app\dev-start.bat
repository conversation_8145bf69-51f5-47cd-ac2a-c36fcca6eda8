@echo off
echo 🔥 Starting BioShort Development Environment with Hot Reload
echo ================================================================

REM Set development environment variables
set FLASK_ENV=development
set FLASK_DEBUG=true

echo 📁 Starting in development mode...
echo 🔥 Hot reload enabled for both frontend and backend!
echo.

REM Start both frontend and backend with hot reload
echo 🚀 Starting services...
start "Backend (Hot Reload)" cmd /k "cd backend && python app.py"
timeout /t 3 /nobreak >nul
start "Frontend (Hot Reload)" cmd /k "cd frontend && npm run dev"

echo.
echo ✅ Development servers starting...
echo 🌐 Frontend: http://localhost:5173 (Hot Reload ✅)
echo 🔧 Backend:  http://localhost:5000 (Hot Reload ✅)
echo.
echo 💡 Make changes to your files and see them update instantly!
echo 🛑 Press Ctrl+C in each window to stop the servers
pause
