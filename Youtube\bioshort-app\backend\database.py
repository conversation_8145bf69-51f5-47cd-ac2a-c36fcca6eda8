import sqlite3
import json
from datetime import datetime
from typing import List, Dict, Optional

class Database:
    def __init__(self, db_path: str = "bioshort.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize the database with required tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create people queue table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS people_queue (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                title_language TEXT NOT NULL DEFAULT 'english',
                description_language TEXT NOT NULL DEFAULT 'english',
                audio_language TEXT NOT NULL DEFAULT 'hindi',
                voice_type TEXT NOT NULL DEFAULT 'hindi_male',
                status TEXT NOT NULL DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                processed_at TIMESTAMP NULL,
                video_url TEXT NULL,
                error_message TEXT NULL
            )
        ''')
        
        # Create processing log table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS processing_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                person_name TEXT NOT NULL,
                status TEXT NOT NULL,
                message TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def add_person_to_queue(self, name: str, title_language: str = 'english', description_language: str = 'english', audio_language: str = 'hindi', voice_type: str = 'hindi_male') -> bool:
        """Add a person to the processing queue"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check if person already exists in pending status
            cursor.execute(
                "SELECT id FROM people_queue WHERE name = ? AND status = 'pending'",
                (name,)
            )
            
            if cursor.fetchone():
                conn.close()
                return False  # Person already in queue
            
            cursor.execute('''
                INSERT INTO people_queue (name, title_language, description_language, audio_language, voice_type)
                VALUES (?, ?, ?, ?, ?)
            ''', (name, title_language, description_language, audio_language, voice_type))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            print(f"Error adding person to queue: {str(e)}")
            return False
    
    def get_queue(self) -> List[Dict]:
        """Get all people in the queue"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT id, name, title_language, description_language, audio_language, voice_type, status,
                       created_at, processed_at, video_url, error_message
                FROM people_queue
                ORDER BY created_at ASC
            ''')
            
            rows = cursor.fetchall()
            conn.close()
            
            return [
                {
                    'id': row[0],
                    'name': row[1],
                    'title_language': row[2],
                    'description_language': row[3],
                    'audio_language': row[4],
                    'voice_type': row[5],
                    'status': row[6],
                    'created_at': row[7],
                    'processed_at': row[8],
                    'video_url': row[9],
                    'error_message': row[10]
                }
                for row in rows
            ]
            
        except Exception as e:
            print(f"Error getting queue: {str(e)}")
            return []
    
    def get_next_pending_person(self) -> Optional[Dict]:
        """Get the next person to process"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT id, name, title_language, description_language, audio_language, voice_type
                FROM people_queue
                WHERE status = 'pending'
                ORDER BY created_at ASC
                LIMIT 1
            ''')
            
            row = cursor.fetchone()
            conn.close()
            
            if row:
                return {
                    'id': row[0],
                    'name': row[1],
                    'title_language': row[2],
                    'description_language': row[3],
                    'audio_language': row[4],
                    'voice_type': row[5]
                }
            return None
            
        except Exception as e:
            print(f"Error getting next person: {str(e)}")
            return None
    
    def update_person_status(self, person_id: int, status: str, video_url: str = None, error_message: str = None):
        """Update person's processing status"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE people_queue 
                SET status = ?, processed_at = CURRENT_TIMESTAMP, video_url = ?, error_message = ?
                WHERE id = ?
            ''', (status, video_url, error_message, person_id))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Error updating person status: {str(e)}")
    
    def remove_person_from_queue(self, person_id: int) -> bool:
        """Remove a person from the queue"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("DELETE FROM people_queue WHERE id = ?", (person_id,))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            print(f"Error removing person from queue: {str(e)}")
            return False
    
    def add_processing_log(self, person_name: str, status: str, message: str = None):
        """Add a processing log entry"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO processing_log (person_name, status, message)
                VALUES (?, ?, ?)
            ''', (person_name, status, message))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Error adding processing log: {str(e)}")
    
    def get_processing_logs(self, limit: int = 50) -> List[Dict]:
        """Get recent processing logs"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT person_name, status, message, created_at
                FROM processing_log 
                ORDER BY created_at DESC
                LIMIT ?
            ''', (limit,))
            
            rows = cursor.fetchall()
            conn.close()
            
            return [
                {
                    'person_name': row[0],
                    'status': row[1],
                    'message': row[2],
                    'created_at': row[3]
                }
                for row in rows
            ]
            
        except Exception as e:
            print(f"Error getting processing logs: {str(e)}")
            return []
