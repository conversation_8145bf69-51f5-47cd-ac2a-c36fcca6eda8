# Render Blueprint for BioShort App
# This file defines the services to deploy on Render

services:
  # Main Application (Backend + Frontend + Airflow in one container)
  - type: web
    name: bioshort-app
    env: docker
    dockerfilePath: ./Dockerfile.render
    plan: starter
    region: oregon
    branch: render
    buildCommand: ""
    startCommand: ""
    dockerBuildArgs:
      VITE_API_BASE_URL: https://bioshort.onrender.com/api
    envVars:
      - key: FLASK_ENV
        value: production
      - key: FLASK_DEBUG
        value: false
      - key: VITE_API_BASE_URL
        value: https://bioshort.onrender.com/api
      - key: GOOGLE_API_KEY
        sync: false
      - key: YOUTUBE_CLIENT_ID
        sync: false
      - key: YOUTUBE_CLIENT_SECRET
        sync: false
      - key: YOUTUBE_REFRESH_TOKEN
        sync: false
      - key: AIRFLOW__CORE__EXECUTOR
        value: SequentialExecutor
      - key: AIRFLOW__CORE__FERNET_KEY
        generateValue: true
      - key: AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION
        value: false
      - key: AIRFLOW__CORE__LOAD_EXAMPLES
        value: false
      - key: _AIRFLOW_WWW_USER_USERNAME
        value: airflow
      - key: _AIRFLOW_WWW_USER_PASSWORD
        value: airflow
    healthCheckPath: /health
