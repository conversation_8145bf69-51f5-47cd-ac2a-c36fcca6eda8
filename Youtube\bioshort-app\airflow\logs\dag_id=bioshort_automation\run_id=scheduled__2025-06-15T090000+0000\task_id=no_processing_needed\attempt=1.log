[2025-06-16T19:12:37.412+0000] {taskinstance.py:1159} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: bioshort_automation.no_processing_needed scheduled__2025-06-15T09:00:00+00:00 [queued]>
[2025-06-16T19:12:37.429+0000] {taskinstance.py:1159} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: bioshort_automation.no_processing_needed scheduled__2025-06-15T09:00:00+00:00 [queued]>
[2025-06-16T19:12:37.435+0000] {taskinstance.py:1361} INFO - Starting attempt 1 of 2
[2025-06-16T19:12:37.456+0000] {taskinstance.py:1382} INFO - Executing <Task(PythonOperator): no_processing_needed> on 2025-06-15 09:00:00+00:00
[2025-06-16T19:12:37.464+0000] {standard_task_runner.py:57} INFO - Started process 208 to run task
[2025-06-16T19:12:37.471+0000] {standard_task_runner.py:84} INFO - Running: ['***', 'tasks', 'run', 'bioshort_automation', 'no_processing_needed', 'scheduled__2025-06-15T09:00:00+00:00', '--job-id', '17', '--raw', '--subdir', 'DAGS_FOLDER/bioshort_automation.py', '--cfg-path', '/tmp/tmpzjjuedl3']
[2025-06-16T19:12:37.477+0000] {standard_task_runner.py:85} INFO - Job 17: Subtask no_processing_needed
[2025-06-16T19:12:37.594+0000] {task_command.py:415} INFO - Running <TaskInstance: bioshort_automation.no_processing_needed scheduled__2025-06-15T09:00:00+00:00 [running]> on host cdc7242b3dcf
[2025-06-16T19:12:37.732+0000] {taskinstance.py:1660} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='bioshort' AIRFLOW_CTX_DAG_ID='bioshort_automation' AIRFLOW_CTX_TASK_ID='no_processing_needed' AIRFLOW_CTX_EXECUTION_DATE='2025-06-15T09:00:00+00:00' AIRFLOW_CTX_TRY_NUMBER='1' AIRFLOW_CTX_DAG_RUN_ID='scheduled__2025-06-15T09:00:00+00:00'
[2025-06-16T19:12:37.734+0000] {logging_mixin.py:151} INFO - ℹ️ No people in queue. Automation task completed with no action.
[2025-06-16T19:12:37.735+0000] {python.py:194} INFO - Done. Returned value was: No processing needed
[2025-06-16T19:12:37.773+0000] {taskinstance.py:1400} INFO - Marking task as SUCCESS. dag_id=bioshort_automation, task_id=no_processing_needed, execution_date=20250615T090000, start_date=20250616T191237, end_date=20250616T191237
[2025-06-16T19:12:37.803+0000] {local_task_job_runner.py:228} INFO - Task exited with return code 0
[2025-06-16T19:12:37.827+0000] {taskinstance.py:2784} INFO - 0 downstream tasks scheduled from follow-on schedule check
