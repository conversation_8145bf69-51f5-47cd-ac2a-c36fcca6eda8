# 🔥 Development Setup with Hot Reload

This guide helps you set up hot reload for both frontend and backend development.

## 🚀 Quick Start

### Option 1: Automated Script (Recommended)

**Windows:**
```bash
# Double-click or run in terminal
dev-start.bat
```

**Linux/Mac:**
```bash
./dev-start.sh
```

### Option 2: Manual Setup

**Terminal 1 - Backend (Hot Reload):**
```bash
cd backend
set FLASK_ENV=development  # Windows
export FLASK_ENV=development  # Linux/Mac
set FLASK_DEBUG=true  # Windows  
export FLASK_DEBUG=true  # Linux/Mac
python app.py
```

**Terminal 2 - Frontend (Hot Reload):**
```bash
cd frontend
npm run dev:hot
```

## 🔥 Hot Reload Features

### Frontend (Vite + React)
- ✅ **Instant Updates** - Changes appear immediately
- ✅ **Component State Preservation** - React Fast Refresh
- ✅ **Error Overlay** - See errors directly in browser
- ✅ **CSS Hot Reload** - Styles update without page refresh

### Backend (Flask)
- ✅ **Auto Restart** - Server restarts on file changes
- ✅ **Debug Mode** - Detailed error messages
- ✅ **Threaded** - Better performance during development

## 📱 Development URLs

- **Frontend:** http://localhost:5173
- **Backend API:** http://localhost:5000
- **Health Check:** http://localhost:5000/api/health

## 💡 Development Tips

### Frontend Hot Reload
- Edit any `.jsx`, `.js`, `.css` file
- Changes appear instantly in browser
- Component state is preserved
- No manual refresh needed

### Backend Hot Reload  
- Edit any `.py` file in backend/
- Flask automatically restarts
- API changes are immediately available
- Debug information in terminal

### Common Issues

**Frontend not updating?**
- Check browser console for errors
- Try hard refresh (Ctrl+F5)
- Restart with `npm run dev:hot`

**Backend not reloading?**
- Check FLASK_DEBUG=true is set
- Look for syntax errors in terminal
- Restart manually if needed

## 🛠 Advanced Configuration

### Vite Config (frontend/vite.config.js)
- Fast Refresh enabled
- Polling for file changes
- Error overlay enabled
- Optimized dependencies

### Flask Config (backend/app.py)
- Auto-reloader enabled
- Debug mode enabled
- Threading enabled

## 🎯 Workflow

1. **Start Development Servers** (using script or manually)
2. **Open Browser** to http://localhost:5173
3. **Make Changes** to any file
4. **See Updates Instantly** - no manual refresh needed!

Happy coding! 🚀
