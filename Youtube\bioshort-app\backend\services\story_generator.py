#!/usr/bin/env python3
"""
Story Generator Service
Uses Gemini Pro to generate inspiring biographical stories
"""

import json
import google.generativeai as genai
from typing import Dict, List, Any
from config.env import env

class StoryGenerator:
    def __init__(self):
        """Initialize Gemini Pro for story generation"""
        api_key = env.GOOGLE_API_KEY
        if not api_key:
            raise ValueError("GOOGLE_API_KEY environment variable is required")
        
        genai.configure(api_key=api_key)
        # Try different Gemini models for better quota management
        try:
            self.model = genai.GenerativeModel('gemini-1.5-flash')
            self.model_name = 'gemini-1.5-flash'
            print("✅ Story Generator initialized with Gemini 1.5 Flash")
        except Exception as e:
            print(f"⚠️ Gemini 1.5 Flash failed, trying Gemini Pro: {str(e)}")
            try:
                self.model = genai.GenerativeModel('gemini-pro')
                self.model_name = 'gemini-pro'
                print("✅ Story Generator initialized with Gemini Pro")
            except Exception as e2:
                print(f"❌ All Gemini models failed: {str(e2)}")
                raise ValueError("No Gemini models available")

        self.used_names = set()  # Track used names to avoid repetition
    
    def generate_story(self, person_name: str) -> Dict[str, Any]:
        """
        Generate an inspiring biographical story with 7-8 segments
        Following plan.md specifications: 150-200 words total
        """
        try:
            prompt = self._create_story_prompt(person_name)
            
            print(f"📝 Generating story for {person_name}...")
            print(f"🤖 Using model: {self.model_name}")

            # Add rate limit handling
            try:
                response = self.model.generate_content(prompt)
            except Exception as api_error:
                error_str = str(api_error)
                if "429" in error_str or "quota" in error_str.lower():
                    print(f"❌ Gemini API quota exceeded. Please wait and try again.")
                    return {
                        'success': False,
                        'error': f'API quota exceeded. Please wait {53} seconds and try again. Or check your Google Cloud billing at: https://console.cloud.google.com/'
                    }
                else:
                    raise api_error
            
            if not response.text:
                return {
                    'success': False,
                    'error': 'No story content generated'
                }
            
            # Parse the response into structured segments
            story_data = self._parse_story_response(response.text, person_name)
            
            return {
                'success': True,
                'story': story_data,
                'raw_response': response.text
            }
            
        except Exception as e:
            print(f"❌ Story generation error: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _create_story_prompt(self, person_name: str) -> str:
        """Create optimized prompt for Gemini Pro"""
        return f"""
Create an inspiring biographical story about {person_name} for a 1-minute YouTube Short video.

Requirements:
- Write exactly 7-8 segments (each 20-25 words)
- Total length: 150-200 words
- Focus on key achievements, struggles, and inspiring moments
- Make it engaging and motivational
- Include vivid scene descriptions for image generation

Format your response as JSON:
{{
    "title": "The Story of {person_name}",
    "segments": [
        {{
            "text": "Segment narration text (20-25 words)",
            "scene_description": "Detailed visual description for AI image generation",
            "emotion": "inspiring/determined/triumphant/etc"
        }}
    ]
}}

Focus on:
1. Early life/childhood challenges
2. Key discoveries or achievements  
3. Obstacles overcome
4. Legacy and impact
5. Inspirational message

Make each segment visually compelling and emotionally engaging.
"""
    
    def _parse_story_response(self, response_text: str, person_name: str) -> Dict[str, Any]:
        """Parse Gemini response into structured story data"""
        try:
            # Try to extract JSON from response
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            
            if json_start != -1 and json_end != -1:
                json_text = response_text[json_start:json_end]
                story_data = json.loads(json_text)
                
                # Validate structure
                if 'segments' in story_data and isinstance(story_data['segments'], list):
                    # Ensure we have 7-8 segments
                    segments = story_data['segments'][:8]  # Limit to 8 max
                    
                    # Validate each segment
                    validated_segments = []
                    for i, segment in enumerate(segments):
                        if isinstance(segment, dict) and 'text' in segment:
                            validated_segment = {
                                'text': segment.get('text', '').strip(),
                                'scene_description': segment.get('scene_description', f'Scene showing {person_name}').strip(),
                                'emotion': segment.get('emotion', 'inspiring').strip(),
                                'index': i
                            }
                            validated_segments.append(validated_segment)
                    
                    return {
                        'title': story_data.get('title', f'The Story of {person_name}'),
                        'segments': validated_segments,
                        'total_segments': len(validated_segments),
                        'estimated_duration': len(validated_segments) * 7.5  # ~7.5 seconds per segment
                    }
            
            # Fallback: Parse as plain text
            return self._parse_plain_text_story(response_text, person_name)
            
        except json.JSONDecodeError:
            # Fallback to plain text parsing
            return self._parse_plain_text_story(response_text, person_name)
    
    def _parse_plain_text_story(self, text: str, person_name: str) -> Dict[str, Any]:
        """Fallback parser for plain text responses"""
        # Split into sentences and group into segments
        sentences = [s.strip() for s in text.split('.') if s.strip()]
        
        segments = []
        current_segment = ""
        
        for sentence in sentences:
            if len(current_segment + sentence) < 25:  # Target 20-25 words
                current_segment += sentence + ". "
            else:
                if current_segment:
                    segments.append({
                        'text': current_segment.strip(),
                        'scene_description': f'Scene depicting {person_name} - {current_segment[:50]}...',
                        'emotion': 'inspiring',
                        'index': len(segments)
                    })
                current_segment = sentence + ". "
        
        # Add final segment
        if current_segment:
            segments.append({
                'text': current_segment.strip(),
                'scene_description': f'Final scene showing {person_name}\'s legacy',
                'emotion': 'triumphant',
                'index': len(segments)
            })
        
        # Ensure we have 7-8 segments
        while len(segments) < 7:
            segments.append({
                'text': f'{person_name} continues to inspire people around the world.',
                'scene_description': f'Inspirational scene showing {person_name}\'s lasting impact',
                'emotion': 'inspiring',
                'index': len(segments)
            })
        
        return {
            'title': f'The Story of {person_name}',
            'segments': segments[:8],  # Limit to 8 segments
            'total_segments': len(segments[:8]),
            'estimated_duration': len(segments[:8]) * 7.5
        }

    def generate_random_names(self) -> Dict[str, Any]:
        """Generate random famous person names using Gemini AI"""
        print("🎲 Starting AI-powered random names generation...")

        try:
            import random
            from datetime import datetime

            # Add maximum randomness to ensure different results each time
            random_seed = random.randint(1000, 9999)
            timestamp = datetime.now().microsecond
            random_number = random.randint(100, 999)

            # Expanded categories with more variety to avoid repetition
            all_categories = [
                "Trending Bollywood actors", "Famous Hollywood celebrities", "Popular YouTubers and influencers",
                "Tech billionaires and entrepreneurs", "Viral TikTok stars", "Famous athletes and sports icons",
                "Popular musicians and singers", "Well-known politicians and leaders", "Famous scientists and innovators",
                "Trending social media personalities", "Popular content creators", "Famous business leaders",
                "Well-known historical figures", "Popular TV and movie stars", "Famous writers and authors",
                "Trending international celebrities", "Popular Indian personalities", "Famous global icons",
                "Revolutionary leaders and activists", "Nobel Prize winners", "Olympic champions",
                "Space explorers and astronauts", "Famous directors and filmmakers", "Legendary musicians",
                "Tech innovators and inventors", "Fashion icons and designers", "Famous chefs and food personalities",
                "Gaming and esports personalities", "Podcast hosts and media personalities", "Famous photographers",
                "Renowned architects", "Famous dancers and choreographers", "Stand-up comedians",
                "Famous journalists and news anchors", "Spiritual leaders and philosophers", "Environmental activists"
            ]
            categories = random.sample(all_categories, random.randint(3, 6))

            all_regions = [
                "trending globally", "popular in India and worldwide", "famous internationally",
                "well-known celebrities", "trending personalities", "popular figures worldwide",
                "famous people everyone knows", "trending icons and celebrities",
                "from different continents", "representing diverse cultures", "from various time periods",
                "modern and historical figures", "contemporary and classic personalities", "global and local icons",
                "from entertainment industry", "from technology sector", "from sports world",
                "from political arena", "from scientific community", "from business world"
            ]
            regions = random.choice(all_regions)

            # Create exclusion list from previously used names
            exclusion_list = list(self.used_names) if self.used_names else []
            exclusion_text = f"NEVER include these names: {', '.join(exclusion_list[:20])}" if exclusion_list else "Generate completely fresh names"

            prompt = f"""
GENERATE 10 COMPLETELY DIFFERENT AND UNIQUE famous person names that have NEVER been generated before.

RANDOMIZATION PARAMETERS:
- Seed: {random_seed}
- Timestamp: {timestamp}
- Random: {random_number}
- Focus: {', '.join(categories)} {regions}

EXCLUSION RULE: {exclusion_text}

CRITICAL REQUIREMENTS:
1. Generate FRESH, DIFFERENT names each time - ZERO repeats from previous generations
2. Focus on TRENDING and FAMOUS people that are:
   - Well-known celebrities and public figures
   - Popular on social media and trending platforms
   - Famous actors, musicians, athletes, entrepreneurs
   - Viral personalities and content creators
   - Well-known historical figures that are still popular
   - Trending politicians, scientists, and innovators
3. Include diverse mix of:
   - Current trending celebrities (Bollywood, Hollywood, YouTube, etc.)
   - Famous business leaders and tech entrepreneurs
   - Popular athletes and sports stars
   - Well-known musicians and artists
   - Trending social media personalities
   - Famous historical figures that are still popular today
4. Prioritize FAMOUS and RECOGNIZABLE names over obscure figures
5. Include both international celebrities and popular Indian personalities
6. AVOID ALL COMMON/OVERUSED NAMES like Einstein, Gandhi, Jobs, Curie, Tesla, etc.

VARIETY REQUIREMENTS:
- Mix different time periods (ancient, modern, contemporary)
- Include different fields (tech, sports, entertainment, politics, science)
- Balance genders and cultures
- Include both historical and current personalities

Return ONLY a clean JSON array:
["Name 1", "Name 2", "Name 3", "Name 4", "Name 5", "Name 6", "Name 7", "Name 8", "Name 9", "Name 10"]

NO explanations, NO additional text, JUST the JSON array.
"""

            print("🤖 Requesting fresh names from Gemini AI...")

            # Use a shorter timeout to prevent hanging
            response = self.model.generate_content(
                prompt,
                generation_config={
                    'temperature': 0.9,  # High creativity
                    'top_p': 0.8,
                    'top_k': 40,
                    'max_output_tokens': 200,  # Limit output for faster response
                }
            )

            if not response.text:
                print("⚠️ Empty response from Gemini, using fallback")
                return self._generate_fallback_names()

            print(f"📝 Gemini response: {response.text[:100]}...")

            # Parse the JSON response
            response_text = response.text.strip()
            json_start = response_text.find('[')
            json_end = response_text.rfind(']') + 1

            if json_start != -1 and json_end != -1:
                json_text = response_text[json_start:json_end]
                names = json.loads(json_text)

                if isinstance(names, list) and len(names) > 0:
                    # Filter and validate names, excluding already used ones
                    valid_names = []
                    for name in names:
                        if isinstance(name, str) and name.strip():
                            clean_name = name.strip()
                            # Check if name is not in used names (case-insensitive)
                            if not any(clean_name.lower() == used.lower() for used in self.used_names):
                                valid_names.append(clean_name)
                                self.used_names.add(clean_name)

                    if len(valid_names) >= 3:  # Lower threshold for more flexibility
                        print(f"✅ AI generated {len(valid_names)} fresh names (total used: {len(self.used_names)})!")
                        return {
                            'success': True,
                            'names': valid_names[:10],  # Limit to 10 names
                            'source': 'gemini_ai',
                            'raw_response': response.text[:200]
                        }
                    else:
                        print(f"⚠️ Only {len(valid_names)} new names found. Used names cache size: {len(self.used_names)}")
                        # If we have too many used names (>100), clear some old ones
                        if len(self.used_names) > 100:
                            # Keep only the most recent 50 names
                            recent_names = list(self.used_names)[-50:]
                            self.used_names = set(recent_names)
                            print(f"🔄 Cleared old names, keeping {len(self.used_names)} recent ones")

                        # Try again with all names from current response
                        valid_names = [name.strip() for name in names if isinstance(name, str) and name.strip()]
                        for name in valid_names:
                            self.used_names.add(name)
                        return {
                            'success': True,
                            'names': valid_names[:10],
                            'source': 'gemini_ai_partial_reset',
                            'raw_response': response.text[:200]
                        }

            print("⚠️ Failed to parse AI response, using fallback")
            return self._generate_fallback_names()

        except Exception as e:
            print(f"❌ AI names generation error: {str(e)}")
            return self._generate_fallback_names()

    def generate_youtube_content(self, person_name: str, language: str = 'english', story_data: dict = None) -> Dict[str, Any]:
        """Generate YouTube titles and description based on the actual story content"""
        print(f"📺 Generating YouTube content for {person_name} in {language}...")

        try:
            import random
            from datetime import datetime

            # Add randomness for variety
            random_seed = random.randint(1000, 9999)
            timestamp = datetime.now().microsecond

            # Extract story details for dynamic content generation
            story_summary = ""
            key_achievements = []
            struggles = []

            if story_data and 'segments' in story_data:
                # Extract key information from story segments
                for segment in story_data['segments']:
                    text = segment.get('text', '')
                    story_summary += text + " "

                    # Identify achievements and struggles from the text
                    if any(word in text.lower() for word in ['achieved', 'success', 'breakthrough', 'invented', 'discovered', 'founded', 'created', 'won', 'became']):
                        key_achievements.append(text.strip())
                    if any(word in text.lower() for word in ['struggle', 'challenge', 'difficult', 'persecution', 'hardship', 'obstacle', 'faced', 'overcome']):
                        struggles.append(text.strip())

                story_summary = story_summary.strip()[:300]  # Limit for prompt

            # Language-specific prompts
            if language.lower() == 'hindi':
                prompt = f"""
Generate YouTube content for a biographical video about {person_name} in HINDI language.

STORY CONTEXT:
{story_summary}

KEY ACHIEVEMENTS: {', '.join(key_achievements[:2]) if key_achievements else 'Various achievements'}
STRUGGLES: {', '.join(struggles[:2]) if struggles else 'Life challenges'}

RANDOMIZATION: {random_seed}-{timestamp}

Generate:
1. THREE different YouTube titles (engaging, emotional, with emojis and hashtags)
2. ONE detailed description (inspiring, with emojis and hashtags)

IMPORTANT: Base the titles and description on the ACTUAL STORY CONTENT above, not generic templates.

TITLE REQUIREMENTS:
- Use HINDI language (Devanagari script)
- Include emotional words like "संघर्ष कहानी", "असली कहानी", "प्रेरणादायक", "अनकही कहानी", "जीवन संघर्ष"
- Add relevant emojis (💔, 🔥, ✨, 🇮🇳, 🌟, 💪, ⚡, etc.)
- Include #shorts hashtag
- Make them clickable, emotional, and LONGER (descriptive)
- Include specific achievements or struggles of {person_name}
- Use compelling phrases like "कैसे बने", "सफलता का राज", "अद्भुत यात्रा"
- Keep under 150 characters (longer than before)

DESCRIPTION REQUIREMENTS:
- Use HINDI language (Devanagari script)
- Start with inspiring hook about {person_name}
- Include their achievements and impact
- Add call-to-action for likes, comments, subscribe
- Include relevant hashtags in Hindi and English
- Use emojis throughout
- Make it engaging and shareable

Return ONLY this JSON format:
{{
    "titles": [
        "Title 1 with emojis 🔥 #shorts",
        "Title 2 with emojis ✨ #shorts",
        "Title 3 with emojis 💔 #shorts"
    ],
    "description": "Full description with emojis and hashtags..."
}}

NO explanations, JUST the JSON.
"""
            else:  # English
                prompt = f"""
Generate YouTube content for a biographical video about {person_name} in ENGLISH language.

STORY CONTEXT:
{story_summary}

KEY ACHIEVEMENTS: {', '.join(key_achievements[:2]) if key_achievements else 'Various achievements'}
STRUGGLES: {', '.join(struggles[:2]) if struggles else 'Life challenges'}

RANDOMIZATION: {random_seed}-{timestamp}

Generate:
1. THREE different YouTube titles (engaging, emotional, with emojis and hashtags)
2. ONE detailed description (inspiring, with emojis and hashtags)

IMPORTANT: Base the titles and description on the ACTUAL STORY CONTENT above, not generic templates.

TITLE REQUIREMENTS:
- Use ENGLISH language
- Include emotional words like "untold story", "inspiring journey", "legacy", "incredible life", "remarkable story"
- Add relevant emojis (💔, 🔥, ✨, 🌟, 💪, ⚡, 🎯, etc.)
- Include #shorts hashtag
- Make them clickable, emotional, and LONGER (descriptive)
- Include specific achievements, struggles, or impact of {person_name}
- Use compelling phrases like "How they became", "The secret behind", "Amazing journey of"
- Keep under 150 characters (longer than before)

DESCRIPTION REQUIREMENTS:
- Use ENGLISH language
- Start with inspiring hook about {person_name}
- Include their achievements and impact
- Add call-to-action for likes, comments, subscribe
- Include relevant hashtags
- Use emojis throughout
- Make it engaging and shareable

Return ONLY this JSON format:
{{
    "titles": [
        "Title 1 with emojis 🔥 #shorts",
        "Title 2 with emojis ✨ #shorts",
        "Title 3 with emojis 💔 #shorts"
    ],
    "description": "Full description with emojis and hashtags..."
}}

NO explanations, JUST the JSON.
"""

            print("🤖 Requesting YouTube content from Gemini AI...")

            response = self.model.generate_content(
                prompt,
                generation_config={
                    'temperature': 0.8,
                    'top_p': 0.9,
                    'top_k': 40,
                    'max_output_tokens': 800,
                }
            )

            if not response.text:
                print("⚠️ Empty response from Gemini")
                return self._generate_fallback_youtube_content(person_name, language, story_data)

            print(f"📝 Gemini response: {response.text[:100]}...")

            # Parse the JSON response
            response_text = response.text.strip()
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1

            if json_start != -1 and json_end != -1:
                json_text = response_text[json_start:json_end]
                content = json.loads(json_text)

                if 'titles' in content and 'description' in content:
                    if isinstance(content['titles'], list) and len(content['titles']) >= 3:
                        print(f"✅ Generated YouTube content in {language}!")
                        return {
                            'success': True,
                            'titles': content['titles'][:3],  # Limit to 3 titles
                            'description': content['description'],
                            'language': language,
                            'source': 'gemini_ai'
                        }

            print("⚠️ Failed to parse AI response, using fallback")
            return self._generate_fallback_youtube_content(person_name, language, story_data)

        except Exception as e:
            print(f"❌ YouTube content generation error: {str(e)}")
            return self._generate_fallback_youtube_content(person_name, language, story_data)

    def _generate_fallback_youtube_content(self, person_name: str, language: str, story_data: dict = None) -> Dict[str, Any]:
        """Fallback YouTube content generation with story-based content if available"""
        print(f"⚠️ Using fallback YouTube content for {person_name} in {language}")

        # Try to extract some context from story if available
        story_context = ""
        if story_data and 'segments' in story_data:
            # Get first few segments for context
            first_segments = story_data['segments'][:3]
            story_context = " ".join([seg.get('text', '') for seg in first_segments])[:200]

        if language.lower() == 'hindi':
            titles = [
                f"{person_name} की अद्भुत जीवन यात्रा 🔥 | कैसे बने महान व्यक्तित्व | संघर्ष से सफलता तक ✨ #shorts",
                f"{person_name} का असली संघर्ष 💔 | Zero से Hero बनने की पूरी कहानी | प्रेरणादायक सफर 🇮🇳 #shorts",
                f"{person_name} की अनकही कहानी 🌟 | जीवन संघर्ष, सफलता और विरासत | Amazing Life Story 💪 #shorts"
            ]
            description = f"""देखिए {person_name} की अद्भुत और प्रेरणादायक जीवन कहानी जो आपको हमेशा प्रेरित करती रहेगी! ✨

📽️ इस वीडियो में आप देखेंगे कैसे {person_name} ने अपने संघर्षों से जीत हासिल की और इतिहास में अपना नाम दर्ज कराया।

🔔 अगर आपको यह वीडियो पसंद आया तो लाइक, कमेंट और सब्सक्राइब करना न भूलें!

💬 कमेंट में बताएं: {person_name} आपके लिए क्या मायने रखते हैं?

#{person_name.replace(' ', '')} #प्रेरणा #संघर्ष #सफलता #भारत #InspirationalVideo #Biography #Motivation #shorts"""
        else:  # English
            titles = [
                f"{person_name}'s Incredible Life Journey 🔥 | How They Became a Legend | From Struggle to Success ✨ #shorts",
                f"The Untold Story of {person_name} 💔 | Amazing Life Struggles & Triumphs | Zero to Hero Journey 🌟 #shorts",
                f"{person_name}'s Remarkable Legacy 💪 | Life Lessons, Inspiration & Impact | The Complete Story ⚡ #shorts"
            ]
            description = f"""Witness the incredible and inspiring legacy of {person_name} that continues to motivate generations around the world! ✨

📽️ This video captures the essence of determination, courage, and excellence that defined {person_name}'s remarkable journey through life.

🔔 Don't forget to like, comment, and subscribe for more powerful stories and inspirational content!

💬 Tell us in the comments: What does {person_name} mean to you?

#{person_name.replace(' ', '')} #Inspiration #Legacy #Biography #Motivation #InspirationalVideo #History #shorts"""

        return {
            'success': True,
            'titles': titles,
            'description': description,
            'language': language,
            'source': 'fallback'
        }

    def _generate_fallback_names(self) -> Dict[str, Any]:
        """Emergency fallback - only used when AI completely fails"""
        print("⚠️ Using emergency fallback names (AI failed)")

        # Diverse trending and famous personalities fallback - rotate different sets
        import time
        fallback_sets = [
            # Set 1: Tech & Business
            ["Elon Musk", "Sundar Pichai", "Satya Nadella", "Tim Cook", "Jeff Bezos", "Warren Buffett", "Ratan Tata", "Mukesh Ambani"],
            # Set 2: Entertainment & Sports
            ["Shah Rukh Khan", "Priyanka Chopra", "Deepika Padukone", "Virat Kohli", "Cristiano Ronaldo", "Lionel Messi", "Serena Williams", "Roger Federer"],
            # Set 3: Politics & Leaders
            ["Narendra Modi", "Barack Obama", "Nelson Mandela", "Winston Churchill", "John F. Kennedy", "Mahatma Gandhi", "Martin Luther King Jr.", "Abraham Lincoln"],
            # Set 4: Science & Innovation
            ["A.P.J. Abdul Kalam", "Stephen Hawking", "Marie Curie", "Nikola Tesla", "Charles Darwin", "Isaac Newton", "Albert Einstein", "Leonardo da Vinci"],
            # Set 5: Modern Celebrities
            ["Dwayne Johnson", "Oprah Winfrey", "Taylor Swift", "Beyoncé", "Robert Downey Jr.", "Will Smith", "Jennifer Lawrence", "Ryan Reynolds"],
            # Set 6: Historical Figures
            ["Cleopatra", "Julius Caesar", "Alexander the Great", "Napoleon Bonaparte", "Queen Elizabeth I", "George Washington", "Benjamin Franklin", "Thomas Edison"]
        ]

        # Use time-based rotation to ensure different sets
        set_index = int(time.time() / 300) % len(fallback_sets)  # Change every 5 minutes
        emergency_names = fallback_sets[set_index]

        return {
            'success': True,
            'names': emergency_names,
            'source': 'emergency_fallback',
            'warning': 'AI generation failed - using emergency names'
        }

    def get_sample_story(self, person_name: str) -> Dict[str, Any]:
        """Generate a sample story for testing purposes"""
        return {
            'title': f'The Story of {person_name}',
            'segments': [
                {
                    'text': f'{person_name} was born into humble beginnings, showing early signs of brilliance.',
                    'scene_description': f'Young {person_name} as a child, studying by candlelight in a simple home',
                    'emotion': 'hopeful',
                    'index': 0
                },
                {
                    'text': f'Despite facing numerous challenges, {person_name} never gave up on their dreams.',
                    'scene_description': f'{person_name} working hard, surrounded by books and determination',
                    'emotion': 'determined',
                    'index': 1
                },
                {
                    'text': f'Through perseverance and dedication, {person_name} made groundbreaking discoveries.',
                    'scene_description': f'{person_name} in a laboratory or study, making important discoveries',
                    'emotion': 'triumphant',
                    'index': 2
                },
                {
                    'text': f'Their work changed the world and continues to inspire millions today.',
                    'scene_description': f'Global impact scene showing {person_name}\'s lasting legacy',
                    'emotion': 'inspiring',
                    'index': 3
                }
            ],
            'total_segments': 4,
            'estimated_duration': 30
        }


