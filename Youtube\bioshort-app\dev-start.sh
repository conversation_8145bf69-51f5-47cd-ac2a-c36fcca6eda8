#!/bin/bash

echo "🔥 Starting BioShort Development Environment with Hot Reload"
echo "================================================================"

# Set development environment variables
export FLASK_ENV=development
export FLASK_DEBUG=true

echo "📁 Starting in development mode..."
echo "🔥 Hot reload enabled for both frontend and backend!"
echo ""

# Function to start backend
start_backend() {
    echo "🔧 Starting Backend with Hot Reload..."
    cd backend
    python app.py
}

# Function to start frontend  
start_frontend() {
    echo "🌐 Starting Frontend with Hot Reload..."
    cd frontend
    npm run dev
}

echo "🚀 Starting services..."

# Start backend in background
start_backend &
BACKEND_PID=$!

# Wait a moment for backend to start
sleep 3

# Start frontend in background
start_frontend &
FRONTEND_PID=$!

echo ""
echo "✅ Development servers started!"
echo "🌐 Frontend: http://localhost:5173 (Hot Reload ✅)"
echo "🔧 Backend:  http://localhost:5000 (Hot Reload ✅)"
echo ""
echo "💡 Make changes to your files and see them update instantly!"
echo "🛑 Press Ctrl+C to stop all servers"

# Wait for Ctrl+C
trap "echo ''; echo '🛑 Stopping servers...'; kill $BACKEND_PID $FRONTEND_PID; exit" INT
wait
