# BioShort App - Render Deployment Guide

This guide will help you deploy your BioShort application to Render for 24/7 automation.

## Prerequisites

1. ✅ GitHub account with your code in the `automation` branch
2. ✅ Render account (free tier available)
3. ✅ Your API keys ready (Google/Gemini, YouTube)

## Step-by-Step Deployment

### Step 1: Prepare Your GitHub Repository

1. **Push all changes to GitHub:**
   ```bash
   git add .
   git commit -m "Add Render deployment configuration"
   git push origin render
   ```

2. **Verify your repository has these new files:**
   - `Dockerfile.render`
   - `render.yaml`
   - `start.sh`
   - `nginx.conf`
   - `supervisord.conf`
   - `postgres/Dockerfile`

### Step 2: Deploy to Render

1. **Go to Render Dashboard:**
   - Visit: https://render.com
   - Sign up/Login with your GitHub account

2. **Create New Web Service:**
   - Click "New +" → "Web Service"
   - Connect your GitHub repository
   - Select your repository and `render` branch

3. **Configure the Service:**
   - **Name:** `bioshort-app`
   - **Environment:** `Docker`
   - **Dockerfile Path:** `./Dockerfile.render`
   - **Plan:** `Starter` (Free tier)
   - **Region:** `Oregon` (or closest to you)

4. **Set Environment Variables:**
   Click "Advanced" and add these environment variables:
   ```
   FLASK_ENV=production
   FLASK_DEBUG=false
   GOOGLE_API_KEY=AIzaSyCELuz9ZGZ8JCVgV69jJYG0REpMYiGrex0
   YOUTUBE_CLIENT_ID=************-r9641h32toea2djco0hdtbma1m30di4t.apps.googleusercontent.com
   YOUTUBE_CLIENT_SECRET=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
   YOUTUBE_REFRESH_TOKEN=1//04o0yipMeacDACgYIARAAGAQSNwF-L9IrmtxI1lqPQqAV9L5OuvKs5dQLE79T7LADQhPwFN6QwdByMi01j_zqrZB-dMmVb2vjHRM
   AIRFLOW__CORE__EXECUTOR=LocalExecutor
   AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION=false
   AIRFLOW__CORE__LOAD_EXAMPLES=false
   _AIRFLOW_WWW_USER_USERNAME=airflow
   _AIRFLOW_WWW_USER_PASSWORD=airflow
   ```

5. **Deploy:**
   - Click "Create Web Service"
   - Wait for the build and deployment (5-10 minutes)

### Step 3: Access Your Application

Once deployed, you'll get a URL like: `https://bioshort-app.onrender.com`

**Application URLs:**
- **Frontend:** `https://bioshort-app.onrender.com/`
- **Backend API:** `https://bioshort-app.onrender.com/api/`
- **Airflow Dashboard:** `https://bioshort-app.onrender.com/airflow/`
- **Health Check:** `https://bioshort-app.onrender.com/health`

### Step 4: Configure Automation

1. **Access Airflow Dashboard:**
   - Go to: `https://bioshort-app.onrender.com/airflow/`
   - Login with: `airflow` / `airflow`

2. **Enable Your DAGs:**
   - Find your automation DAGs
   - Toggle them ON
   - Verify the schedule is set correctly

3. **Test the Application:**
   - Visit the frontend
   - Add a person to the automation queue
   - Check that videos are generated
   - Verify YouTube uploads work

## Important Notes

### Free Tier Limitations
- **Sleep Mode:** Free services sleep after 15 minutes of inactivity
- **Build Time:** Limited build minutes per month
- **Memory:** 512MB RAM limit
- **Storage:** Ephemeral (files are lost on restart)

### Keeping Your App Awake
To prevent sleep mode from affecting automation:

1. **Use a monitoring service** like UptimeRobot (free):
   - Monitor: `https://bioshort-app.onrender.com/health`
   - Check every 5 minutes
   - This keeps your app awake

2. **Or upgrade to a paid plan** ($7/month) for always-on service

### Data Persistence
- SQLite database is stored in `/app/data/` (ephemeral)
- For production, consider upgrading to PostgreSQL add-on
- Generated videos are served from memory and deleted after serving

## Troubleshooting

### Build Failures
1. Check build logs in Render dashboard
2. Verify all files are committed to GitHub
3. Check Dockerfile syntax

### Runtime Issues
1. Check service logs in Render dashboard
2. Verify environment variables are set correctly
3. Test health endpoint: `/health`

### Automation Not Working
1. Check Airflow logs in dashboard
2. Verify DAGs are enabled
3. Check API keys are working
4. Ensure app is not sleeping during scheduled times

## Monitoring

**Health Check Endpoint:**
```
GET https://bioshort-app.onrender.com/health
```

**Expected Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "services": {
    "backend": "running",
    "airflow": "running"
  }
}
```

## Support

If you encounter issues:
1. Check Render service logs
2. Verify environment variables
3. Test locally with Docker first
4. Check GitHub repository has all files

Your automation will now run 24/7 without needing your PC to be on! 🎉
