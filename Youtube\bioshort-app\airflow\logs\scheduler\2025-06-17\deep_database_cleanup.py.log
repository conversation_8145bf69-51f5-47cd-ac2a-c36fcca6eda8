[2025-06-17T17:45:11.971+0000] {processor.py:157} INFO - Started process (PID=188) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:45:11.974+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-17T17:45:11.978+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:45:11.977+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:45:12.107+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:45:12.173+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:45:12.173+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:45:12.220+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:45:12.220+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-17T17:45:12.275+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.316 seconds
[2025-06-17T17:45:42.717+0000] {processor.py:157} INFO - Started process (PID=209) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:45:42.719+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-17T17:45:42.721+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:45:42.721+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:45:42.767+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:45:42.896+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:45:42.896+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:45:42.934+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:45:42.934+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-17T17:45:42.985+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.277 seconds
[2025-06-17T17:46:13.716+0000] {processor.py:157} INFO - Started process (PID=232) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:46:13.718+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-17T17:46:13.720+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:46:13.719+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:46:13.759+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:46:13.826+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:46:13.825+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:46:13.851+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:46:13.851+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-17T17:46:13.903+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.195 seconds
[2025-06-17T17:46:44.055+0000] {processor.py:157} INFO - Started process (PID=255) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:46:44.058+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-17T17:46:44.059+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:46:44.059+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:46:44.122+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:46:44.212+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:46:44.211+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:46:44.235+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:46:44.235+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-17T17:46:44.468+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.420 seconds
[2025-06-17T17:47:14.578+0000] {processor.py:157} INFO - Started process (PID=278) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:47:14.580+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-17T17:47:14.582+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:47:14.581+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:47:14.828+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:47:14.877+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:47:14.876+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:47:14.895+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:47:14.895+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-17T17:47:14.932+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.359 seconds
[2025-06-17T17:47:45.296+0000] {processor.py:157} INFO - Started process (PID=301) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:47:45.300+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-17T17:47:45.302+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:47:45.301+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:47:45.375+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:47:45.445+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:47:45.445+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:47:45.478+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:47:45.478+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-17T17:47:45.532+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.243 seconds
[2025-06-17T17:48:15.874+0000] {processor.py:157} INFO - Started process (PID=324) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:48:15.876+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-17T17:48:15.877+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:48:15.877+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:48:15.920+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:48:15.973+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:48:15.973+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:48:15.998+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:48:15.998+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-17T17:48:16.048+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.180 seconds
[2025-06-17T17:48:46.900+0000] {processor.py:157} INFO - Started process (PID=347) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:48:46.903+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-17T17:48:46.906+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:48:46.906+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:48:46.958+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:48:47.019+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:48:47.018+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:48:47.041+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:48:47.040+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-17T17:48:47.088+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.200 seconds
[2025-06-17T17:49:17.767+0000] {processor.py:157} INFO - Started process (PID=370) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:49:17.769+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-17T17:49:17.771+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:49:17.771+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:49:17.816+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:49:17.873+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:49:17.872+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:49:17.895+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:49:17.894+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-17T17:49:17.941+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.179 seconds
[2025-06-17T17:49:48.560+0000] {processor.py:157} INFO - Started process (PID=393) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:49:48.565+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-17T17:49:48.568+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:49:48.568+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:49:48.617+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:49:48.672+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:49:48.672+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:49:48.696+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:49:48.695+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-17T17:49:48.743+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.190 seconds
[2025-06-17T17:50:19.573+0000] {processor.py:157} INFO - Started process (PID=416) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:50:19.575+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-17T17:50:19.577+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:50:19.576+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:50:19.618+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:50:19.676+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:50:19.676+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:50:19.698+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:50:19.698+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-17T17:50:19.741+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.174 seconds
[2025-06-17T17:50:50.415+0000] {processor.py:157} INFO - Started process (PID=441) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:50:50.417+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-17T17:50:50.420+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:50:50.419+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:50:50.483+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:50:50.566+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:50:50.565+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:50:50.586+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:50:50.585+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-17T17:50:50.640+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.232 seconds
[2025-06-17T17:51:21.035+0000] {processor.py:157} INFO - Started process (PID=464) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:51:21.037+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-17T17:51:21.042+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:51:21.039+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:51:21.084+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:51:21.144+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:51:21.144+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:51:21.167+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:51:21.167+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-17T17:51:21.209+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.181 seconds
[2025-06-17T17:51:52.127+0000] {processor.py:157} INFO - Started process (PID=479) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:51:52.129+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-17T17:51:52.136+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:51:52.134+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:51:52.190+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:51:52.249+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:51:52.249+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:51:52.269+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:51:52.269+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-17T17:51:52.311+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.192 seconds
[2025-06-17T17:52:22.711+0000] {processor.py:157} INFO - Started process (PID=502) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:52:22.713+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-17T17:52:22.715+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:52:22.714+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:52:22.768+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:52:22.821+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:52:22.820+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:52:22.842+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:52:22.841+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-17T17:52:22.883+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.177 seconds
[2025-06-17T17:52:53.259+0000] {processor.py:157} INFO - Started process (PID=525) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:52:53.262+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-17T17:52:53.265+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:52:53.263+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:52:53.331+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:52:53.437+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:52:53.437+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:52:53.496+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:52:53.495+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-17T17:52:53.585+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.340 seconds
[2025-06-17T17:53:24.662+0000] {processor.py:157} INFO - Started process (PID=548) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:53:24.664+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-17T17:53:24.666+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:53:24.666+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:53:24.727+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:53:24.804+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:53:24.804+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:53:24.834+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:53:24.834+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-17T17:53:24.883+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.230 seconds
[2025-06-17T17:53:55.744+0000] {processor.py:157} INFO - Started process (PID=572) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:53:55.748+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-17T17:53:55.750+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:53:55.750+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:53:55.802+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:53:55.859+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:53:55.859+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:53:55.884+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:53:55.884+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-17T17:53:55.922+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.184 seconds
[2025-06-17T17:54:26.101+0000] {processor.py:157} INFO - Started process (PID=595) to work on /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:54:26.108+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/deep_database_cleanup.py for tasks to queue
[2025-06-17T17:54:26.111+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:54:26.110+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:54:26.167+0000] {processor.py:839} INFO - DAG(s) dict_keys(['deep_database_cleanup']) retrieved from /opt/airflow/dags/deep_database_cleanup.py
[2025-06-17T17:54:26.232+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:54:26.232+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:54:26.260+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:54:26.260+0000] {dag.py:3677} INFO - Setting next_dagrun for deep_database_cleanup to None, run_after=None
[2025-06-17T17:54:26.306+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/deep_database_cleanup.py took 0.211 seconds
