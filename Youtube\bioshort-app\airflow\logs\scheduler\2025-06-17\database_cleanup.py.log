[2025-06-17T17:45:11.974+0000] {processor.py:157} INFO - Started process (PID=191) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:45:11.977+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-17T17:45:11.981+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:45:11.980+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:45:12.114+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:45:12.185+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:45:12.184+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:45:12.220+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:45:12.219+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-17T17:45:12.279+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.315 seconds
[2025-06-17T17:45:42.736+0000] {processor.py:157} INFO - Started process (PID=212) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:45:42.738+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-17T17:45:42.740+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:45:42.739+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:45:42.796+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:45:42.897+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:45:42.896+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:45:42.940+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:45:42.939+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-17T17:45:42.989+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.261 seconds
[2025-06-17T17:46:13.728+0000] {processor.py:157} INFO - Started process (PID=235) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:46:13.731+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-17T17:46:13.733+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:46:13.733+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:46:13.779+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:46:13.841+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:46:13.841+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:46:13.865+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:46:13.865+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-17T17:46:13.903+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.180 seconds
[2025-06-17T17:46:44.075+0000] {processor.py:157} INFO - Started process (PID=258) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:46:44.080+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-17T17:46:44.082+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:46:44.082+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:46:44.157+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:46:44.228+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:46:44.227+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:46:44.254+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:46:44.253+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-17T17:46:44.470+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.405 seconds
[2025-06-17T17:47:15.216+0000] {processor.py:157} INFO - Started process (PID=283) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:47:15.219+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-17T17:47:15.221+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:47:15.220+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:47:15.278+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:47:15.331+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:47:15.330+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:47:15.352+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:47:15.351+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-17T17:47:15.390+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.181 seconds
[2025-06-17T17:47:45.637+0000] {processor.py:157} INFO - Started process (PID=306) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:47:45.640+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-17T17:47:45.642+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:47:45.641+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:47:45.714+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:47:45.775+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:47:45.775+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:47:45.801+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:47:45.800+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-17T17:47:45.852+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.222 seconds
[2025-06-17T17:48:16.122+0000] {processor.py:157} INFO - Started process (PID=329) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:48:16.124+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-17T17:48:16.126+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:48:16.125+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:48:16.167+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:48:16.212+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:48:16.211+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:48:16.233+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:48:16.233+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-17T17:48:16.266+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.149 seconds
[2025-06-17T17:48:46.930+0000] {processor.py:157} INFO - Started process (PID=350) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:48:46.934+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-17T17:48:46.937+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:48:46.936+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:48:46.984+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:48:47.040+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:48:47.039+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:48:47.068+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:48:47.067+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-17T17:48:47.108+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.190 seconds
[2025-06-17T17:49:17.782+0000] {processor.py:157} INFO - Started process (PID=373) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:49:17.783+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-17T17:49:17.785+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:49:17.785+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:49:17.833+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:49:17.886+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:49:17.885+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:49:17.909+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:49:17.909+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-17T17:49:17.947+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.171 seconds
[2025-06-17T17:49:48.585+0000] {processor.py:157} INFO - Started process (PID=396) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:49:48.588+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-17T17:49:48.590+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:49:48.590+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:49:48.641+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:49:48.694+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:49:48.693+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:49:48.716+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:49:48.715+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-17T17:49:48.751+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.173 seconds
[2025-06-17T17:50:19.590+0000] {processor.py:157} INFO - Started process (PID=419) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:50:19.593+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-17T17:50:19.595+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:50:19.594+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:50:19.647+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:50:19.695+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:50:19.694+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:50:19.717+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:50:19.717+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-17T17:50:19.750+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.166 seconds
[2025-06-17T17:50:50.432+0000] {processor.py:157} INFO - Started process (PID=444) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:50:50.434+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-17T17:50:50.435+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:50:50.435+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:50:50.531+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:50:50.586+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:50:50.585+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:50:50.605+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:50:50.605+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-17T17:50:50.660+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.232 seconds
[2025-06-17T17:51:21.053+0000] {processor.py:157} INFO - Started process (PID=467) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:51:21.056+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-17T17:51:21.058+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:51:21.057+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:51:21.118+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:51:21.172+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:51:21.171+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:51:21.192+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:51:21.191+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-17T17:51:21.227+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.179 seconds
[2025-06-17T17:51:52.149+0000] {processor.py:157} INFO - Started process (PID=482) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:51:52.152+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-17T17:51:52.154+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:51:52.154+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:51:52.216+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:51:52.267+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:51:52.266+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:51:52.287+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:51:52.287+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-17T17:51:52.320+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.179 seconds
[2025-06-17T17:52:22.725+0000] {processor.py:157} INFO - Started process (PID=505) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:52:22.727+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-17T17:52:22.731+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:52:22.731+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:52:22.788+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:52:22.836+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:52:22.835+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:52:22.858+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:52:22.857+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-17T17:52:22.890+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.172 seconds
[2025-06-17T17:52:53.278+0000] {processor.py:157} INFO - Started process (PID=528) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:52:53.281+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-17T17:52:53.284+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:52:53.282+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:52:53.372+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:52:53.474+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:52:53.472+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:52:53.530+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:52:53.529+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-17T17:52:53.604+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.331 seconds
[2025-06-17T17:53:24.670+0000] {processor.py:157} INFO - Started process (PID=551) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:53:24.675+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-17T17:53:24.678+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:53:24.677+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:53:24.752+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:53:24.818+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:53:24.818+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:53:24.845+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:53:24.844+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-17T17:53:24.886+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.222 seconds
[2025-06-17T17:53:55.757+0000] {processor.py:157} INFO - Started process (PID=575) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:53:55.760+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-17T17:53:55.762+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:53:55.761+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:53:55.820+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:53:55.878+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:53:55.878+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:53:55.902+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:53:55.901+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-17T17:53:55.938+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.186 seconds
[2025-06-17T17:54:26.116+0000] {processor.py:157} INFO - Started process (PID=598) to work on /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:54:26.122+0000] {processor.py:829} INFO - Processing file /opt/airflow/dags/database_cleanup.py for tasks to queue
[2025-06-17T17:54:26.124+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:54:26.123+0000] {dagbag.py:539} INFO - Filling up the DagBag from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:54:26.177+0000] {processor.py:839} INFO - DAG(s) dict_keys(['database_cleanup']) retrieved from /opt/airflow/dags/database_cleanup.py
[2025-06-17T17:54:26.244+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:54:26.243+0000] {dag.py:2907} INFO - Sync 1 DAGs
[2025-06-17T17:54:26.269+0000] {logging_mixin.py:151} INFO - [2025-06-17T17:54:26.269+0000] {dag.py:3677} INFO - Setting next_dagrun for database_cleanup to None, run_after=None
[2025-06-17T17:54:26.306+0000] {processor.py:179} INFO - Processing /opt/airflow/dags/database_cleanup.py took 0.197 seconds
