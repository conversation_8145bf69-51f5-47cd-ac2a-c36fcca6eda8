// Environment Test Utility
// Use this to test if environment variables are working correctly

import { env, validateEnv, getAllEnvVars } from '../config/env.js'

/**
 * Test environment configuration
 */
export const testEnvironment = () => {
  console.log('🧪 Testing Environment Configuration...')
  
  // Test 1: Check if env object is available
  console.log('📋 Test 1: Environment object availability')
  console.log('  env object:', env)
  console.log('  env.API_BASE_URL:', env.API_BASE_URL)
  console.log('  env.NODE_ENV:', env.NODE_ENV)
  console.log('  env.DEBUG:', env.DEBUG)
  
  // Test 2: Check import.meta.env
  console.log('📋 Test 2: Vite import.meta.env')
  if (typeof import.meta !== 'undefined' && import.meta.env) {
    console.log('  import.meta.env available:', true)
    console.log('  VITE_API_BASE_URL:', import.meta.env.VITE_API_BASE_URL)
    console.log('  NODE_ENV:', import.meta.env.NODE_ENV)
    console.log('  MODE:', import.meta.env.MODE)
    console.log('  DEV:', import.meta.env.DEV)
    console.log('  PROD:', import.meta.env.PROD)
  } else {
    console.log('  import.meta.env available:', false)
  }
  
  // Test 3: Validate environment
  console.log('📋 Test 3: Environment validation')
  const isValid = validateEnv()
  console.log('  Validation result:', isValid)
  
  // Test 4: Get all environment variables
  console.log('📋 Test 4: All environment variables')
  const allVars = getAllEnvVars()
  console.log('  All variables:', allVars)
  
  // Test 5: API URL test
  console.log('📋 Test 5: API URL test')
  const apiUrl = env.API_BASE_URL
  console.log('  API URL:', apiUrl)
  console.log('  Is localhost:', apiUrl.includes('localhost'))
  console.log('  Is production:', apiUrl.includes('onrender.com'))
  
  console.log('✅ Environment test completed!')
  
  return {
    env,
    isValid,
    allVars,
    apiUrl,
    importMetaEnv: typeof import.meta !== 'undefined' ? import.meta.env : null
  }
}

/**
 * Quick environment check for debugging
 */
export const quickEnvCheck = () => {
  return {
    API_BASE_URL: env.API_BASE_URL,
    NODE_ENV: env.NODE_ENV,
    DEBUG: env.DEBUG,
    hasImportMeta: typeof import.meta !== 'undefined',
    hasImportMetaEnv: typeof import.meta !== 'undefined' && !!import.meta.env,
    viteApiBaseUrl: typeof import.meta !== 'undefined' && import.meta.env ? import.meta.env.VITE_API_BASE_URL : 'not available'
  }
}

// Auto-run test in development
if (env.DEBUG) {
  console.log('🔧 Auto-running environment test in debug mode...')
  testEnvironment()
}
