#!/usr/bin/env python3
"""
BioShort - AI-Powered Biography Video Generator
Main Flask application following the plan.md specifications
"""

import os
import json
import tempfile
import shutil
from datetime import datetime
from flask import Flask, request, jsonify, send_file
from flask_cors import CORS

# Import environment configuration
from config.env import env

# Import our services
from services.story_generator import StoryGenerator
from services.image_generator import ImageGenerator
from services.voice_generator import VoiceGenerator
from services.video_compiler import VideoCompiler
from services.youtube_uploader import YouTubeUploader
from database import Database
from auth_middleware import require_auth, optional_auth

# Initialize Flask app
app = Flask(__name__)

# Configure CORS based on environment
if env.FLASK_ENV == 'development':
    # Allow all origins in development
    CORS(app, origins="*")
else:
    # Restrict to specific origins in production
    CORS(app, origins=[env.FRONTEND_URL])

# Configuration
app.config['MAX_CONTENT_LENGTH'] = 256 * 1024 * 1024  # 256MB max file size for video uploads

# Directories
TEMP_DIR = os.path.join(os.path.dirname(__file__), 'temp')
OUTPUT_DIR = os.path.join(os.path.dirname(__file__), 'output')

# Ensure directories exist
os.makedirs(TEMP_DIR, exist_ok=True)
os.makedirs(OUTPUT_DIR, exist_ok=True)

# Initialize services
story_generator = StoryGenerator()
image_generator = ImageGenerator()
voice_generator = VoiceGenerator()
video_compiler = VideoCompiler()

# Initialize database
db = Database()

# Initialize YouTube uploader (only if credentials are available)
youtube_uploader = None
try:
    youtube_uploader = YouTubeUploader()
    print("✅ YouTube uploader initialized")
except Exception as e:
    print(f"⚠️ YouTube uploader not available: {str(e)}")
    print("   YouTube upload functionality will be disabled")

print("🚀 BioShort API server starting...")
print(f"📁 Temp directory: {TEMP_DIR}")
print("📁 Files stored in temp directory")

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'message': 'BioShort API is running!',
        'timestamp': datetime.now().isoformat(),
        'services': {
            'story_generator': 'ready',
            'image_generator': 'ready',
            'voice_generator': 'ready',
            'video_compiler': 'ready'
        }
    })

@app.route('/api/keep-alive', methods=['GET'])
def keep_alive():
    """Keep-alive endpoint to prevent service from sleeping (for Render)"""
    return jsonify({
        'status': 'alive',
        'timestamp': datetime.now().isoformat(),
        'message': 'Service is active and running',
        'uptime': 'active'
    })

@app.route('/api/airflow-status', methods=['GET'])
def airflow_status():
    """Check if Airflow is running"""
    try:
        import requests

        # Try to connect to Airflow webserver
        airflow_response = requests.get('http://localhost:8080/health', timeout=5)
        airflow_running = airflow_response.status_code == 200

        # Try to check scheduler status
        scheduler_response = requests.get('http://localhost:8080/api/v1/health', timeout=5)
        scheduler_running = scheduler_response.status_code == 200

        return jsonify({
            'airflow_webserver': 'running' if airflow_running else 'not running',
            'airflow_scheduler': 'running' if scheduler_running else 'not running',
            'airflow_url': 'http://localhost:8080',
            'status': 'healthy' if (airflow_running and scheduler_running) else 'partial',
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            'airflow_webserver': 'not running',
            'airflow_scheduler': 'not running',
            'error': str(e),
            'status': 'unhealthy',
            'timestamp': datetime.now().isoformat()
        })

@app.route('/api/generate-video-progressive', methods=['POST'])
@require_auth
def generate_video_progressive():
    """
    Progressive video generation endpoint that returns data as it's generated
    """
    try:
        # Step 1: Accept user input
        data = request.get_json()
        if not data or 'person_name' not in data:
            return jsonify({'success': False, 'error': 'Person name is required'}), 400

        person_name = data['person_name'].strip()
        voice_type = data.get('voice_type', 'hindi_male')
        language = data.get('language', 'hindi')

        if not person_name:
            return jsonify({'success': False, 'error': 'Person name cannot be empty'}), 400

        print(f"🎬 Starting progressive generation for: {person_name}")

        # Return initial response with session info
        return jsonify({
            'success': True,
            'message': 'Progressive generation started',
            'person_name': person_name,
            'language': language,
            'voice_type': voice_type,
            'session_id': f"{person_name}_{datetime.now().timestamp()}"
        })

    except Exception as e:
        print(f"❌ Error in progressive generation: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Internal server error: {str(e)}'
        }), 500

@app.route('/api/generate-video', methods=['POST'])
@require_auth
def generate_video():
    """
    Main endpoint to generate video from person name
    Following the 8-step workflow from plan.md
    """
    try:
        # Step 1: Accept user input
        data = request.get_json()
        if not data or 'person_name' not in data:
            return jsonify({'success': False, 'error': 'Person name is required'}), 400

        person_name = data['person_name'].strip()
        voice_type = data.get('voice_type', 'hindi_male')  # Default to Hindi male
        language = data.get('language', 'hindi')  # Default to Hindi

        if not person_name:
            return jsonify({'success': False, 'error': 'Person name cannot be empty'}), 400

        print(f"🎬 Generating video for: {person_name}")

        # Check if pre-generated content is provided
        pregenerated_story = data.get('pregenerated_story')
        pregenerated_images = data.get('pregenerated_images')

        if pregenerated_story and pregenerated_images:
            print("🔄 Using pre-generated story and images (avoiding regeneration)")
            story = pregenerated_story
            segments = story['segments']
            print(f"✅ Using {len(segments)} pre-generated story segments")

            # Convert pre-generated images to the expected format
            image_results = []
            for img_data in pregenerated_images:
                image_results.append({
                    'success': True,
                    'filename': img_data['filename'],
                    'image_base64': img_data['base64']
                })
            print(f"✅ Using {len(image_results)} pre-generated images")
        else:
            # Step 2: Generate short story using Gemini Pro
            print("📝 Step 2: Generating story...")
            story_result = story_generator.generate_story(person_name)
            if not story_result['success']:
                return jsonify({'success': False, 'error': f"Story generation failed: {story_result['error']}"}), 500

            story = story_result['story']
            segments = story['segments']
            print(f"✅ Generated {len(segments)} story segments")
        
        # Step 3: Split story into segments (already done in step 2)
        print("📋 Step 3: Story segments ready")

        # Step 4: Generate or use pre-generated images
        if not pregenerated_images:
            # Generate animated images for each segment (in memory)
            print("🎨 Step 4: Generating images...")
            image_results = []

            successful_images = []  # Track successful images for reuse

            for i, segment in enumerate(segments):
                print(f"🖼️ Generating image {i+1}/{len(segments)}: {segment['scene_description'][:50]}...")

                # Try to generate image with reduced retry for speed
                image_result = None
                max_retries = 2  # Reduced from 3 to 2 for faster generation

                for attempt in range(max_retries):
                    if attempt == 0:
                        # First attempt: Use original description
                        prompt = segment['scene_description']
                        print(f"🎯 Attempt {attempt+1}: Original prompt")
                    elif attempt == 1:
                        # Second attempt: More detailed description
                        prompt = f"Professional portrait photograph of {person_name}, realistic, high quality, detailed face, photorealistic"
                        print(f"🎯 Attempt {attempt+1}: Detailed portrait prompt")
                    elif attempt == 2:
                        # Third attempt: Simple portrait
                        prompt = f"Portrait of {person_name}, realistic photo, clear face"
                        print(f"🎯 Attempt {attempt+1}: Simple portrait prompt")
                    else:
                        # Fourth attempt: Very basic
                        prompt = f"{person_name} headshot photo"
                        print(f"🎯 Attempt {attempt+1}: Basic headshot prompt")

                    print(f"   Using prompt: {prompt}")

                    try:
                        image_result = image_generator.generate_image(
                            prompt,
                            person_name,
                            segment_index=i
                        )

                        if image_result and image_result.get('success'):
                            print(f"✅ Image {i+1} generated on attempt {attempt+1}: {image_result['filename']}")
                            break
                        else:
                            error_msg = image_result.get('error', 'Unknown error') if image_result else 'No result returned'
                            print(f"⚠️ Attempt {attempt+1} failed: {error_msg}")
                            if attempt < max_retries - 1:
                                print(f"🔄 Retrying with different prompt...")
                                # Add a small delay between attempts
                                import time
                                time.sleep(2)
                    except Exception as img_gen_error:
                        print(f"⚠️ Image generation exception on attempt {attempt+1}: {str(img_gen_error)}")
                        image_result = {'success': False, 'error': str(img_gen_error)}
                        if attempt < max_retries - 1:
                            print(f"🔄 Retrying after exception...")
                            import time
                            time.sleep(2)

                # Check if we got a successful image
                if image_result and image_result['success']:
                    image_results.append(image_result)
                    successful_images.append(image_result)  # Store for potential reuse
                    print(f"✅ Image {i+1} successfully added to results")
                else:
                    print(f"❌ All {max_retries} attempts failed for image {i+1}")

                    # Smart fallback strategy - reuse other scene images
                    fallback_used = False

                    # Try to reuse a successful image from another scene
                    if successful_images:
                        # Use a random successful image to add variety
                        import random
                        reused_image = random.choice(successful_images).copy()  # Random selection for variety
                        reused_image['filename'] = f"{person_name.replace(' ', '_').lower()}_reused_scene_{i+1}_{int(os.path.getmtime(__file__) * 1000) if os.path.exists(__file__) else 1000}.png"
                        image_results.append(reused_image)
                        print(f"🔄 Reused successful scene image for scene {i+1}: {reused_image['filename']}")
                        fallback_used = True

                    # If no successful images yet, we'll wait and try to get at least one image
                    if not fallback_used:
                        print(f"⚠️ No successful images to reuse yet for scene {i+1}")
                        # For now, add a placeholder that we'll replace later if we get successful images
                        placeholder = {
                            'success': False,
                            'filename': f"{person_name.replace(' ', '_').lower()}_placeholder_{i}.png",
                            'needs_replacement': True,
                            'scene_index': i
                        }
                        image_results.append(placeholder)
            # Post-process: Replace any placeholders with successful images
            print("🔄 Post-processing: Replacing placeholders with successful images...")
            for i, img_result in enumerate(image_results):
                if isinstance(img_result, dict) and img_result.get('needs_replacement', False):
                    if successful_images:
                        # Replace placeholder with a random successful image
                        import random
                        replacement_image = random.choice(successful_images).copy()
                        replacement_image['filename'] = f"{person_name.replace(' ', '_').lower()}_replacement_scene_{i+1}_{int(os.path.getmtime(__file__) * 1000) if os.path.exists(__file__) else 1000}.png"
                        image_results[i] = replacement_image
                        print(f"🔄 Replaced placeholder for scene {i+1} with successful scene image")
                    else:
                        print(f"❌ No successful images available to replace placeholder for scene {i+1}")
                        # This should rarely happen, but if it does, we'll need a basic fallback
                        # For now, we'll leave the placeholder and handle it in video compilation

            print(f"✅ Final image count: {len([img for img in image_results if img.get('success', True)])} images ready")
        else:
            print("🔄 Step 4: Using pre-generated images (skipping regeneration)")
        
        # Step 5: Generate voice narration and prepare subtitle text
        print(f"🎤 Step 5: Generating voice narration ({voice_type}, {language})...")

        # Debug: Print segments to see what we have
        print(f"📝 Debug: Found {len(segments)} segments")
        for i, seg in enumerate(segments):
            print(f"  Segment {i+1}: {seg.get('text', 'NO TEXT')[:100]}...")

        full_script = " ".join([seg['text'] for seg in segments])
        print(f"📝 Debug: Full script length: {len(full_script)} characters")
        print(f"📝 Debug: Full script preview: {full_script[:200]}...")

        # No subtitle processing needed - videos will have audio narration only

        voice_result = voice_generator.generate_voice(full_script, person_name, voice_type, language)
        if not voice_result['success']:
            return jsonify({'success': False, 'error': f"Voice generation failed: {voice_result['error']}"}), 500

        print(f"✅ Voice generated: {voice_result['filename']} (in memory)")

        # Step 6: Combine images + audio into video using in-memory data
        print("🎥 Step 6: Compiling video...")
        video_result = video_compiler.compile_video(
            image_results=image_results,
            audio_result=voice_result,
            person_name=person_name
        )
        
        if not video_result['success']:
            return jsonify({'success': False, 'error': f"Video compilation failed: {video_result['error']}"}), 500

        video_filename = video_result['filename']
        print(f"✅ Video compiled: {video_filename}")

        # Step 7: Return response with media links
        print("📤 Step 7: Preparing response...")

        response_data = {
            'success': True,
            'message': f'Video generated successfully for {person_name}',
            'person_name': person_name,
            'language': language,
            'voice_type': voice_type,
            'story': story,
            'images': [{'filename': img['filename'], 'base64': img['image_base64']} for img in image_results],
            'audio': {'filename': voice_result['filename'], 'base64': voice_result['audio_base64']},
            'video': {'filename': video_filename, 'base64': video_result['video_base64']},
            'generation_time': datetime.now().isoformat(),
            'stats': {
                'segments_count': len(segments),
                'images_count': len(image_results),
                'audio_duration': voice_result.get('duration', 'unknown'),
                'video_duration': video_result.get('duration', 'unknown'),
                'video_size': video_result.get('size', 0)
            },
            'note': 'All media (images, audio, video) are served directly from memory. No files are stored on disk.'
        }
        
        print(f"🎉 Video generation completed for {person_name}")
        return jsonify(response_data)
        
    except Exception as e:
        import traceback
        print(f"❌ Error in generate_video: {str(e)}")
        print(f"❌ Full traceback:")
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': f'Internal server error: {str(e)}'
        }), 500

@app.route('/api/download-video/<filename>', methods=['GET'])
def download_video(filename):
    """Download generated video file (from temp directory)"""
    try:
        file_path = os.path.join(TEMP_DIR, filename)
        if not os.path.exists(file_path):
            return jsonify({'error': 'Video file not found'}), 404

        return send_file(
            file_path,
            as_attachment=True,
            download_name=filename,
            mimetype='video/mp4'
        )
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/download-audio/<filename>', methods=['GET'])
def download_audio(filename):
    """Download generated audio file (from temp directory)"""
    try:
        # Check temp directory first (for audio files and voice samples)
        temp_path = os.path.join(TEMP_DIR, filename)
        if os.path.exists(temp_path):
            return send_file(
                temp_path,
                as_attachment=True,
                download_name=filename,
                mimetype='audio/mpeg'
            )

        # No fallback needed - all files in temp directory

        return jsonify({'error': 'Audio file not found'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/preview-image/<filename>', methods=['GET'])
def preview_image(filename):
    """Preview generated image (from temp directory)"""
    try:
        # Check temp directory first (for images)
        temp_path = os.path.join(TEMP_DIR, filename)
        if os.path.exists(temp_path):
            return send_file(temp_path, mimetype='image/png')

        # No fallback needed - all files in temp directory

        return jsonify({'error': 'Image file not found'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/voices', methods=['GET'])
def get_voices():
    """Get available voice options"""
    try:
        voices = {
            'hindi_male': {
                'name': 'Hindi Male Voice',
                'language': 'Hindi',
                'gender': 'Male',
                'description': 'Natural Hindi male voice'
            },
            'hindi_female': {
                'name': 'Hindi Female Voice',
                'language': 'Hindi',
                'gender': 'Female',
                'description': 'Natural Hindi female voice'
            },
            'english_male': {
                'name': 'English Male Voice',
                'language': 'English',
                'gender': 'Male',
                'description': 'Natural English male voice'
            },
            'english_female': {
                'name': 'English Female Voice',
                'language': 'English',
                'gender': 'Female',
                'description': 'Natural English female voice'
            }
        }

        return jsonify({
            'success': True,
            'voices': voices,
            'default': 'hindi_male'
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/voice-sample', methods=['POST'])
def generate_voice_sample():
    """Generate a voice sample for preview (served from memory)"""
    try:
        data = request.get_json()
        voice_type = data.get('voice_type', 'hindi_male')
        language = data.get('language', 'hindi')

        # Sample text for voice preview
        sample_texts = {
            'hindi': "नमस्ते! यह आपकी चुनी गई आवाज़ का नमूना है। यह आवाज़ आपके वीडियो में कहानी सुनाएगी।",
            'english': "Hello! This is a sample of your selected voice. This voice will narrate your video story."
        }

        sample_text = sample_texts.get(language, sample_texts['english'])

        print(f"🎤 Voice sample request: {voice_type} ({language})")

        # Generate voice sample
        result = voice_generator.generate_voice_sample(
            text=sample_text,
            voice_type=voice_type,
            language=language
        )

        if result['success']:
            return jsonify({
                'success': True,
                'filename': result['filename'],
                'audio_base64': result.get('audio_base64'),  # Include base64 audio data
                'voice_type': voice_type,
                'language': language,
                'duration': result.get('duration', 3.0),
                'type': result.get('type', 'sample'),
                'message': 'Voice sample generated successfully (in memory)'
            })
        else:
            return jsonify({
                'success': False,
                'error': result.get('error', 'Voice sample generation failed')
            }), 500

    except Exception as e:
        print(f"❌ Voice sample generation error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/generate-random-names', methods=['GET'])
def generate_random_names():
    """Generate random famous person names using AI"""
    try:
        print("🎲 Generating random famous person names...")

        # Use the story generator's Gemini API to generate random names
        result = story_generator.generate_random_names()

        if result['success']:
            return jsonify({
                'success': True,
                'names': result['names'],
                'generation_time': datetime.now().isoformat(),
                'count': len(result['names'])
            })
        else:
            return jsonify({
                'success': False,
                'error': result.get('error', 'Failed to generate random names')
            }), 500

    except Exception as e:
        print(f"❌ Random names generation error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/generate-youtube-content', methods=['POST'])
def generate_youtube_content():
    """Generate YouTube titles and description for a person"""
    try:
        data = request.get_json()
        if not data or 'person_name' not in data:
            return jsonify({'success': False, 'error': 'Person name is required'}), 400

        person_name = data['person_name'].strip()
        language = data.get('language', 'english')  # Default to English
        story_data = data.get('story_data', None)  # Get story data if provided

        if not person_name:
            return jsonify({'success': False, 'error': 'Person name cannot be empty'}), 400

        print(f"📺 Generating YouTube content for: {person_name} in {language}")

        # Always use provided story data, don't regenerate
        if story_data:
            print("📝 Using provided story data for YouTube content generation")
        else:
            print("⚠️ No story data provided - YouTube content will be generic")

        # Generate YouTube content using Gemini AI with actual story content
        result = story_generator.generate_youtube_content(person_name, language, story_data)

        if result['success']:
            return jsonify({
                'success': True,
                'person_name': person_name,
                'language': language,
                'titles': result['titles'],
                'description': result['description'],
                'source': result.get('source', 'ai'),
                'generation_time': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'error': result.get('error', 'Failed to generate YouTube content')
            }), 500

    except Exception as e:
        print(f"❌ YouTube content generation error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/generate-story', methods=['POST'])
def generate_story_only():
    """Generate story for a person"""
    try:
        data = request.get_json()
        if not data or 'person_name' not in data:
            return jsonify({'success': False, 'error': 'Person name is required'}), 400

        person_name = data['person_name'].strip()
        if not person_name:
            return jsonify({'success': False, 'error': 'Person name cannot be empty'}), 400

        print(f"📝 Generating story for: {person_name}")
        story_result = story_generator.generate_story(person_name)

        if story_result['success']:
            return jsonify({
                'success': True,
                'person_name': person_name,
                'story': story_result['story'],
                'generation_time': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'error': story_result.get('error', 'Story generation failed')
            }), 500

    except Exception as e:
        print(f"❌ Story generation error: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/generate-images', methods=['POST'])
def generate_images_only():
    """Generate images for story segments"""
    try:
        data = request.get_json()
        if not data or 'person_name' not in data or 'segments' not in data:
            return jsonify({'success': False, 'error': 'Person name and segments are required'}), 400

        person_name = data['person_name'].strip()
        segments = data['segments']

        print(f"🎨 Generating images for: {person_name}")
        image_results = []
        successful_images = []

        for i, segment in enumerate(segments):
            print(f"🖼️ Generating image {i+1}/{len(segments)}")

            image_result = image_generator.generate_image(
                segment['scene_description'],
                person_name,
                segment_index=i
            )

            if image_result['success']:
                image_results.append(image_result)
                successful_images.append(image_result)
            else:
                if successful_images:
                    reused_image = successful_images[-1]
                    image_results.append(reused_image)
                else:
                    fallback_result = image_generator.create_fallback_image(
                        segment['scene_description'],
                        person_name,
                        segment_index=i
                    )
                    image_results.append(fallback_result)

        return jsonify({
            'success': True,
            'person_name': person_name,
            'images': [{'filename': img['filename'], 'base64': img['image_base64']} for img in image_results],
            'generation_time': datetime.now().isoformat(),
            'count': len(image_results)
        })

    except Exception as e:
        print(f"❌ Images generation error: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/upload-to-youtube', methods=['POST'])
def upload_to_youtube():
    """
    Upload generated video to YouTube
    """
    try:
        # Check if YouTube uploader is available
        if not youtube_uploader:
            return jsonify({
                'success': False,
                'error': 'YouTube upload service is not available. Please check server configuration.'
            }), 503

        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        print(f"🔍 Received upload request data keys: {list(data.keys())}")
        print(f"🔍 Title received: '{data.get('title', 'NOT_PROVIDED')}'")
        print(f"🔍 Description received: '{data.get('description', 'NOT_PROVIDED')}'")

        # Validate required fields - now we only need metadata, not the video data
        required_fields = ['title', 'description']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'error': f'Missing required field: {field}'
                }), 400

            # Check if field is empty or just whitespace
            if not data[field] or not str(data[field]).strip():
                return jsonify({
                    'success': False,
                    'error': f'Field "{field}" cannot be empty'
                }), 400

        # Get video data from request (if provided) or use session storage
        video_base64 = data.get('video_base64')
        if not video_base64:
            return jsonify({
                'success': False,
                'error': 'No video data provided. Please generate a video first.'
            }), 400

        title = data['title'].strip()
        description = data['description'].strip()
        privacy_status = data.get('privacy_status', 'private')  # Default to private
        tags = data.get('tags', [])

        # Validate privacy status
        valid_privacy_statuses = ['private', 'public', 'unlisted']
        if privacy_status not in valid_privacy_statuses:
            return jsonify({
                'success': False,
                'error': f'Invalid privacy status. Must be one of: {", ".join(valid_privacy_statuses)}'
            }), 400

        print(f"🎬 YouTube upload request:")
        print(f"   Title: {title}")
        print(f"   Privacy: {privacy_status}")
        print(f"   Tags: {tags}")
        print(f"   Video data size: {len(video_base64)} characters")

        # Check video size before processing
        try:
            import base64
            video_data = base64.b64decode(video_base64)
            video_size_mb = len(video_data) / (1024 * 1024)
            print(f"   Video size: {video_size_mb:.2f} MB")

            # YouTube allows up to 256GB, but let's set a reasonable limit
            if video_size_mb > 100:  # 100MB limit
                return jsonify({
                    'success': False,
                    'error': f'Video file too large ({video_size_mb:.2f} MB). Maximum allowed: 100 MB'
                }), 413

        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'Invalid video data: {str(e)}'
            }), 400

        # Upload video to YouTube
        upload_result = youtube_uploader.upload_video_from_base64(
            video_base64=video_base64,
            title=title,
            description=description,
            privacy_status=privacy_status,
            tags=tags
        )

        if upload_result['success']:
            print(f"✅ YouTube upload successful: {upload_result['video_url']}")
            return jsonify({
                'success': True,
                'message': 'Video uploaded to YouTube successfully!',
                'video_id': upload_result['video_id'],
                'video_url': upload_result['video_url'],
                'title': upload_result['title'],
                'privacy_status': upload_result['privacy_status'],
                'upload_time': datetime.now().isoformat()
            })
        else:
            print(f"❌ YouTube upload failed: {upload_result['error']}")
            return jsonify({
                'success': False,
                'error': upload_result['error'],
                'error_code': upload_result.get('error_code')
            }), 500

    except Exception as e:
        print(f"❌ YouTube upload endpoint error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/youtube-quota-info', methods=['GET'])
def get_youtube_quota_info():
    """
    Get YouTube upload quota and limits information
    """
    try:
        if not youtube_uploader:
            return jsonify({
                'success': False,
                'error': 'YouTube upload service is not available'
            }), 503

        quota_info = youtube_uploader.get_upload_quota_info()
        return jsonify({
            'success': True,
            'quota_info': quota_info,
            'service_available': True
        })

    except Exception as e:
        print(f"❌ YouTube quota info error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# Queue Management APIs for Airflow Automation

@app.route('/api/queue/add', methods=['POST'])
@require_auth
def add_to_queue():
    """Add a person to the automated processing queue"""
    try:
        data = request.get_json()
        if not data or 'name' not in data:
            return jsonify({'success': False, 'error': 'Person name is required'}), 400

        name = data['name'].strip()
        title_language = data.get('title_language', 'english')
        description_language = data.get('description_language', 'english')
        audio_language = data.get('audio_language', 'hindi')
        voice_type = data.get('voice_type', 'hindi_male')

        if not name:
            return jsonify({'success': False, 'error': 'Person name cannot be empty'}), 400

        # Validate language options
        valid_languages = ['english', 'hindi']
        valid_voice_types = ['hindi_male', 'hindi_female', 'english_male', 'english_female']

        if title_language not in valid_languages or description_language not in valid_languages:
            return jsonify({'success': False, 'error': 'Invalid language option'}), 400

        if audio_language not in valid_languages or voice_type not in valid_voice_types:
            return jsonify({'success': False, 'error': 'Invalid audio language or voice type'}), 400

        success = db.add_person_to_queue(name, title_language, description_language, audio_language, voice_type)

        if success:
            return jsonify({
                'success': True,
                'message': f'{name} added to processing queue',
                'name': name,
                'title_language': title_language,
                'description_language': description_language,
                'audio_language': audio_language,
                'voice_type': voice_type
            })
        else:
            return jsonify({
                'success': False,
                'error': f'{name} is already in the queue'
            }), 400

    except Exception as e:
        print(f"❌ Error adding to queue: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/queue', methods=['GET'])
def get_queue():
    """Get the current processing queue"""
    try:
        queue = db.get_queue()
        return jsonify({
            'success': True,
            'queue': queue,
            'count': len(queue)
        })
    except Exception as e:
        print(f"❌ Error getting queue: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/queue/remove/<int:person_id>', methods=['DELETE'])
def remove_from_queue(person_id):
    """Remove a person from the queue"""
    try:
        success = db.remove_person_from_queue(person_id)

        if success:
            return jsonify({
                'success': True,
                'message': 'Person removed from queue'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to remove person from queue'
            }), 400

    except Exception as e:
        print(f"❌ Error removing from queue: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/queue/next', methods=['GET'])
def get_next_person():
    """Get the next person to process (for Airflow)"""
    try:
        person = db.get_next_pending_person()

        if person:
            return jsonify({
                'success': True,
                'person': person
            })
        else:
            return jsonify({
                'success': True,
                'person': None,
                'message': 'No pending people in queue'
            })

    except Exception as e:
        print(f"❌ Error getting next person: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/queue/update-status', methods=['POST'])
def update_person_status():
    """Update person's processing status (for Airflow)"""
    try:
        data = request.get_json()
        if not data or 'person_id' not in data or 'status' not in data:
            return jsonify({'success': False, 'error': 'person_id and status are required'}), 400

        person_id = data['person_id']
        status = data['status']
        video_url = data.get('video_url')
        error_message = data.get('error_message')

        db.update_person_status(person_id, status, video_url, error_message)

        return jsonify({
            'success': True,
            'message': f'Status updated to {status}'
        })

    except Exception as e:
        print(f"❌ Error updating status: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/queue/logs', methods=['GET'])
def get_processing_logs():
    """Get processing logs"""
    try:
        limit = request.args.get('limit', 50, type=int)
        logs = db.get_processing_logs(limit)

        return jsonify({
            'success': True,
            'logs': logs,
            'count': len(logs)
        })

    except Exception as e:
        print(f"❌ Error getting logs: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/airflow')
@app.route('/airflow/')
@app.route('/airflow/<path:path>')
def airflow_proxy(path=''):
    """Proxy requests to Airflow web UI"""
    try:
        import requests

        # Airflow runs on localhost:8080 inside the container
        airflow_url = f"http://localhost:8080/{path}"

        # Forward query parameters
        if request.query_string:
            airflow_url += f"?{request.query_string.decode()}"

        # Make request to Airflow
        response = requests.get(
            airflow_url,
            headers={k: v for k, v in request.headers if k.lower() != 'host'},
            timeout=30
        )

        # Return Airflow response
        from flask import Response
        return Response(
            response.content,
            status=response.status_code,
            headers=dict(response.headers)
        )

    except Exception as e:
        return f"Airflow not available: {str(e)}", 503

@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    print("🌟 BioShort API server ready!")
    print("📖 API Documentation:")
    print("  GET  /api/health - Health check")
    print("  POST /api/generate-video - Generate video from person name")
    print("  GET  /api/download-video/<filename> - Download video")
    print("  GET  /api/download-audio/<filename> - Download audio")
    print("  GET  /api/preview-image/<filename> - Preview image")
    print("  GET  /api/generate-random-names - Generate AI-powered random names")
    print("  POST /api/generate-youtube-content - Generate YouTube titles & description")
    print("  GET  /api/voices - Get available voice options")
    print("  POST /api/voice-sample - Generate voice sample for preview")
    print("  POST /api/upload-to-youtube - Upload video to YouTube")
    print("  GET  /api/youtube-quota-info - Get YouTube upload quota info")
    print()

    app.run(
        host='0.0.0.0',
        port=5000,
        debug=env.FLASK_DEBUG,
        use_reloader=True,  # Enable auto-reload on file changes
        use_debugger=True,  # Enable debugger
        threaded=True       # Enable threading for better performance
    )
