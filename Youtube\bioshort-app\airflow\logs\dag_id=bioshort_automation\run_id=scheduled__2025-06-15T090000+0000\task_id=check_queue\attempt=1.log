[2025-06-16T19:12:35.265+0000] {taskinstance.py:1159} INFO - Dependencies all met for dep_context=non-requeueable deps ti=<TaskInstance: bioshort_automation.check_queue scheduled__2025-06-15T09:00:00+00:00 [queued]>
[2025-06-16T19:12:35.281+0000] {taskinstance.py:1159} INFO - Dependencies all met for dep_context=requeueable deps ti=<TaskInstance: bioshort_automation.check_queue scheduled__2025-06-15T09:00:00+00:00 [queued]>
[2025-06-16T19:12:35.282+0000] {taskinstance.py:1361} INFO - Starting attempt 1 of 2
[2025-06-16T19:12:35.300+0000] {taskinstance.py:1382} INFO - Executing <Task(PythonOperator): check_queue> on 2025-06-15 09:00:00+00:00
[2025-06-16T19:12:35.306+0000] {standard_task_runner.py:57} INFO - Started process 204 to run task
[2025-06-16T19:12:35.312+0000] {standard_task_runner.py:84} INFO - Running: ['***', 'tasks', 'run', 'bioshort_automation', 'check_queue', 'scheduled__2025-06-15T09:00:00+00:00', '--job-id', '15', '--raw', '--subdir', 'DAGS_FOLDER/bioshort_automation.py', '--cfg-path', '/tmp/tmp1gm9vbaf']
[2025-06-16T19:12:35.317+0000] {standard_task_runner.py:85} INFO - Job 15: Subtask check_queue
[2025-06-16T19:12:35.428+0000] {task_command.py:415} INFO - Running <TaskInstance: bioshort_automation.check_queue scheduled__2025-06-15T09:00:00+00:00 [running]> on host cdc7242b3dcf
[2025-06-16T19:12:35.554+0000] {taskinstance.py:1660} INFO - Exporting env vars: AIRFLOW_CTX_DAG_OWNER='bioshort' AIRFLOW_CTX_DAG_ID='bioshort_automation' AIRFLOW_CTX_TASK_ID='check_queue' AIRFLOW_CTX_EXECUTION_DATE='2025-06-15T09:00:00+00:00' AIRFLOW_CTX_TRY_NUMBER='1' AIRFLOW_CTX_DAG_RUN_ID='scheduled__2025-06-15T09:00:00+00:00'
[2025-06-16T19:12:35.557+0000] {logging_mixin.py:151} INFO - 🔍 Checking queue for next person to process...
[2025-06-16T19:12:35.574+0000] {logging_mixin.py:151} INFO - ℹ️ No pending people in queue. Skipping processing.
[2025-06-16T19:12:35.575+0000] {python.py:194} INFO - Done. Returned value was: no_processing_needed
[2025-06-16T19:12:35.613+0000] {taskinstance.py:1400} INFO - Marking task as SUCCESS. dag_id=bioshort_automation, task_id=check_queue, execution_date=20250615T090000, start_date=20250616T191235, end_date=20250616T191235
[2025-06-16T19:12:35.645+0000] {local_task_job_runner.py:228} INFO - Task exited with return code 0
[2025-06-16T19:12:35.683+0000] {taskinstance.py:2784} INFO - 2 downstream tasks scheduled from follow-on schedule check
